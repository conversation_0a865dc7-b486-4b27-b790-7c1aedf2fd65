// Admin authentication service with hardcoded credentials
import { supabaseService } from './supabaseClient';
import { config } from '../config/environment';

export interface AdminCredentials {
  login: string;
  password: string;
}

export interface AdminUser {
  id: string;
  login: string;
  role: 'admin';
  isAuthenticated: boolean;
  loginTime: string;
}

// Hardcoded admin credentials
const ADMIN_CREDENTIALS: AdminCredentials[] = [
  {
    login: 'admin',
    password: 'admin123'
  }
];

class AdminAuthService {
  private static instance: AdminAuthService;
  private currentAdmin: AdminUser | null = null;
  private sessionKey = 'admin_session';
  private loggedEvents = new Set<string>(); // Track logged events to prevent duplicates

  private constructor() {
    this.loadSession();
  }

  public static getInstance(): AdminAuthService {
    if (!AdminAuthService.instance) {
      AdminAuthService.instance = new AdminAuthService();
    }
    return AdminAuthService.instance;
  }

  // Prevent duplicate logging while allowing important admin actions
  private logOnce(eventKey: string, logFunction: () => void, forceLog = false): void {
    // Always log if forceLog is true (for important admin actions)
    if (forceLog || !this.loggedEvents.has(eventKey)) {
      logFunction();

      // Only track non-forced logs to prevent duplicates
      if (!forceLog) {
        this.loggedEvents.add(eventKey);

        // Clean up old events (keep only last 100)
        if (this.loggedEvents.size > 100) {
          const oldEvents = Array.from(this.loggedEvents).slice(0, 50);
          oldEvents.forEach(event => this.loggedEvents.delete(event));
        }
      }
    }
  }

  // Validate admin credentials
  public async validateCredentials(login: string, password: string): Promise<boolean> {
    // Simulate network delay for realistic UX
    await new Promise(resolve => setTimeout(resolve, 500));

    const validCredential = ADMIN_CREDENTIALS.find(
      cred => cred.login === login && cred.password === password
    );

    return !!validCredential;
  }

  // Admin login
  public async login(credentials: AdminCredentials): Promise<AdminUser> {
    const isValid = await this.validateCredentials(credentials.login, credentials.password);
    
    if (!isValid) {
      throw new Error('Неверный логин или пароль');
    }

    // Create admin session
    const adminUser: AdminUser = {
      id: `admin-${Date.now()}`,
      login: credentials.login,
      role: 'admin',
      isAuthenticated: true,
      loginTime: new Date().toISOString(),
    };

    this.currentAdmin = adminUser;
    this.saveSession(adminUser);

    // Track admin session for activity logs
    this.trackAdminSession(adminUser);

    // Always log admin login (important security event)
    this.logOnce(`admin_login_${credentials.login}_${Date.now()}`, () => {
      console.log(`Admin login successful: ${credentials.login} at ${adminUser.loginTime}`);
    }, true);

    return adminUser;
  }

  // Admin logout
  public async logout(): Promise<void> {
    if (this.currentAdmin) {
      // Always log admin logout (important security event)
      this.logOnce(`admin_logout_${this.currentAdmin.login}_${Date.now()}`, () => {
        console.log(`Admin logout: ${this.currentAdmin!.login} at ${new Date().toISOString()}`);
      }, true);
    }

    this.currentAdmin = null;
    this.clearSession();
  }

  // Check if admin is authenticated
  public isAuthenticated(): boolean {
    return !!this.currentAdmin && this.currentAdmin.isAuthenticated;
  }

  // Get current admin
  public getCurrentAdmin(): AdminUser | null {
    return this.currentAdmin;
  }

  // Check session validity (expires after 8 hours)
  public isSessionValid(): boolean {
    if (!this.currentAdmin) {
      return false;
    }

    const loginTime = new Date(this.currentAdmin.loginTime);
    const now = new Date();
    const sessionDuration = 8 * 60 * 60 * 1000; // 8 hours in milliseconds

    return (now.getTime() - loginTime.getTime()) < sessionDuration;
  }

  // Save session to localStorage
  private saveSession(adminUser: AdminUser): void {
    try {
      const sessionData = {
        ...adminUser,
        expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(), // 8 hours
      };
      localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
    } catch (error) {
      console.error('Error saving admin session:', error);
    }
  }

  // Load session from localStorage
  private loadSession(): void {
    try {
      const sessionData = localStorage.getItem(this.sessionKey);
      if (!sessionData) {
        return;
      }

      const session = JSON.parse(sessionData);
      const expiresAt = new Date(session.expiresAt);
      const now = new Date();

      // Check if session is expired
      if (now > expiresAt) {
        this.clearSession();
        return;
      }

      // Restore admin session
      this.currentAdmin = {
        id: session.id,
        login: session.login,
        role: session.role,
        isAuthenticated: session.isAuthenticated,
        loginTime: session.loginTime,
      };

      console.log(`Admin session restored: ${this.currentAdmin.login}`);
    } catch (error) {
      console.error('Error loading admin session:', error);
      this.clearSession();
    }
  }

  // Clear session
  private clearSession(): void {
    localStorage.removeItem(this.sessionKey);
  }

  // Track admin sessions for activity logging
  private trackAdminSession(adminUser: AdminUser): void {
    try {
      const sessions = JSON.parse(localStorage.getItem('admin_sessions') || '[]');
      sessions.push({
        id: adminUser.id,
        login: adminUser.login,
        loginTime: adminUser.loginTime,
        timestamp: new Date().toISOString()
      });

      // Keep only last 10 sessions
      if (sessions.length > 10) {
        sessions.splice(0, sessions.length - 10);
      }

      localStorage.setItem('admin_sessions', JSON.stringify(sessions));
    } catch (error) {
      console.warn('Failed to track admin session:', error);
    }
  }

  // Get admin statistics
  public async getAdminStats() {
    if (!this.isAuthenticated()) {
      throw new Error('Требуется авторизация администратора');
    }

    try {
      // Try to get real stats from adminUserService
      const { adminUserService } = await import('./adminUserService');
      const stats = await adminUserService.getUserStats();

      return {
        totalUsers: stats.totalUsers,
        totalProfiles: stats.usersByRole.user + stats.usersByRole.moderator,
        activeUsers: stats.activeUsers,
        adminSessions: 1,
        lastUpdate: new Date().toISOString(),
        usersByRole: stats.usersByRole,
        recentRegistrations: stats.recentRegistrations,
        registrationSources: stats.registrationSources
      };
    } catch (error) {
      console.warn('Failed to get admin stats from adminUserService:', error);

      // If Supabase is connected, get real stats as fallback
      if (supabaseService.isConnected && supabaseService.client) {
        try {
          const [usersCount, profilesCount] = await Promise.all([
            supabaseService.client.from('users').select('id', { count: 'exact', head: true }),
            supabaseService.client.from('profiles').select('id', { count: 'exact', head: true }),
          ]);

          return {
            totalUsers: usersCount.count || 0,
            totalProfiles: profilesCount.count || 0,
            adminSessions: 1, // Current session
            lastUpdate: new Date().toISOString(),
          };
        } catch (supabaseError) {
          console.error('Error fetching Supabase admin stats:', supabaseError);
        }
      }

      // Final fallback mock data
      return {
        totalUsers: 0,
        totalProfiles: 0,
        adminSessions: 1,
        lastUpdate: new Date().toISOString(),
      };
    }
  }

  // Validate admin access for protected operations
  public validateAdminAccess(): void {
    if (!this.isAuthenticated() || !this.isSessionValid()) {
      throw new Error('Доступ запрещен. Требуется авторизация администратора.');
    }
  }
}

export const adminAuthService = AdminAuthService.getInstance();
export default adminAuthService;