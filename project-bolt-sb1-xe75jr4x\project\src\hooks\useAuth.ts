import React, { useState, useEffect, useCallback } from 'react';
import authService, { User, LoginCredentials, RegisterData } from '../services/authService';

interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  error: string | null;
  clearError: () => void;
}

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize auth state
  useEffect(() => {
    // Always initialize auth - the authService will handle Supabase vs fallback mode
    initializeAuth();

    // Listen for storage changes to update auth state when user logs in/out in another tab
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth') {
        // Auth state changed, refresh user
        initializeAuth();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Also listen for custom auth events (for same-tab updates)
    const handleAuthChange = () => {
      initializeAuth();
    };

    window.addEventListener('authStateChanged', handleAuthChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('authStateChanged', handleAuthChange);
    };
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);

      if (currentUser) {
        console.log('✅ User authenticated:', currentUser.email);
      }
    } catch (err) {
      console.error('Auth initialization error:', err);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      setIsLoading(true);
      setError(null);

      const authResponse = await authService.login(credentials);
      setUser(authResponse.user);

      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('authStateChanged'));
    } catch (err: any) {
      setError(err.message || 'Ошибка входа в систему');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const register = useCallback(async (userData: RegisterData) => {
    try {
      setIsLoading(true);
      setError(null);

      const authResponse = await authService.register(userData);
      setUser(authResponse.user);

      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('authStateChanged'));
    } catch (err: any) {
      setError(err.message || 'Ошибка регистрации');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await authService.logout();
      setUser(null);
      setError(null);

      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('authStateChanged'));
    } catch (err: any) {
      console.error('Logout error:', err);
      // Clear user state even if logout fails
      setUser(null);
      // Still dispatch event even if logout fails
      window.dispatchEvent(new CustomEvent('authStateChanged'));
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshUser = useCallback(async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (err) {
      console.error('Refresh user error:', err);
      setUser(null);
    }
  }, []);

  const hasPermission = useCallback((permission: string): boolean => {
    return authService.hasPermission(permission);
  }, [user]);

  const hasRole = useCallback((role: string): boolean => {
    return authService.hasRole(role);
  }, [user]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const authState = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    refreshUser,
    hasPermission,
    hasRole,
    error,
    clearError,
  };

  // Debug log whenever auth state changes
  React.useEffect(() => {
    console.log('🔐 useAuth state updated:', {
      user: user?.email || null,
      isAuthenticated: !!user,
      isLoading
    });
  }, [user, isLoading]);

  return authState;
};

export default useAuth;