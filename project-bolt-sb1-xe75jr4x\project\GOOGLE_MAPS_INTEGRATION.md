# Google Maps Integration Guide

## Overview

The application has been updated to use Google Maps JavaScript API instead of Leaflet for displaying exchange points (обменники) on an interactive map. This provides better performance, more accurate location data, and enhanced user experience.

## Features

### ✅ Implemented Features

1. **Interactive Google Maps Integration**
   - Full Google Maps JavaScript API integration
   - Custom markers for exchange points
   - Interactive info windows with exchange details
   - Map controls (zoom, street view, fullscreen)

2. **Exchange Point Display**
   - All registered exchange points displayed as markers
   - Custom marker icons with exchange symbol (₿)
   - Detailed popups showing:
     - Exchange name and address
     - Phone number and working hours
     - Current exchange rates (RUB)
     - Star ratings

3. **Filtering and Search**
   - District-based filtering
   - Search functionality across exchange names and addresses
   - Real-time marker updates based on filters

4. **Responsive Design**
   - Works on desktop and mobile devices
   - Responsive layout with sidebar exchange list
   - Touch-friendly controls

5. **Error Handling and Fallback**
   - Graceful fallback when Google Maps fails to load
   - Alternative list view with "Open in Maps" functionality
   - Retry mechanism for failed map loads
   - Clear error messages and instructions

6. **Performance Optimization**
   - Efficient marker management
   - Automatic map bounds adjustment
   - Lazy loading of map components

## Setup Instructions

### 1. Google Maps API Key Configuration

1. **Get Google Maps API Key:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable the following APIs:
     - Maps JavaScript API
     - Places API (optional, for future features)
     - Geocoding API (optional, for address validation)

2. **Configure API Key:**
   - Copy `.env.example` to `.env`
   - Add your Google Maps API key:
     ```
     VITE_GOOGLE_MAPS_API_KEY=your-google-maps-api-key-here
     ```

3. **API Key Restrictions (Recommended):**
   - Restrict the API key to your domain
   - Limit to specific APIs (Maps JavaScript API)
   - Set usage quotas to prevent unexpected charges

### 2. Environment Variables

Required environment variables:
```bash
# Google Maps Configuration (Required)
VITE_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Application Configuration
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_APP_VERSION=1.0.0

# Optional: Supabase for authentication
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
```

## Component Architecture

### Main Components

1. **GoogleMap.tsx** - Main map component
   - Handles Google Maps initialization
   - Manages markers and info windows
   - Provides filtering and search functionality

2. **MapFallback.tsx** - Fallback component
   - Displays when Google Maps fails to load
   - Shows exchange points in grid layout
   - Provides "Open in Maps" functionality

### Key Features

#### Map Initialization
```typescript
const loader = new Loader({
  apiKey: config.GOOGLE_MAPS_API_KEY,
  version: 'weekly',
  libraries: ['places', 'geometry'],
});
```

#### Custom Markers
- Blue circular markers with exchange symbol (₿)
- Click to show detailed info window
- Automatic clustering for better performance

#### Info Windows
- Exchange name and contact information
- Current exchange rates
- Star ratings
- Responsive design

## Usage

### Basic Usage
```tsx
import GoogleMap from './components/GoogleMap';

<GoogleMap 
  searchQuery={searchQuery}
  onViewReviews={(exchangerId) => {
    // Handle review viewing
  }}
/>
```

### With Filtering
The component automatically handles:
- District-based filtering
- Search query filtering
- Real-time updates

## Troubleshooting

### Common Issues

1. **Map Not Loading**
   - Check if Google Maps API key is configured
   - Verify API key has correct permissions
   - Check browser console for errors
   - Ensure internet connection is stable

2. **API Key Errors**
   - Verify API key is valid and active
   - Check if required APIs are enabled
   - Ensure domain restrictions are correct

3. **Markers Not Showing**
   - Check if exchange data is loading correctly
   - Verify coordinates are valid
   - Check browser console for JavaScript errors

### Fallback Behavior

When Google Maps fails to load:
1. Error message is displayed
2. Fallback component shows exchange list
3. "Open in Maps" buttons provide navigation
4. Retry option available

## Performance Considerations

### Optimization Features

1. **Efficient Marker Management**
   - Markers are created/destroyed as needed
   - Automatic bounds adjustment
   - Memory cleanup on component unmount

2. **Lazy Loading**
   - Google Maps API loaded only when needed
   - Components render progressively

3. **Caching**
   - Exchange data cached via React Query
   - Map instance reused when possible

### Best Practices

1. **API Usage**
   - Monitor API usage in Google Cloud Console
   - Set appropriate quotas and billing alerts
   - Use API key restrictions

2. **Performance**
   - Limit number of markers displayed
   - Use clustering for large datasets
   - Implement pagination if needed

## Migration from Leaflet

### Changes Made

1. **Removed Dependencies**
   - `leaflet` package removed
   - `@types/leaflet` removed

2. **Added Dependencies**
   - `@googlemaps/js-api-loader`
   - `@types/google.maps`

3. **Component Updates**
   - `Map.tsx` replaced with `GoogleMap.tsx`
   - `MapLegend.tsx` removed (functionality integrated)
   - Updated imports in `App.tsx`

### Benefits of Migration

1. **Better Performance**
   - Native Google Maps performance
   - Better mobile experience
   - Faster loading times

2. **Enhanced Features**
   - Street View integration
   - Better satellite imagery
   - More accurate location data

3. **User Experience**
   - Familiar Google Maps interface
   - Better touch controls
   - Integrated navigation

## Future Enhancements

### Planned Features

1. **Advanced Filtering**
   - Filter by exchange rates
   - Filter by working hours
   - Filter by services offered

2. **Enhanced Markers**
   - Different marker types for different services
   - Marker clustering for better performance
   - Custom marker animations

3. **Additional Features**
   - Directions integration
   - Street View integration
   - Nearby places search

4. **Performance Improvements**
   - Marker clustering
   - Virtual scrolling for exchange list
   - Progressive loading

## Support

For issues related to Google Maps integration:

1. Check the browser console for errors
2. Verify API key configuration
3. Test with fallback component
4. Check Google Cloud Console for API usage

For development questions, refer to:
- [Google Maps JavaScript API Documentation](https://developers.google.com/maps/documentation/javascript)
- [Google Maps API Loader Documentation](https://github.com/googlemaps/js-api-loader)
