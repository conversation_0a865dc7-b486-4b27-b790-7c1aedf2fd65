<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Management</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>User Management Test</h1>
    
    <div class="test-section info">
        <h2>Test Controls</h2>
        <button onclick="createTestUsers()">Create Test Users</button>
        <button onclick="testUserService()">Test User Service</button>
        <button onclick="testUserStats()">Test User Stats</button>
        <button onclick="clearAllUsers()">Clear All Users</button>
        <button onclick="showCurrentUsers()">Show Current Users</button>
    </div>

    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="test-section ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            console.log(`[${timestamp}] ${message}`);
        }

        function createTestUsers() {
            const testUsers = [
                {
                    id: `user-${Date.now()}-1`,
                    email: '<EMAIL>',
                    password: 'password123',
                    firstName: 'John',
                    lastName: 'Doe',
                    phone: '+1234567890',
                    role: 'user',
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    registrationSource: 'fallback'
                },
                {
                    id: `user-${Date.now()}-2`,
                    email: '<EMAIL>',
                    password: 'password123',
                    firstName: 'Jane',
                    lastName: 'Smith',
                    phone: '+1234567891',
                    role: 'moderator',
                    isActive: true,
                    createdAt: new Date(Date.now() - 86400000).toISOString(),
                    registrationSource: 'fallback'
                },
                {
                    id: `user-${Date.now()}-3`,
                    email: '<EMAIL>',
                    password: 'password123',
                    firstName: 'Bob',
                    lastName: 'Wilson',
                    phone: '+1234567892',
                    role: 'user',
                    isActive: false,
                    createdAt: new Date(Date.now() - 172800000).toISOString(),
                    registrationSource: 'fallback'
                }
            ];

            const existingUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
            const updatedUsers = [...existingUsers, ...testUsers];
            localStorage.setItem('registered_users', JSON.stringify(updatedUsers));

            log(`Created ${testUsers.length} test users`, 'success');
            showCurrentUsers();
        }

        async function testUserService() {
            try {
                log('Testing adminUserService...', 'info');
                
                // This would normally be imported, but for testing we'll simulate the service
                const users = JSON.parse(localStorage.getItem('registered_users') || '[]');
                
                log(`Found ${users.length} users in localStorage`, 'info');
                
                // Test filtering
                const activeUsers = users.filter(u => u.isActive);
                const inactiveUsers = users.filter(u => !u.isActive);
                const usersByRole = users.reduce((acc, user) => {
                    acc[user.role] = (acc[user.role] || 0) + 1;
                    return acc;
                }, {});

                log(`
                    <h3>User Service Test Results:</h3>
                    <p>Total Users: ${users.length}</p>
                    <p>Active Users: ${activeUsers.length}</p>
                    <p>Inactive Users: ${inactiveUsers.length}</p>
                    <p>Users by Role: ${JSON.stringify(usersByRole)}</p>
                `, 'success');

            } catch (error) {
                log(`User service test failed: ${error.message}`, 'error');
            }
        }

        async function testUserStats() {
            try {
                log('Testing user statistics...', 'info');
                
                const users = JSON.parse(localStorage.getItem('registered_users') || '[]');
                const now = new Date();
                const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

                const stats = {
                    totalUsers: users.length,
                    activeUsers: users.filter(u => u.isActive).length,
                    inactiveUsers: users.filter(u => !u.isActive).length,
                    usersByRole: {
                        admin: users.filter(u => u.role === 'admin').length,
                        moderator: users.filter(u => u.role === 'moderator').length,
                        user: users.filter(u => u.role === 'user').length,
                    },
                    recentRegistrations: {
                        today: users.filter(u => new Date(u.createdAt) >= today).length,
                        thisWeek: users.filter(u => new Date(u.createdAt) >= thisWeek).length,
                        thisMonth: users.filter(u => new Date(u.createdAt) >= thisMonth).length,
                    },
                    registrationSources: {
                        supabase: users.filter(u => u.registrationSource === 'supabase').length,
                        backend: users.filter(u => u.registrationSource === 'backend').length,
                        fallback: users.filter(u => u.registrationSource === 'fallback').length,
                    }
                };

                log(`
                    <h3>User Statistics:</h3>
                    <pre>${JSON.stringify(stats, null, 2)}</pre>
                `, 'success');

            } catch (error) {
                log(`User stats test failed: ${error.message}`, 'error');
            }
        }

        function clearAllUsers() {
            localStorage.removeItem('registered_users');
            localStorage.removeItem('fallback_users');
            log('All users cleared from localStorage', 'info');
            showCurrentUsers();
        }

        function showCurrentUsers() {
            const registeredUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
            const fallbackUsers = JSON.parse(localStorage.getItem('fallback_users') || '[]');
            
            let tableHtml = `
                <h3>Current Users in localStorage:</h3>
                <h4>Registered Users (${registeredUsers.length}):</h4>
                <table>
                    <tr>
                        <th>Email</th>
                        <th>Name</th>
                        <th>Role</th>
                        <th>Active</th>
                        <th>Source</th>
                        <th>Created</th>
                    </tr>
            `;
            
            registeredUsers.forEach(user => {
                tableHtml += `
                    <tr>
                        <td>${user.email}</td>
                        <td>${user.firstName || ''} ${user.lastName || ''}</td>
                        <td>${user.role}</td>
                        <td>${user.isActive ? 'Yes' : 'No'}</td>
                        <td>${user.registrationSource || 'fallback'}</td>
                        <td>${new Date(user.createdAt).toLocaleString()}</td>
                    </tr>
                `;
            });
            
            tableHtml += '</table>';
            
            if (fallbackUsers.length > 0) {
                tableHtml += `
                    <h4>Fallback Users (${fallbackUsers.length}):</h4>
                    <table>
                        <tr>
                            <th>Email</th>
                            <th>Name</th>
                            <th>Role</th>
                            <th>Active</th>
                            <th>Created</th>
                        </tr>
                `;
                
                fallbackUsers.forEach(user => {
                    tableHtml += `
                        <tr>
                            <td>${user.email}</td>
                            <td>${user.name || user.firstName || ''} ${user.lastName || ''}</td>
                            <td>${user.role}</td>
                            <td>${user.isActive ? 'Yes' : 'No'}</td>
                            <td>${new Date(user.createdAt).toLocaleString()}</td>
                        </tr>
                    `;
                });
                
                tableHtml += '</table>';
            }
            
            log(tableHtml, 'info');
        }

        // Show initial state
        window.addEventListener('load', () => {
            log('User Management Test Loaded', 'info');
            showCurrentUsers();
        });
    </script>
</body>
</html>
