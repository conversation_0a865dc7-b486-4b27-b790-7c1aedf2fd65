import React, { useState, useEffect } from 'react';
import { config } from '../../config/environment';

interface TestResult {
  name: string;
  status: 'testing' | 'success' | 'error';
  message: string;
  details?: any;
}

export const ConnectivityTest: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Backend API Health', status: 'testing', message: 'Testing...' },
    { name: 'Backend Auth Endpoint', status: 'testing', message: 'Testing...' },
    { name: 'Supabase Connection', status: 'testing', message: 'Testing...' },
  ]);

  const updateTest = (index: number, update: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => i === index ? { ...test, ...update } : test));
  };

  const testBackendHealth = async () => {
    try {
      const response = await fetch(`${config.API_BASE_URL}/health`);
      if (response.ok) {
        const data = await response.json();
        updateTest(0, {
          status: 'success',
          message: 'Backend is running',
          details: data
        });
      } else {
        updateTest(0, {
          status: 'error',
          message: `HTTP ${response.status}: ${response.statusText}`
        });
      }
    } catch (error: any) {
      updateTest(0, {
        status: 'error',
        message: error.message || 'Connection failed'
      });
    }
  };

  const testBackendAuth = async () => {
    try {
      const response = await fetch(`${config.API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'test123456',
          name: 'Connectivity Test'
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        updateTest(1, {
          status: 'success',
          message: 'Auth endpoint accessible',
          details: data
        });
      } else {
        updateTest(1, {
          status: 'error',
          message: `HTTP ${response.status}: ${response.statusText}`
        });
      }
    } catch (error: any) {
      updateTest(1, {
        status: 'error',
        message: error.message || 'Connection failed'
      });
    }
  };

  const testSupabase = async () => {
    try {
      const response = await fetch(`${config.SUPABASE_URL}/rest/v1/`, {
        headers: {
          'apikey': config.SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${config.SUPABASE_ANON_KEY}`
        }
      });
      
      if (response.ok) {
        updateTest(2, {
          status: 'success',
          message: 'Supabase is accessible'
        });
      } else {
        updateTest(2, {
          status: 'error',
          message: `HTTP ${response.status}: ${response.statusText}`
        });
      }
    } catch (error: any) {
      updateTest(2, {
        status: 'error',
        message: error.message || 'Connection failed'
      });
    }
  };

  useEffect(() => {
    testBackendHealth();
    testBackendAuth();
    testSupabase();
  }, []);

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'testing': return '🔄';
      case 'success': return '✅';
      case 'error': return '❌';
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'monospace', 
      backgroundColor: '#f5f5f5',
      border: '1px solid #ddd',
      borderRadius: '8px',
      margin: '20px'
    }}>
      <h3>Connectivity Test Results</h3>
      {tests.map((test, index) => (
        <div key={index} style={{ marginBottom: '10px', padding: '10px', backgroundColor: 'white', borderRadius: '4px' }}>
          <div style={{ fontWeight: 'bold' }}>
            {getStatusIcon(test.status)} {test.name}
          </div>
          <div style={{ marginLeft: '20px', color: test.status === 'error' ? 'red' : 'green' }}>
            {test.message}
          </div>
          {test.details && (
            <details style={{ marginLeft: '20px', marginTop: '5px' }}>
              <summary>Details</summary>
              <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                {JSON.stringify(test.details, null, 2)}
              </pre>
            </details>
          )}
        </div>
      ))}
      
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#e8f4f8', borderRadius: '4px' }}>
        <strong>Configuration:</strong>
        <div>API Base URL: {config.API_BASE_URL}</div>
        <div>Supabase URL: {config.SUPABASE_URL}</div>
        <div>Environment: {config.APP_ENV}</div>
      </div>
    </div>
  );
};
