<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест компонента HistoricalRates</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@tanstack/react-query@5/build/umd/index.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 15px;
            max-width: 300px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .debug-info h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }
        .debug-info p {
            margin: 5px 0;
            font-size: 12px;
            color: #666;
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-loading { color: #ffc107; }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <div class="debug-info">
        <h3>🔍 Отладочная информация</h3>
        <p>API URL: <span id="api-url">-</span></p>
        <p>Статус API: <span id="api-status" class="status-loading">Проверка...</span></p>
        <p>Последний запрос: <span id="last-request">-</span></p>
        <p>Ошибки: <span id="error-count">0</span></p>
        <button onclick="testAPI()" style="margin-top: 10px; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Тест API
        </button>
    </div>

    <script type="text/babel">
        const { useState, useEffect } = React;
        const { QueryClient, QueryClientProvider, useQuery } = ReactQuery;

        // Создаем query client
        const queryClient = new QueryClient({
            defaultOptions: {
                queries: {
                    retry: 1,
                    refetchOnWindowFocus: false,
                },
            },
        });

        // Конфигурация API
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        
        // Обновляем отладочную информацию
        document.getElementById('api-url').textContent = API_BASE_URL;

        // Простой компонент для тестирования API
        const HistoricalRatesTest = () => {
            const [filters, setFilters] = useState({
                currency_pair: '',
                days: 7,
                exchanger_id: '',
            });

            // Запрос исторических данных
            const { 
                data: historicalData, 
                isLoading, 
                error, 
                refetch 
            } = useQuery({
                queryKey: ['historical-rates-test', filters],
                queryFn: async () => {
                    const params = new URLSearchParams();
                    if (filters.currency_pair) params.append('currency_pair', filters.currency_pair);
                    if (filters.exchanger_id) params.append('exchanger_id', filters.exchanger_id);
                    params.append('days_back', filters.days.toString());
                    params.append('limit', '10');

                    const url = `${API_BASE_URL}/parsing/historical-rates?${params}`;
                    console.log('🔄 Запрос к API:', url);
                    
                    // Обновляем отладочную информацию
                    document.getElementById('last-request').textContent = new Date().toLocaleTimeString();
                    
                    const response = await fetch(url);
                    console.log('📡 Ответ API:', response.status, response.statusText);
                    
                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('❌ Ошибка API:', errorText);
                        document.getElementById('api-status').textContent = `Ошибка ${response.status}`;
                        document.getElementById('api-status').className = 'status-error';
                        throw new Error(`API Error: ${response.status} ${response.statusText}`);
                    }
                    
                    const data = await response.json();
                    console.log('✅ Данные получены:', data);
                    
                    document.getElementById('api-status').textContent = 'Работает';
                    document.getElementById('api-status').className = 'status-ok';
                    
                    return data;
                },
                refetchInterval: false,
            });

            // Обработка ошибок
            useEffect(() => {
                if (error) {
                    console.error('🚨 Ошибка компонента:', error);
                    const errorCount = parseInt(document.getElementById('error-count').textContent) + 1;
                    document.getElementById('error-count').textContent = errorCount;
                    document.getElementById('api-status').textContent = 'Ошибка';
                    document.getElementById('api-status').className = 'status-error';
                }
            }, [error]);

            // Обработка загрузки
            useEffect(() => {
                if (isLoading) {
                    document.getElementById('api-status').textContent = 'Загрузка...';
                    document.getElementById('api-status').className = 'status-loading';
                }
            }, [isLoading]);

            if (error) {
                return (
                    <div className="max-w-4xl mx-auto p-6">
                        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                            <h2 className="text-xl font-bold text-red-800 mb-4">❌ Ошибка загрузки данных</h2>
                            <p className="text-red-600 mb-4">
                                Не удалось загрузить исторические данные курсов валют
                            </p>
                            <p className="text-sm text-red-500 mb-4 font-mono">
                                {error.message}
                            </p>
                            <div className="space-x-2">
                                <button
                                    onClick={() => refetch()}
                                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
                                >
                                    Попробовать снова
                                </button>
                                <button
                                    onClick={() => window.location.reload()}
                                    className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors"
                                >
                                    Перезагрузить страницу
                                </button>
                            </div>
                        </div>
                    </div>
                );
            }

            if (isLoading) {
                return (
                    <div className="max-w-4xl mx-auto p-6">
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <h2 className="text-lg font-semibold text-blue-800">Загрузка исторических данных...</h2>
                            <p className="text-blue-600 mt-2">Пожалуйста, подождите</p>
                        </div>
                    </div>
                );
            }

            const rates = historicalData?.data?.rates || [];
            const totalCount = historicalData?.data?.total_count || 0;
            const currencyPairs = historicalData?.data?.available_filters?.currency_pairs || [];
            const exchangers = historicalData?.data?.available_filters?.exchangers || [];

            return (
                <div className="max-w-6xl mx-auto p-6">
                    <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
                        <h1 className="text-2xl font-bold text-gray-800 mb-4">
                            📊 Тест исторических курсов валют
                        </h1>
                        
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                            <div className="bg-blue-50 p-4 rounded-lg text-center">
                                <div className="text-2xl font-bold text-blue-600">{rates.length}</div>
                                <div className="text-sm text-blue-500">Загружено записей</div>
                            </div>
                            <div className="bg-green-50 p-4 rounded-lg text-center">
                                <div className="text-2xl font-bold text-green-600">{totalCount}</div>
                                <div className="text-sm text-green-500">Всего записей</div>
                            </div>
                            <div className="bg-purple-50 p-4 rounded-lg text-center">
                                <div className="text-2xl font-bold text-purple-600">{currencyPairs.length}</div>
                                <div className="text-sm text-purple-500">Валютных пар</div>
                            </div>
                            <div className="bg-orange-50 p-4 rounded-lg text-center">
                                <div className="text-2xl font-bold text-orange-600">{exchangers.length}</div>
                                <div className="text-sm text-orange-500">Обменников</div>
                            </div>
                        </div>

                        {/* Фильтры */}
                        <div className="bg-gray-50 p-4 rounded-lg mb-6">
                            <h3 className="font-semibold mb-3">Фильтры</h3>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label className="block text-sm font-medium mb-1">Валютная пара</label>
                                    <select
                                        value={filters.currency_pair}
                                        onChange={(e) => setFilters(prev => ({ ...prev, currency_pair: e.target.value }))}
                                        className="w-full p-2 border border-gray-300 rounded"
                                    >
                                        <option value="">Все пары</option>
                                        {currencyPairs.map((pair) => (
                                            <option key={pair.currency_pair} value={pair.currency_pair}>
                                                {pair.currency_pair} ({pair.records_count})
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-1">Период (дней)</label>
                                    <select
                                        value={filters.days}
                                        onChange={(e) => setFilters(prev => ({ ...prev, days: parseInt(e.target.value) }))}
                                        className="w-full p-2 border border-gray-300 rounded"
                                    >
                                        <option value={1}>1 день</option>
                                        <option value={7}>7 дней</option>
                                        <option value={14}>14 дней</option>
                                        <option value={30}>30 дней</option>
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-1">Обменник</label>
                                    <select
                                        value={filters.exchanger_id}
                                        onChange={(e) => setFilters(prev => ({ ...prev, exchanger_id: e.target.value }))}
                                        className="w-full p-2 border border-gray-300 rounded"
                                    >
                                        <option value="">Все обменники</option>
                                        {exchangers.map((exchanger) => (
                                            <option key={exchanger.exchanger_id} value={exchanger.exchanger_id}>
                                                {exchanger.exchanger_name} ({exchanger.records_count})
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        </div>

                        {/* Таблица данных */}
                        {rates.length > 0 ? (
                            <div className="overflow-x-auto">
                                <table className="min-w-full bg-white border border-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Валютная пара</th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Обменник</th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Курс покупки</th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Курс продажи</th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Дата</th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200">
                                        {rates.map((rate, index) => (
                                            <tr key={index} className="hover:bg-gray-50">
                                                <td className="px-4 py-2">
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        {rate.currency_pair}
                                                    </span>
                                                </td>
                                                <td className="px-4 py-2 text-sm text-gray-900">
                                                    {rate.exchanger_name || 'N/A'}
                                                </td>
                                                <td className="px-4 py-2 text-sm font-mono text-gray-900">
                                                    {rate.buy_rate ? rate.buy_rate.toFixed(4) : 'N/A'}
                                                </td>
                                                <td className="px-4 py-2 text-sm font-mono text-gray-900">
                                                    {rate.sell_rate ? rate.sell_rate.toFixed(4) : 'N/A'}
                                                </td>
                                                <td className="px-4 py-2 text-sm text-gray-500">
                                                    {rate.parsed_at ? new Date(rate.parsed_at).toLocaleString('ru-RU') : 'N/A'}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <p className="text-gray-500">Нет данных для отображения</p>
                            </div>
                        )}
                    </div>
                </div>
            );
        };

        // Главный компонент приложения
        const App = () => {
            return (
                <QueryClientProvider client={queryClient}>
                    <div className="min-h-screen bg-gray-100">
                        <HistoricalRatesTest />
                    </div>
                </QueryClientProvider>
            );
        };

        // Рендерим приложение
        ReactDOM.render(<App />, document.getElementById('root'));

        // Функция для тестирования API
        window.testAPI = async () => {
            try {
                document.getElementById('api-status').textContent = 'Тестирование...';
                document.getElementById('api-status').className = 'status-loading';
                
                const response = await fetch(`${API_BASE_URL}/parsing/historical-rates?limit=1`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    document.getElementById('api-status').textContent = 'Работает ✅';
                    document.getElementById('api-status').className = 'status-ok';
                    console.log('✅ API тест успешен:', data);
                } else {
                    throw new Error(`API Error: ${response.status}`);
                }
            } catch (error) {
                document.getElementById('api-status').textContent = 'Ошибка ❌';
                document.getElementById('api-status').className = 'status-error';
                console.error('❌ API тест неудачен:', error);
            }
        };

        // Автоматический тест при загрузке
        setTimeout(() => {
            window.testAPI();
        }, 1000);
    </script>
</body>
</html>
