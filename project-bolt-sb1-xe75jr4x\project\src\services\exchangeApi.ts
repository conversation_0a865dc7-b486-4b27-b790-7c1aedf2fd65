// Real API service for exchange data
import axios, { AxiosInstance } from 'axios';
import { Exchanger, Review, ExchangeRate } from '../types';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';

class ExchangeApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor for auth
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('authToken');
          window.location.href = '/admin';
        }
        return Promise.reject(error);
      }
    );
  }

  // Exchangers API
  async getExchangers(params?: {
    district?: string;
    search?: string;
    limit?: number;
    force_refresh?: boolean;
  }) {
    const response = await this.client.get('/exchangers/', { params });
    return response.data.data;
  }

  async getTopExchangers(limit = 10) {
    try {
      const response = await this.client.get('/exchangers/top', {
        params: { limit }
      });
      return response.data.data;
    } catch (error) {
      console.warn('Top exchangers API failed, using enhanced fallback:', error.message);
      
      // Enhanced fallback with proper data structure
      try {
        const { getActiveExchangersForMainPage } = await import('../data/adminData');
        const activeExchangers = getActiveExchangersForMainPage();
        
        if (activeExchangers && activeExchangers.length > 0) {
          console.log(`Fallback: Found ${activeExchangers.length} active exchangers from admin data`);
          
          const topExchangers = activeExchangers
            .filter(exchanger => exchanger && typeof exchanger.rating === 'number')
            .sort((a, b) => {
              // Enhanced sorting logic
              const ratingDiff = (b.rating || 0) - (a.rating || 0);
              if (Math.abs(ratingDiff) > 0.05) return ratingDiff;
              
              // If ratings are close, sort by review count
              const reviewDiff = (b.reviewCount || 0) - (a.reviewCount || 0);
              if (reviewDiff !== 0) return reviewDiff;
              
              // Finally, sort by name for consistency
              return a.name.localeCompare(b.name, 'ru');
            })
            .slice(0, limit);
          
          console.log(`Fallback: Returning ${topExchangers.length} top exchangers`);
          
          return {
            exchangers: topExchangers,
            total: activeExchangers.length,
            source: 'admin_fallback',
            loadedAt: new Date().toISOString(),
            fallbackReason: error.message || 'API unavailable'
          };
        }
      } catch (importError) {
        console.error('Error importing admin data for top exchangers:', importError);
      }
      
      // Final fallback to mock data
      const { allExchangers } = await import('../data/mockData');
      const topMockExchangers = allExchangers
        .filter(exchanger => exchanger && typeof exchanger.rating === 'number')
        .sort((a, b) => {
          const ratingDiff = (b.rating || 0) - (a.rating || 0);
          if (Math.abs(ratingDiff) > 0.05) return ratingDiff;
          return (b.reviewCount || 0) - (a.reviewCount || 0);
        })
        .slice(0, limit);
      
      console.log(`Final fallback: Using ${topMockExchangers.length} mock exchangers`);
      
      return {
        exchangers: topMockExchangers,
        total: allExchangers.length,
        source: 'mock_fallback',
        loadedAt: new Date().toISOString(),
        fallbackReason: 'All data sources failed'
      };
    }
  }

  async getExchangerById(id: number) {
    const response = await this.client.get(`/exchangers/${id}`);
    return response.data.data;
  }

  async getExchangerRates(id: number) {
    const response = await this.client.get(`/exchangers/${id}/rates`);
    return response.data.data;
  }

  // Reviews API (placeholder for future implementation)
  async getReviews(params?: {
    exchangerId?: number;
    page?: number;
    limit?: number;
  }) {
    // For now, return mock data until reviews API is implemented
    const mockReviews = JSON.parse(localStorage.getItem('approvedReviews') || '[]');
    return {
      reviews: mockReviews.filter((r: Review) => 
        !params?.exchangerId || r.exchangerId === params.exchangerId
      ),
      total: mockReviews.length
    };
  }

  async createReview(reviewData: Omit<Review, 'id' | 'date' | 'moderated'>) {
    // Store in localStorage for now
    const pendingReviews = JSON.parse(localStorage.getItem('pendingReviews') || '[]');
    const newReview = {
      ...reviewData,
      id: Date.now(),
      date: new Date().toISOString().split('T')[0],
      moderated: false
    };
    pendingReviews.push(newReview);
    localStorage.setItem('pendingReviews', JSON.stringify(pendingReviews));
    return newReview;
  }

  // Admin API
  async getSystemStatus() {
    try {
      const response = await this.client.get('/admin/status');
      return response.data.data;
    } catch (error) {
      // Fallback to mock data if API is not available
      console.warn('API not available, using fallback data');
      return {
        status: 'running',
        exchangers_count: 45,
        last_update: new Date().toISOString()
      };
    }
  }

  async forceUpdateRates() {
    try {
      const response = await this.client.post('/admin/rates/update');
      return response.data.data;
    } catch (error) {
      // Simulate successful update for demo
      console.warn('API not available, simulating update');
      return {
        success: true,
        message: 'Курсы обновлены (демо режим)',
        updated_count: 3
      };
    }
  }

  async testGoogleSheetsConnection() {
    try {
      const response = await this.client.post('/admin/sheets/test');
      return response.data.data;
    } catch (error) {
      // Simulate test result for demo
      console.warn('API not available, simulating test');
      return {
        success: true,
        message: 'Подключение успешно (демо режим)',
        preview_data: [
          ['Exchanger Name', 'RUB_BUY', 'RUB_SELL'],
          ['Русский обменник №1', '36.2', '37.8'],
          ['Phuket Exchange Pro', '36.0', '37.9']
        ]
      };
    }
  }

  async getCacheInfo() {
    try {
      const response = await this.client.get('/admin/cache/info');
      return response.data.data;
    } catch (error) {
      // Fallback cache info
      console.warn('API not available, using fallback cache info');
      return {
        exists: true,
        ttl_seconds: 300,
        expires_at: new Date(Date.now() + 300000).toISOString()
      };
    }
  }

  async clearCache() {
    try {
      const response = await this.client.post('/admin/cache/clear');
      return response.data;
    } catch (error) {
      // Simulate cache clear
      console.warn('API not available, simulating cache clear');
      return {
        success: true,
        message: 'Кэш очищен (демо режим)'
      };
    }
  }
}

export const exchangeApi = new ExchangeApiService();