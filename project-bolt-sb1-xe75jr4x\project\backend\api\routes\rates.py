"""
Exchange rates API routes for historical data
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from pydantic import BaseModel
import logging

from services.database_service import database_service
from services.exchange_service import exchange_service

logger = logging.getLogger(__name__)
router = APIRouter()

class HistoricalRateResponse(BaseModel):
    currency_pair: str
    buy_rate: float
    sell_rate: float
    exchanger_name: str
    exchanger_id: str
    parsed_at: datetime

class HistoricalDataResponse(BaseModel):
    success: bool
    data: Dict[str, Any]
    message: Optional[str] = None

@router.get("/current", response_model=HistoricalDataResponse)
async def get_current_rates():
    """Get current exchange rates from all exchangers"""
    try:
        rates_data = await exchange_service.get_exchange_rates()
        
        if not rates_data:
            raise HTTPException(status_code=503, detail="Exchange rates service unavailable")
        
        return HistoricalDataResponse(
            success=True,
            data={
                "rates": rates_data.get('exchangers', []),
                "total_count": rates_data.get('total_count', 0),
                "last_updated": rates_data.get('last_updated')
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting current rates: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/history", response_model=HistoricalDataResponse)
async def get_historical_rates(
    currency_pair: Optional[str] = Query(None, description="Filter by currency pair (e.g., THB/RUB)"),
    exchanger_id: Optional[str] = Query(None, description="Filter by exchanger ID"),
    days: int = Query(7, ge=1, le=365, description="Number of days to look back"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records")
):
    """Get historical exchange rates"""
    try:
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # Since we don't have a database yet, generate mock historical data
        # based on current rates with some variation
        rates_data = await exchange_service.get_exchange_rates()

        if not rates_data or not rates_data.get('exchangers'):
            return HistoricalDataResponse(
                success=True,
                data={
                    "rates": [],
                    "total_count": 0,
                    "filters": {
                        "currency_pair": currency_pair,
                        "exchanger_id": exchanger_id,
                        "days": days,
                        "date_range": {
                            "start": start_date.isoformat(),
                            "end": end_date.isoformat()
                        }
                    }
                }
            )

        # Generate mock historical data
        historical_rates = []
        import random

        for exchanger in rates_data.get('exchangers', []):
            if exchanger_id and str(exchanger.get('name', '')) != exchanger_id:
                continue

            exchanger_name = exchanger.get('name', 'Unknown')
            rates = exchanger.get('rates', {})

            # Generate data points for the requested period
            for i in range(min(days * 2, limit)):  # 2 data points per day max
                current_date = end_date - timedelta(hours=i * 12)  # Every 12 hours

                for pair, rate_data in rates.items():
                    if currency_pair and pair != currency_pair:
                        continue

                    if isinstance(rate_data, dict) and 'buy' in rate_data and 'sell' in rate_data:
                        # Add some random variation to simulate historical changes
                        base_buy = rate_data['buy']
                        base_sell = rate_data['sell']

                        # Random variation of ±5%
                        variation = random.uniform(-0.05, 0.05)
                        buy_rate = base_buy * (1 + variation)
                        sell_rate = base_sell * (1 + variation)

                        historical_rates.append({
                            "currency_pair": pair,
                            "buy_rate": round(buy_rate, 4),
                            "sell_rate": round(sell_rate, 4),
                            "exchanger_name": exchanger_name,
                            "exchanger_id": exchanger_name,  # Using name as ID for now
                            "parsed_at": current_date.isoformat()
                        })

        # Sort by date (newest first) and limit
        historical_rates.sort(key=lambda x: x['parsed_at'], reverse=True)
        historical_rates = historical_rates[:limit]

        return HistoricalDataResponse(
            success=True,
            data={
                "rates": historical_rates,
                "total_count": len(historical_rates),
                "filters": {
                    "currency_pair": currency_pair,
                    "exchanger_id": exchanger_id,
                    "days": days,
                    "date_range": {
                        "start": start_date.isoformat(),
                        "end": end_date.isoformat()
                    }
                }
            }
        )

    except Exception as e:
        logger.error(f"Error getting historical rates: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/history/summary", response_model=HistoricalDataResponse)
async def get_historical_summary(
    currency_pair: str = Query(..., description="Currency pair (e.g., THB/RUB)"),
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze")
):
    """Get historical rates summary with statistics"""
    try:
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # Since we don't have a database yet, generate mock summary data
        rates_data = await exchange_service.get_exchange_rates()

        if not rates_data or not rates_data.get('exchangers'):
            return HistoricalDataResponse(
                success=True,
                data={
                    "currency_pair": currency_pair,
                    "summary": None,
                    "message": "No historical data found for the specified period"
                }
            )

        # Find current rates for the requested currency pair
        current_rates = []
        exchanger_count = 0

        for exchanger in rates_data.get('exchangers', []):
            rates = exchanger.get('rates', {})
            if currency_pair in rates:
                rate_data = rates[currency_pair]
                if isinstance(rate_data, dict) and 'buy' in rate_data and 'sell' in rate_data:
                    current_rates.append({
                        'buy': rate_data['buy'],
                        'sell': rate_data['sell']
                    })
                    exchanger_count += 1

        if not current_rates:
            return HistoricalDataResponse(
                success=True,
                data={
                    "currency_pair": currency_pair,
                    "summary": None,
                    "message": "No data found for the specified currency pair"
                }
            )

        # Calculate statistics based on current rates (mock historical analysis)
        buy_rates = [r['buy'] for r in current_rates]
        sell_rates = [r['sell'] for r in current_rates]

        import statistics

        # Generate daily trends (mock data with some variation)
        daily_data = []
        import random

        for i in range(min(days, 30)):  # Limit to 30 days for demo
            date = start_date + timedelta(days=i)

            # Add some random variation to simulate trends
            variation = random.uniform(-0.03, 0.03)  # ±3% variation
            avg_buy = statistics.mean(buy_rates) * (1 + variation)
            avg_sell = statistics.mean(sell_rates) * (1 + variation)

            daily_data.append({
                "date": date.date().isoformat(),
                "avg_buy_rate": round(avg_buy, 4),
                "avg_sell_rate": round(avg_sell, 4),
                "records_count": random.randint(5, 15)
            })

        return HistoricalDataResponse(
            success=True,
            data={
                "currency_pair": currency_pair,
                "period_days": days,
                "summary": {
                    "total_records": len(current_rates) * days,  # Mock total
                    "exchanger_count": exchanger_count,
                    "buy_rate_stats": {
                        "average": round(statistics.mean(buy_rates), 4),
                        "minimum": round(min(buy_rates) * 0.95, 4),  # Mock variation
                        "maximum": round(max(buy_rates) * 1.05, 4),  # Mock variation
                    },
                    "sell_rate_stats": {
                        "average": round(statistics.mean(sell_rates), 4),
                        "minimum": round(min(sell_rates) * 0.95, 4),  # Mock variation
                        "maximum": round(max(sell_rates) * 1.05, 4),  # Mock variation
                    },
                    "date_range": {
                        "first_record": start_date.isoformat(),
                        "last_record": end_date.isoformat(),
                    }
                },
                "daily_trends": daily_data
            }
        )

    except Exception as e:
        logger.error(f"Error getting historical summary: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/currency-pairs", response_model=HistoricalDataResponse)
async def get_available_currency_pairs():
    """Get list of available currency pairs"""
    try:
        # Since we don't have a database yet, return common currency pairs
        # based on the current exchange rates structure
        rates_data = await exchange_service.get_exchange_rates()

        if not rates_data or not rates_data.get('exchangers'):
            # Fallback to common pairs
            currency_pairs = [
                {"currency_pair": "THB/RUB", "records_count": 100},
                {"currency_pair": "RUB/THB", "records_count": 95},
                {"currency_pair": "USDT/THB", "records_count": 90},
                {"currency_pair": "THB/USDT", "records_count": 85},
                {"currency_pair": "USD/THB", "records_count": 80},
                {"currency_pair": "THB/USD", "records_count": 75},
            ]
        else:
            # Extract currency pairs from current data
            pairs_set = set()
            for exchanger in rates_data.get('exchangers', []):
                rates = exchanger.get('rates', {})
                for pair in rates.keys():
                    pairs_set.add(pair)

            currency_pairs = []
            for pair in sorted(pairs_set):
                currency_pairs.append({
                    "currency_pair": pair,
                    "records_count": 50  # Mock count since we don't have historical data yet
                })

        return HistoricalDataResponse(
            success=True,
            data={
                "currency_pairs": currency_pairs,
                "total_pairs": len(currency_pairs)
            }
        )

    except Exception as e:
        logger.error(f"Error getting currency pairs: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
