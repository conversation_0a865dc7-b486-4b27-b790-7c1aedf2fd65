import React, { useState } from 'react';
import { Play, CheckCircle, XCircle, Clock, AlertTriangle, RefreshCw } from 'lucide-react';
import IntegrationTester, { IntegrationTestSuite, TestResult } from '../../utils/integrationTester';
import DataPersistenceDebugger from '../../utils/dataPersistenceDebugger';

const SystemHealthCheck: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<IntegrationTestSuite | null>(null);
  const [lastRunTime, setLastRunTime] = useState<Date | null>(null);

  const runHealthCheck = async () => {
    setIsRunning(true);
    try {
      const tester = new IntegrationTester();
      const results = await tester.runAllTests();
      setTestResults(results);
      setLastRunTime(new Date());
      
      // Log detailed report to console
      console.log(tester.generateReport(results));
    } catch (error) {
      console.error('Health check failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <XCircle className="w-5 h-5 text-red-500" />
    );
  };

  const getStatusColor = (success: boolean) => {
    return success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
  };

  const debugInfo = DataPersistenceDebugger.getDebugInfo();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Проверка системы</h2>
          <p className="text-sm text-gray-600">
            Интеграционные тесты и диагностика системы обмена валют
          </p>
        </div>
        <button
          onClick={runHealthCheck}
          disabled={isRunning}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
        >
          {isRunning ? (
            <RefreshCw className="w-4 h-4 animate-spin" />
          ) : (
            <Play className="w-4 h-4" />
          )}
          <span>{isRunning ? 'Выполняется...' : 'Запустить проверку'}</span>
        </button>
      </div>

      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Обменники</p>
              <p className="text-2xl font-bold text-gray-800">{debugInfo.activeExchangerCount}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Размер данных</p>
              <p className="text-2xl font-bold text-gray-800">{Math.round(debugInfo.localStorageSize / 1024)}KB</p>
            </div>
            <Clock className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Резервная копия</p>
              <p className="text-2xl font-bold text-gray-800">{debugInfo.backupExists ? 'Есть' : 'Нет'}</p>
            </div>
            {debugInfo.backupExists ? (
              <CheckCircle className="w-8 h-8 text-green-500" />
            ) : (
              <AlertTriangle className="w-8 h-8 text-yellow-500" />
            )}
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Последнее сохранение</p>
              <p className="text-sm font-medium text-gray-800">
                {debugInfo.lastSaveTime ? new Date(debugInfo.lastSaveTime).toLocaleTimeString('ru-RU') : 'Неизвестно'}
              </p>
            </div>
            <Clock className="w-8 h-8 text-gray-400" />
          </div>
        </div>
      </div>

      {/* Test Results */}
      {testResults && (
        <div className="bg-white rounded-lg shadow border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getStatusIcon(testResults.overallSuccess)}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">
                    Результаты тестирования
                  </h3>
                  <p className="text-sm text-gray-600">
                    {testResults.summary.passed}/{testResults.summary.total} тестов пройдено
                    {lastRunTime && ` • ${lastRunTime.toLocaleString('ru-RU')}`}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">Время выполнения</p>
                <p className="text-lg font-semibold text-gray-800">{testResults.totalDuration}ms</p>
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              {testResults.results.map((result: TestResult, index: number) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border ${getStatusColor(result.success)}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      {getStatusIcon(result.success)}
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-800">{result.testName}</h4>
                        <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                        {result.details && (
                          <details className="mt-2">
                            <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                              Подробности
                            </summary>
                            <pre className="text-xs text-gray-600 mt-1 bg-gray-50 p-2 rounded overflow-x-auto">
                              {JSON.stringify(result.details, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                    <div className="text-right text-sm text-gray-500">
                      {result.duration}ms
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Debug Information */}
      <div className="bg-white rounded-lg shadow border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800">Отладочная информация</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Состояние данных</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Всего обменников:</span>
                  <span className="font-medium">{debugInfo.exchangerCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Активных:</span>
                  <span className="font-medium">{debugInfo.activeExchangerCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Размер хранилища:</span>
                  <span className="font-medium">{debugInfo.localStorageSize} байт</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Флаги сессии</h4>
              <div className="space-y-1">
                {debugInfo.sessionFlags.length > 0 ? (
                  debugInfo.sessionFlags.map(flag => (
                    <span key={flag} className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                      {flag}
                    </span>
                  ))
                ) : (
                  <span className="text-sm text-gray-500">Нет активных флагов</span>
                )}
              </div>
            </div>
          </div>

          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex space-x-2">
              <button
                onClick={() => DataPersistenceDebugger.logDebugInfo()}
                className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded text-sm"
              >
                Логировать отладку
              </button>
              <button
                onClick={() => DataPersistenceDebugger.forceDataRefresh()}
                className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded text-sm"
              >
                Принудительное обновление
              </button>
              <button
                onClick={() => DataPersistenceDebugger.clearPersistenceFlags()}
                className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded text-sm"
              >
                Очистить флаги
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-800 mb-2">Инструкции по тестированию</h4>
        <div className="text-sm text-blue-700 space-y-1">
          <p>• Запустите проверку системы для выполнения всех интеграционных тестов</p>
          <p>• Проверьте консоль браузера для подробных логов</p>
          <p>• Используйте <code>window.runIntegrationTests()</code> в консоли для ручного запуска</p>
          <p>• Используйте <code>window.debugPersistence</code> для отладки сохранения данных</p>
        </div>
      </div>
    </div>
  );
};

export default SystemHealthCheck;
