// User registration service with Supabase integration
import { supabaseService } from './supabaseClient';
import { config } from '../config/environment';

export interface UserRegistrationData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
}

export interface RegistrationResult {
  success: boolean;
  user?: any;
  message: string;
  requiresEmailConfirmation?: boolean;
}

export interface ValidationError {
  field: string;
  message: string;
}

class UserRegistrationService {
  private static instance: UserRegistrationService;
  private loggedEvents = new Set<string>(); // Track logged events to prevent duplicates

  private constructor() {}

  public static getInstance(): UserRegistrationService {
    if (!UserRegistrationService.instance) {
      UserRegistrationService.instance = new UserRegistrationService();
    }
    return UserRegistrationService.instance;
  }

  // Prevent duplicate logging
  private logOnce(eventKey: string, logFunction: () => void): void {
    if (!this.loggedEvents.has(eventKey)) {
      logFunction();
      this.loggedEvents.add(eventKey);

      // Clean up old events (keep only last 100)
      if (this.loggedEvents.size > 100) {
        const oldEvents = Array.from(this.loggedEvents).slice(0, 50);
        oldEvents.forEach(event => this.loggedEvents.delete(event));
      }
    }
  }

  // Validate registration data
  public validateRegistrationData(data: UserRegistrationData): ValidationError[] {
    const errors: ValidationError[] = [];

    // Email validation
    if (!data.email) {
      errors.push({ field: 'email', message: 'Email обязателен' });
    } else if (!this.isValidEmail(data.email)) {
      errors.push({ field: 'email', message: 'Некорректный формат email' });
    }

    // Password validation
    if (!data.password) {
      errors.push({ field: 'password', message: 'Пароль обязателен' });
    } else if (data.password.length < 8) {
      errors.push({ field: 'password', message: 'Пароль должен содержать минимум 8 символов' });
    } else if (!this.isStrongPassword(data.password)) {
      errors.push({ 
        field: 'password', 
        message: 'Пароль должен содержать буквы, цифры и специальные символы' 
      });
    }

    // Name validation
    if (data.firstName && data.firstName.length < 2) {
      errors.push({ field: 'firstName', message: 'Имя должно содержать минимум 2 символа' });
    }

    if (data.lastName && data.lastName.length < 2) {
      errors.push({ field: 'lastName', message: 'Фамилия должна содержать минимум 2 символа' });
    }

    // Phone validation
    if (data.phone && !this.isValidPhone(data.phone)) {
      errors.push({ field: 'phone', message: 'Некорректный формат телефона' });
    }

    return errors;
  }

  // Register new user
  public async registerUser(data: UserRegistrationData): Promise<RegistrationResult> {
    try {
      // Validate input data
      const validationErrors = this.validateRegistrationData(data);
      if (validationErrors.length > 0) {
        console.log('Validation errors:', validationErrors);
        return {
          success: false,
          message: validationErrors.map(e => e.message).join(', ')
        };
      }

      // Try Supabase first if connected
      if (supabaseService.isConnected && supabaseService.client) {
        const supabaseResult = await this.registerWithSupabase(data);

        // If Supabase fails with network error, try backend API fallback
        if (!supabaseResult.success &&
            (supabaseResult.message.includes('Failed to fetch') ||
             supabaseResult.message.includes('fetch') ||
             supabaseResult.message.includes('network'))) {
          try {
            return await this.registerWithBackendAPI(data);
          } catch (backendError) {
            return await this.registerWithFallback(data);
          }
        }

        return supabaseResult;
      } else {
        // Try backend API first, then fallback to local storage
        try {
          return await this.registerWithBackendAPI(data);
        } catch (error) {
          return await this.registerWithFallback(data);
        }
      }
    } catch (error: any) {
      const errorKey = `registration_error_${Date.now()}`;
      this.logOnce(errorKey, () => {
        console.error('Registration error:', error);
      });

      // Provide more specific error messages
      let errorMessage = 'Произошла ошибка при регистрации. Попробуйте еще раз.';

      if (error.message) {
        if (error.message.includes('Failed to fetch')) {
          errorMessage = 'Ошибка подключения к серверу. Проверьте интернет-соединение.';
        } else if (error.message.includes('already exists') || error.message.includes('уже существует')) {
          errorMessage = 'Пользователь с таким email уже существует';
        } else if (error.message.includes('Invalid email') || error.message.includes('email')) {
          errorMessage = 'Неверный формат email адреса';
        } else if (error.message.includes('Password') || error.message.includes('password')) {
          errorMessage = 'Пароль не соответствует требованиям безопасности';
        } else {
          errorMessage = `Ошибка: ${error.message}`;
        }
      }

      return {
        success: false,
        message: errorMessage
      };
    }
  }

  // Register with Supabase
  private async registerWithSupabase(data: UserRegistrationData): Promise<RegistrationResult> {
    try {
      const { data: authData, error } = await supabaseService.client!.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            first_name: data.firstName || '',
            last_name: data.lastName || '',
            phone: data.phone || '',
          },
        },
      });

      if (error) {
        return {
          success: false,
          message: this.getSupabaseErrorMessage(error.message)
        };
      }

      if (!authData.user) {
        return {
          success: false,
          message: 'Ошибка создания пользователя'
        };
      }

      // Check if email confirmation is required
      if (!authData.session) {
        return {
          success: true,
          user: authData.user,
          message: 'Регистрация успешна! Проверьте email для подтверждения аккаунта.',
          requiresEmailConfirmation: true
        };
      }

      // Registration successful with immediate login
      return {
        success: true,
        user: authData.user,
        message: 'Регистрация успешна! Добро пожаловать!'
      };
    } catch (error: any) {
      const errorKey = `supabase_registration_error_${Date.now()}`;
      this.logOnce(errorKey, () => {
        console.error('Supabase registration error:', error);
      });

      // Check if it's a network error that should trigger fallback
      const isNetworkError = error.message && (
        error.message.includes('Failed to fetch') ||
        error.message.includes('fetch') ||
        error.message.includes('network') ||
        error.message.includes('NetworkError') ||
        error.name === 'TypeError'
      );

      return {
        success: false,
        message: isNetworkError ? 'Failed to fetch' : this.getSupabaseErrorMessage(error.message)
      };
    }
  }

  // Register with Backend API
  private async registerWithBackendAPI(data: UserRegistrationData): Promise<RegistrationResult> {
    try {
      const response = await fetch(`${config.API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          name: `${data.firstName || ''} ${data.lastName || ''}`.trim() || data.email.split('@')[0],
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        return {
          success: true,
          user: result.data?.user,
          message: result.message || 'Регистрация успешна!'
        };
      } else {
        return {
          success: false,
          message: result.message || 'Ошибка регистрации'
        };
      }
    } catch (error: any) {
      console.error('Backend API registration error:', error);
      throw error; // Re-throw to trigger fallback
    }
  }

  // Fallback registration for development
  private async registerWithFallback(data: UserRegistrationData): Promise<RegistrationResult> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Check if user already exists in localStorage
    const existingUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
    const userExists = existingUsers.some((user: any) => user.email === data.email);

    if (userExists) {
      return {
        success: false,
        message: 'Пользователь с таким email уже существует'
      };
    }

    // Create new user (store password for fallback authentication)
    const newUser = {
      id: `user-${Date.now()}`,
      email: data.email,
      password: data.password, // Store password for fallback login
      firstName: data.firstName || '',
      lastName: data.lastName || '',
      phone: data.phone || '',
      role: 'user',
      isActive: true,
      createdAt: new Date().toISOString(),
    };

    existingUsers.push(newUser);
    localStorage.setItem('registered_users', JSON.stringify(existingUsers));

    return {
      success: true,
      user: newUser,
      message: 'Регистрация успешна! Добро пожаловать!'
    };
  }

  // Check if email already exists
  public async checkEmailExists(email: string): Promise<boolean> {
    if (!this.isValidEmail(email)) {
      return false;
    }

    try {
      if (supabaseService.isConnected && supabaseService.client) {
        const { data, error } = await supabaseService.client
          .from('users')
          .select('email')
          .eq('email', email)
          .limit(1);

        return !error && data && data.length > 0;
      } else {
        // Fallback check
        const existingUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
        return existingUsers.some((user: any) => user.email === email);
      }
    } catch (error) {
      console.error('Error checking email existence:', error);
      return false;
    }
  }

  // Validation helpers
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  private isStrongPassword(password: string): boolean {
    // At least 8 characters, contains letters, numbers
    const hasLetter = /[a-zA-Z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasMinLength = password.length >= 8;
    
    return hasLetter && hasNumber && hasMinLength;
  }

  private isValidPhone(phone: string): boolean {
    // Basic phone validation (international format)
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }

  // Get user-friendly error messages for Supabase errors
  private getSupabaseErrorMessage(error: string): string {
    switch (error) {
      case 'User already registered':
        return 'Пользователь с таким email уже зарегистрирован';
      case 'Password should be at least 6 characters':
        return 'Пароль должен содержать минимум 6 символов';
      case 'Invalid email':
        return 'Некорректный email адрес';
      case 'Signup is disabled':
        return 'Регистрация временно отключена';
      case 'Email rate limit exceeded':
        return 'Превышен лимит отправки email. Попробуйте позже';
      default:
        return error || 'Произошла ошибка при регистрации';
    }
  }

  // Get registration statistics (for admin)
  public async getRegistrationStats() {
    try {
      if (supabaseService.isConnected && supabaseService.client) {
        const { data, error } = await supabaseService.client
          .from('users')
          .select('created_at, role')
          .order('created_at', { ascending: false });

        if (error) {
          throw error;
        }

        const today = new Date();
        const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

        return {
          total: data.length,
          thisMonth: data.filter(u => new Date(u.created_at) >= thisMonth).length,
          thisWeek: data.filter(u => new Date(u.created_at) >= thisWeek).length,
          today: data.filter(u => 
            new Date(u.created_at).toDateString() === today.toDateString()
          ).length,
          byRole: {
            admin: data.filter(u => u.role === 'admin').length,
            moderator: data.filter(u => u.role === 'moderator').length,
            user: data.filter(u => u.role === 'user').length,
          }
        };
      } else {
        // Fallback stats
        const users = JSON.parse(localStorage.getItem('registered_users') || '[]');
        return {
          total: users.length,
          thisMonth: users.length,
          thisWeek: users.length,
          today: 0,
          byRole: {
            admin: 1,
            moderator: 0,
            user: users.length,
          }
        };
      }
    } catch (error) {
      console.error('Error getting registration stats:', error);
      return {
        total: 0,
        thisMonth: 0,
        thisWeek: 0,
        today: 0,
        byRole: { admin: 0, moderator: 0, user: 0 }
      };
    }
  }
}

export const userRegistrationService = UserRegistrationService.getInstance();
export default userRegistrationService;