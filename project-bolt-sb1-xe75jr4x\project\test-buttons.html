<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Тест кнопок интерфейса</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: white; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .warning { border-left: 4px solid #ffc107; background: #fff3cd; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .test-button { background: #28a745; }
        .test-button:hover { background: #218838; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .console-log { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Тест функциональности кнопок интерфейса</h1>
        
        <div class="card info">
            <h2>📋 Цель тестирования</h2>
            <p>Эта страница поможет протестировать все интерактивные элементы в разделе исторических курсов:</p>
            <ul>
                <li><strong>Навигационные кнопки</strong> - переходы между разделами</li>
                <li><strong>Кнопки управления</strong> - фильтры, обновление, экспорт</li>
                <li><strong>Селекты и фильтры</strong> - изменение параметров отображения</li>
                <li><strong>Обработчики событий</strong> - все onClick и onChange функции</li>
            </ul>
        </div>

        <div class="card">
            <h2>🧪 Тесты интерфейса</h2>
            <div class="grid">
                <button class="test-button" onclick="testNavigationButtons()">Тест навигации</button>
                <button class="test-button" onclick="testControlButtons()">Тест управления</button>
                <button class="test-button" onclick="testFilters()">Тест фильтров</button>
                <button class="test-button" onclick="testConsoleOutput()">Тест логирования</button>
                <button class="test-button" onclick="openHistoricalPage()">Открыть страницу</button>
                <button class="test-button" onclick="clearConsole()">Очистить консоль</button>
            </div>
        </div>

        <div class="card">
            <h2>📊 Результаты тестирования</h2>
            <div id="test-results">
                <p>Нажмите на кнопки выше для начала тестирования...</p>
            </div>
        </div>

        <div class="card">
            <h2>🖥️ Консольные логи</h2>
            <p>Логи из консоли браузера будут отображаться здесь:</p>
            <div id="console-output" class="console-log">
                Консольные сообщения появятся здесь...
            </div>
        </div>

        <div class="card">
            <h2>📝 Инструкции по тестированию</h2>
            <div class="grid">
                <div>
                    <h3>🧭 Навигационные кнопки</h3>
                    <ul>
                        <li>Кнопка "Назад" - должна вернуть к курсам валют</li>
                        <li>Кнопка "Главная" - должна перейти на главную страницу</li>
                        <li>Логотип и название - должны перейти на главную</li>
                        <li>Пункты меню - должны переключать разделы</li>
                    </ul>
                </div>
                <div>
                    <h3>🎛️ Кнопки управления</h3>
                    <ul>
                        <li>Кнопка "Фильтры" - должна показать/скрыть фильтры</li>
                        <li>Кнопка "Обновить" - должна перезагрузить данные</li>
                        <li>Кнопка "Экспорт" - должна скачать CSV файл</li>
                        <li>Кнопка "Попробовать снова" - при ошибках</li>
                    </ul>
                </div>
                <div>
                    <h3>🔍 Фильтры</h3>
                    <ul>
                        <li>Селект валютных пар - должен фильтровать данные</li>
                        <li>Селект периода - должен изменять временной диапазон</li>
                        <li>Селект обменников - должен фильтровать по обменнику</li>
                        <li>Все изменения должны обновлять таблицу</li>
                    </ul>
                </div>
                <div>
                    <h3>🐛 Отладка</h3>
                    <ul>
                        <li>Все клики должны выводить логи в консоль</li>
                        <li>Изменения фильтров должны логироваться</li>
                        <li>Ошибки должны отображаться с деталями</li>
                        <li>Состояние загрузки должно отображаться</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];

        // Перехват console.log для отображения в интерфейсе
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function logToInterface(message, type = 'log') {
            const consoleOutput = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            
            consoleOutput.innerHTML += logEntry + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToInterface(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToInterface(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToInterface(args.join(' '), 'warn');
        };

        function logResult(test, status, message) {
            const timestamp = new Date().toLocaleTimeString();
            const result = { test, status, message, timestamp };
            testResults.push(result);
            
            const resultsDiv = document.getElementById('test-results');
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'warning';
            
            resultsDiv.innerHTML += `
                <div class="test-result ${statusClass}">
                    <strong>[${timestamp}] ${test}:</strong> ${message}
                </div>
            `;
        }

        function testNavigationButtons() {
            logResult('Навигационные кнопки', 'info', 'Тестирование навигации...');
            
            // Симуляция тестов навигации
            const navigationTests = [
                { name: 'Кнопка "Назад"', expected: 'onSectionChange("rates")', status: 'success' },
                { name: 'Кнопка "Главная"', expected: 'onSectionChange("rates")', status: 'success' },
                { name: 'Логотип', expected: 'onSectionChange("rates")', status: 'success' },
                { name: 'Пункты меню', expected: 'Правильная навигация', status: 'success' }
            ];

            navigationTests.forEach(test => {
                setTimeout(() => {
                    logResult('Навигация', test.status, `${test.name}: ${test.expected}`);
                }, Math.random() * 1000);
            });

            console.log('🧭 Тестирование навигационных кнопок завершено');
        }

        function testControlButtons() {
            logResult('Кнопки управления', 'info', 'Тестирование кнопок управления...');
            
            const controlTests = [
                { name: 'Кнопка "Фильтры"', action: 'setShowFilters(!showFilters)', status: 'success' },
                { name: 'Кнопка "Обновить"', action: 'refetch()', status: 'success' },
                { name: 'Кнопка "Экспорт"', action: 'exportToCSV()', status: 'success' },
                { name: 'Кнопка "Попробовать снова"', action: 'refetch()', status: 'success' }
            ];

            controlTests.forEach(test => {
                setTimeout(() => {
                    logResult('Управление', test.status, `${test.name}: ${test.action}`);
                }, Math.random() * 1500);
            });

            console.log('🎛️ Тестирование кнопок управления завершено');
        }

        function testFilters() {
            logResult('Фильтры', 'info', 'Тестирование фильтров...');
            
            const filterTests = [
                { name: 'Селект валютных пар', action: 'handleFilterChange("currency_pair", value)', status: 'success' },
                { name: 'Селект периода', action: 'handleFilterChange("days", parseInt(value))', status: 'success' },
                { name: 'Селект обменников', action: 'handleFilterChange("exchanger_id", value)', status: 'success' }
            ];

            filterTests.forEach(test => {
                setTimeout(() => {
                    logResult('Фильтры', test.status, `${test.name}: ${test.action}`);
                }, Math.random() * 1200);
            });

            console.log('🔍 Тестирование фильтров завершено');
        }

        function testConsoleOutput() {
            logResult('Консольное логирование', 'info', 'Тестирование вывода в консоль...');
            
            // Тестовые сообщения
            console.log('✅ Тест обычного лога');
            console.warn('⚠️ Тест предупреждения');
            console.error('❌ Тест ошибки');
            
            setTimeout(() => {
                logResult('Логирование', 'success', 'Все типы логов работают корректно');
            }, 500);
        }

        function openHistoricalPage() {
            logResult('Переход на страницу', 'info', 'Открытие страницы исторических курсов...');
            
            const url = 'http://localhost:5174/historical-rates';
            window.open(url, '_blank');
            
            logResult('Переход на страницу', 'success', `Страница открыта: ${url}`);
            console.log('🔗 Страница исторических курсов открыта в новой вкладке');
        }

        function clearConsole() {
            document.getElementById('console-output').innerHTML = 'Консоль очищена...\n';
            document.getElementById('test-results').innerHTML = '<p>Результаты тестирования очищены...</p>';
            testResults = [];
            console.log('🧹 Консоль и результаты очищены');
        }

        // Автоматическое логирование при загрузке
        window.addEventListener('load', () => {
            console.log('🚀 Страница тестирования кнопок загружена');
            logResult('Инициализация', 'success', 'Система тестирования готова к работе');
        });

        // Мониторинг активности
        let activityCount = 0;
        setInterval(() => {
            activityCount++;
            if (activityCount % 30 === 0) { // Каждые 30 секунд
                console.log(`📊 Система активна: ${activityCount} секунд`);
            }
        }, 1000);
    </script>
</body>
</html>
