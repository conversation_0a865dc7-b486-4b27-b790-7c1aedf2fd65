"""
Планировщик задач для автоматического парсинга курсов валют
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any
import schedule
import threading
import time

from services.rate_parser_service import rate_parser_service
from config.settings import settings

logger = logging.getLogger(__name__)


class SchedulerService:
    """Сервис планировщика задач для парсинга курсов"""
    
    def __init__(self):
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.loop: Optional[asyncio.AbstractEventLoop] = None
    
    def start(self):
        """Запуск планировщика задач"""
        if self.running:
            logger.warning("Scheduler already running")
            return
        
        self.running = True
        
        # Настройка расписания
        self._setup_schedule()
        
        # Запуск в отдельном потоке
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("Scheduler service started")
    
    def stop(self):
        """Остановка планировщика задач"""
        self.running = False
        schedule.clear()
        
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        
        logger.info("Scheduler service stopped")
    
    def _setup_schedule(self):
        """Настройка расписания задач"""
        # Парсинг курсов каждые 30 минут
        schedule.every(30).minutes.do(self._schedule_parsing_job)
        
        # Очистка старых логов каждый день в 2:00
        schedule.every().day.at("02:00").do(self._schedule_cleanup_job)
        
        # Проверка состояния системы каждые 5 минут
        schedule.every(5).minutes.do(self._schedule_health_check)
        
        logger.info("Scheduled tasks configured:")
        logger.info("- Rate parsing: every 30 minutes")
        logger.info("- Log cleanup: daily at 02:00")
        logger.info("- Health check: every 5 minutes")
    
    def _run_scheduler(self):
        """Основной цикл планировщика"""
        logger.info("Scheduler thread started")
        
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"Scheduler error: {e}")
                time.sleep(5)
        
        logger.info("Scheduler thread stopped")
    
    def _schedule_parsing_job(self):
        """Планирование задачи парсинга"""
        try:
            # Создание нового event loop для async задач
            if not self.loop or self.loop.is_closed():
                self.loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.loop)
            
            # Запуск парсинга
            result = self.loop.run_until_complete(rate_parser_service.parse_all_enabled_exchangers())
            
            logger.info(f"Scheduled parsing completed: {result}")
            
        except Exception as e:
            logger.error(f"Error in scheduled parsing job: {e}")
    
    def _schedule_cleanup_job(self):
        """Планирование задачи очистки старых данных"""
        try:
            if not self.loop or self.loop.is_closed():
                self.loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.loop)
            
            # Запуск очистки
            self.loop.run_until_complete(self._cleanup_old_data())
            
        except Exception as e:
            logger.error(f"Error in scheduled cleanup job: {e}")
    
    def _schedule_health_check(self):
        """Планирование проверки состояния системы"""
        try:
            if not self.loop or self.loop.is_closed():
                self.loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.loop)
            
            # Запуск проверки
            self.loop.run_until_complete(self._health_check())
            
        except Exception as e:
            logger.error(f"Error in scheduled health check: {e}")
    
    async def _cleanup_old_data(self):
        """Очистка старых данных"""
        try:
            from services.database_service import database_service
            
            # Удаление старых неактивных курсов (старше 7 дней)
            await database_service.execute(
                """
                DELETE FROM exchange_rates 
                WHERE is_active = false 
                AND created_at < NOW() - INTERVAL '7 days'
                """
            )
            
            # Удаление старых логов парсинга (старше 30 дней)
            await database_service.execute(
                """
                DELETE FROM parsing_logs 
                WHERE created_at < NOW() - INTERVAL '30 days'
                """
            )
            
            logger.info("Old data cleanup completed")
            
        except Exception as e:
            logger.error(f"Error in cleanup job: {e}")
    
    async def _health_check(self):
        """Проверка состояния системы"""
        try:
            from services.database_service import database_service
            
            # Проверка подключения к БД
            await database_service.fetch_one("SELECT 1")
            
            # Проверка последнего успешного парсинга
            last_success = await database_service.fetch_one(
                """
                SELECT created_at 
                FROM parsing_logs 
                WHERE status = 'success' 
                ORDER BY created_at DESC 
                LIMIT 1
                """
            )
            
            if last_success:
                last_success_time = last_success['created_at']
                time_diff = datetime.now() - last_success_time
                
                # Предупреждение если последний успешный парсинг был более 2 часов назад
                if time_diff > timedelta(hours=2):
                    logger.warning(f"Last successful parsing was {time_diff} ago")
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Получение статуса планировщика"""
        return {
            'running': self.running,
            'next_jobs': [
                {
                    'job': str(job.job_func),
                    'next_run': job.next_run.isoformat() if job.next_run else None
                }
                for job in schedule.jobs
            ],
            'thread_alive': self.scheduler_thread.is_alive() if self.scheduler_thread else False
        }


# Глобальный экземпляр сервиса
scheduler_service = SchedulerService()