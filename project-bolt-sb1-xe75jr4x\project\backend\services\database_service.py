"""
Сервис для работы с базой данных
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional
import asyncpg
from config.settings import settings

logger = logging.getLogger(__name__)


class DatabaseService:
    """Сервис для работы с PostgreSQL через asyncpg"""
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
    
    async def initialize(self):
        """Инициализация пула соединений с базой данных"""
        try:
            self.pool = await asyncpg.create_pool(
                settings.DATABASE_URL,
                min_size=5,
                max_size=20,
                command_timeout=60
            )
            logger.info("Database service initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize database service: {e}")
            return False
    
    async def close(self):
        """Закрытие пула соединений"""
        if self.pool:
            await self.pool.close()
            self.pool = None
    
    async def execute(self, query: str, *args) -> str:
        """Выполнение SQL запроса без возврата данных"""
        if not self.pool:
            await self.initialize()
        
        async with self.pool.acquire() as connection:
            return await connection.execute(query, *args)
    
    async def fetch_one(self, query: str, *args) -> Optional[Dict[str, Any]]:
        """Выполнение SQL запроса с возвратом одной записи"""
        if not self.pool:
            await self.initialize()
        
        async with self.pool.acquire() as connection:
            row = await connection.fetchrow(query, *args)
            return dict(row) if row else None
    
    async def fetch_all(self, query: str, *args) -> List[Dict[str, Any]]:
        """Выполнение SQL запроса с возвратом всех записей"""
        if not self.pool:
            await self.initialize()
        
        async with self.pool.acquire() as connection:
            rows = await connection.fetch(query, *args)
            return [dict(row) for row in rows]
    
    async def transaction(self, queries: List[tuple]) -> bool:
        """Выполнение нескольких запросов в транзакции"""
        if not self.pool:
            await self.initialize()
        
        async with self.pool.acquire() as connection:
            async with connection.transaction():
                try:
                    for query, args in queries:
                        await connection.execute(query, *args)
                    return True
                except Exception as e:
                    logger.error(f"Transaction failed: {e}")
                    raise


# Глобальный экземпляр сервиса
database_service = DatabaseService()