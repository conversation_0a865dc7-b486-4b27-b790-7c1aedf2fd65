"""
API routes for rate parsing management
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, Optional, List
import logging

from services.rate_parser_service import rate_parser_service
from services.scheduler_service import scheduler_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/parse/{exchanger_id}", response_model=Dict[str, Any])
async def parse_exchanger_rates(exchanger_id: str):
    """Запуск парсинга курсов для конкретного обменника"""
    try:
        # Получение конфигурации обменника из БД
        from services.database_service import database_service
        
        exchanger = await database_service.fetch_one(
            """
            SELECT id, name, website_url, parsing_config 
            FROM exchangers 
            WHERE id = $1 AND parsing_enabled = true
            """,
            exchanger_id
        )
        
        if not exchanger:
            raise HTTPException(status_code=404, detail="Exchanger not found or parsing disabled")
        
        config = {
            'website_url': exchanger['website_url'],
            'selectors': exchanger['parsing_config'].get('selectors', {}),
            'timeout': exchanger['parsing_config'].get('timeout', 30)
        }
        
        result = await rate_parser_service.parse_exchanger_rates(exchanger_id, config)
        
        return {
            'success': True,
            'data': result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error parsing exchanger {exchanger_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/parse/all", response_model=Dict[str, Any])
async def parse_all_exchangers():
    """Запуск парсинга курсов для всех обменников"""
    try:
        result = await rate_parser_service.parse_all_enabled_exchangers()
        
        return {
            'success': True,
            'data': result
        }
        
    except Exception as e:
        logger.error(f"Error parsing all exchangers: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/test-config", response_model=Dict[str, Any])
async def test_parsing_config(config_data: Dict[str, Any]):
    """Тестирование конфигурации парсинга"""
    try:
        website_url = config_data.get('website_url')
        selectors = config_data.get('selectors', {})
        
        if not website_url:
            raise HTTPException(status_code=400, detail="Website URL is required")
        
        result = await rate_parser_service.test_parsing_config(website_url, selectors)
        
        return {
            'success': True,
            'data': result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing parsing config: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/rates", response_model=Dict[str, Any])
async def get_latest_rates(exchanger_id: Optional[str] = None):
    """Получение последних курсов валют"""
    try:
        rates = await rate_parser_service.get_latest_rates(exchanger_id)
        
        return {
            'success': True,
            'data': {
                'rates': rates,
                'total_count': len(rates)
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting latest rates: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/logs", response_model=Dict[str, Any])
async def get_parsing_logs(
    exchanger_id: Optional[str] = None,
    limit: int = 50
):
    """Получение логов парсинга"""
    try:
        logs = await rate_parser_service.get_parsing_logs(exchanger_id, limit)
        
        return {
            'success': True,
            'data': {
                'logs': logs,
                'total_count': len(logs)
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting parsing logs: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/scheduler/status", response_model=Dict[str, Any])
async def get_scheduler_status():
    """Получение статуса планировщика задач"""
    try:
        status = scheduler_service.get_status()
        
        return {
            'success': True,
            'data': status
        }
        
    except Exception as e:
        logger.error(f"Error getting scheduler status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/scheduler/start", response_model=Dict[str, Any])
async def start_scheduler():
    """Запуск планировщика задач"""
    try:
        scheduler_service.start()
        
        return {
            'success': True,
            'message': 'Scheduler started successfully'
        }
        
    except Exception as e:
        logger.error(f"Error starting scheduler: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/scheduler/stop", response_model=Dict[str, Any])
async def stop_scheduler():
    """Остановка планировщика задач"""
    try:
        scheduler_service.stop()
        
        return {
            'success': True,
            'message': 'Scheduler stopped successfully'
        }
        
    except Exception as e:
        logger.error(f"Error stopping scheduler: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")