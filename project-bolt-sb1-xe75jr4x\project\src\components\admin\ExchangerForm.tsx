import React, { useState } from 'react';
import { Plus, Trash2, MapPin, Clock, Phone, Globe, Settings, AlertCircle, CheckCircle, X, RefreshCw } from 'lucide-react';
import { ExchangerFormData, AdditionalOffice, ParsingConfig } from '../../types/exchanger';
import { districts } from '../../data/mockData';
import { cities } from '../../data/cities';

interface ExchangerFormProps {
  initialData?: Partial<ExchangerFormData>;
  onSubmit: (data: ExchangerFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

const ExchangerForm: React.FC<ExchangerFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  mode
}) => {
  const [formData, setFormData] = useState<ExchangerFormData>({
    name: initialData?.name || '',
    address: initialData?.address || '',
    district: initialData?.district || 'patong',
    city: initialData?.city || 'phuket',
    phone: initialData?.phone || '',
    hours: initialData?.hours || '9:00 - 20:00',
    websiteUrl: initialData?.websiteUrl || '',
    parsingEnabled: initialData?.parsingEnabled || false,
    parsingConfig: initialData?.parsingConfig || {
      enabled: false,
      selectors: {},
      updateInterval: 60,
      retryAttempts: 3,
      timeout: 30
    },
    coordinates: initialData?.coordinates || undefined,
    additionalOffices: initialData?.additionalOffices || [],
    status: initialData?.status || 'pending'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showParsingConfig, setShowParsingConfig] = useState(false);
  const [testingSelectors, setTestingSelectors] = useState(false);
  const [selectorTestResults, setSelectorTestResults] = useState<Record<string, boolean>>({});

  // Валидация формы
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Основные поля
    if (!formData.name.trim()) {
      newErrors.name = 'Название обязательно';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Название должно содержать минимум 3 символа';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Адрес обязателен';
    }

    if (!formData.city) {
      newErrors.city = 'Город обязателен';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Телефон обязателен';
    } else if (!/^\+?[\d\s\-\(\)]{10,}$/.test(formData.phone)) {
      newErrors.phone = 'Некорректный формат телефона';
    }

    if (!formData.hours.trim()) {
      newErrors.hours = 'Часы работы обязательны';
    }

    // Валидация URL сайта
    if (formData.websiteUrl && formData.websiteUrl.trim()) {
      try {
        new URL(formData.websiteUrl);
      } catch {
        newErrors.websiteUrl = 'Некорректный URL сайта';
      }
    }

    // Валидация дополнительных офисов
    formData.additionalOffices.forEach((office, index) => {
      if (!office.name.trim()) {
        newErrors[`office_${index}_name`] = 'Название офиса обязательно';
      }
      if (!office.address.trim()) {
        newErrors[`office_${index}_address`] = 'Адрес офиса обязателен';
      }
      if (!office.phone.trim()) {
        newErrors[`office_${index}_phone`] = 'Телефон офиса обязателен';
      } else if (!/^\+?[\d\s\-\(\)]{10,}$/.test(office.phone)) {
        newErrors[`office_${index}_phone`] = 'Некорректный формат телефона';
      }
    });

    // Валидация конфигурации парсинга
    if (formData.parsingEnabled) {
      if (!formData.websiteUrl) {
        newErrors.websiteUrl = 'URL сайта обязателен для парсинга';
      }
      if (formData.parsingConfig.updateInterval < 15) {
        newErrors.parsingInterval = 'Интервал обновления не может быть меньше 15 минут';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const testParsingSelectors = async () => {
    if (!formData.websiteUrl) {
      setErrors(prev => ({ ...prev, websiteUrl: 'URL сайта обязателен для тестирования' }));
      return;
    }

    setTestingSelectors(true);
    setSelectorTestResults({});

    try {
      // Симуляция тестирования селекторов
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const results: Record<string, boolean> = {};
      Object.entries(formData.parsingConfig.selectors).forEach(([key, selector]) => {
        if (selector) {
          // Симуляция результата тестирования (80% успеха)
          results[key] = Math.random() > 0.2;
        }
      });
      
      setSelectorTestResults(results);
      
      const successCount = Object.values(results).filter(Boolean).length;
      const totalCount = Object.keys(results).length;
      
      if (successCount === totalCount) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.selectors;
          return newErrors;
        });
      } else {
        setErrors(prev => ({ 
          ...prev, 
          selectors: `Найдено ${successCount} из ${totalCount} селекторов` 
        }));
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, selectors: 'Ошибка тестирования селекторов' }));
    } finally {
      setTestingSelectors(false);
    }
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const addAdditionalOffice = () => {
    const newOffice: AdditionalOffice = {
      name: '',
      address: '',
      district: 'patong',
      phone: '',
      hours: '9:00 - 20:00',
      status: 'active'
    };
    
    setFormData(prev => ({
      ...prev,
      additionalOffices: [...prev.additionalOffices, newOffice]
    }));
  };

  const removeAdditionalOffice = (index: number) => {
    setFormData(prev => ({
      ...prev,
      additionalOffices: prev.additionalOffices.filter((_, i) => i !== index)
    }));
  };

  const updateAdditionalOffice = (index: number, field: keyof AdditionalOffice, value: any) => {
    setFormData(prev => ({
      ...prev,
      additionalOffices: prev.additionalOffices.map((office, i) => 
        i === index ? { ...office, [field]: value } : office
      )
    }));
  };

  const updateParsingConfig = (field: keyof ParsingConfig, value: any) => {
    setFormData(prev => ({
      ...prev,
      parsingConfig: {
        ...prev.parsingConfig,
        [field]: value
      }
    }));
  };

  const updateParsingSelector = (currency: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      parsingConfig: {
        ...prev.parsingConfig,
        selectors: {
          ...prev.parsingConfig.selectors,
          [currency]: value
        }
      }
    }));
  };

  return (
    <div className="w-full max-w-5xl mx-auto bg-white rounded-xl shadow-2xl border border-gray-200 p-6 max-h-[90vh] overflow-y-auto">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          {mode === 'create' ? 'Создать обменник' : 'Редактировать обменник'}
        </h2>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-gray-600 transition-colors"
          disabled={isLoading}
        >
          <X className="w-6 h-6" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Основная информация */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Основная информация</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Название обменника *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Русский обменник №1"
                disabled={isLoading}
                required
              />
              {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Город *
              </label>
              <select
                value={formData.city}
                onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors"
                disabled={isLoading}
                required
              >
                <option value="">Выберите город</option>
                {cities.map(city => (
                  <option key={city.id} value={city.id}>{city.name}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Район
              </label>
              <select
                value={formData.district}
                onChange={(e) => setFormData(prev => ({ ...prev, district: e.target.value }))}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors"
                disabled={isLoading}
              >
                {districts.map(district => (
                  <option key={district.id} value={district.id}>{district.name}</option>
                ))}
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Адрес *
              </label>
              <input
                type="text"
                value={formData.address}
                onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors ${
                  errors.address ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Патонг Бич Роуд, 123"
                disabled={isLoading}
                required
              />
              {errors.address && <p className="text-red-600 text-sm mt-1">{errors.address}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Телефон *
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors ${
                  errors.phone ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="+66 89 123 4567"
                disabled={isLoading}
                required
              />
              {errors.phone && <p className="text-red-600 text-sm mt-1">{errors.phone}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Часы работы *
              </label>
              <input
                type="text"
                value={formData.hours}
                onChange={(e) => setFormData(prev => ({ ...prev, hours: e.target.value }))}
                className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors ${
                  errors.hours ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="9:00 - 20:00"
                disabled={isLoading}
                required
              />
              {errors.hours && <p className="text-red-600 text-sm mt-1">{errors.hours}</p>}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                URL сайта
              </label>
              <input
                type="url"
                value={formData.websiteUrl}
                onChange={(e) => setFormData(prev => ({ ...prev, websiteUrl: e.target.value }))}
                className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors ${
                  errors.websiteUrl ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="https://example-exchange.com"
                disabled={isLoading}
              />
              {errors.websiteUrl && <p className="text-red-600 text-sm mt-1">{errors.websiteUrl}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Статус
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors"
                disabled={isLoading}
              >
                <option value="pending">На модерации</option>
                <option value="active">Активный</option>
                <option value="inactive">Неактивный</option>
              </select>
            </div>
          </div>
        </div>

        {/* Автоматический парсинг курсов */}
        <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center">
              <Settings className="w-5 h-5 mr-2 text-blue-600" />
              Автоматический парсинг курсов
            </h3>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={formData.parsingEnabled}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, parsingEnabled: e.target.checked }));
                  if (e.target.checked && formData.websiteUrl) {
                    setShowParsingConfig(true);
                  }
                }}
                className="sr-only peer"
                disabled={isLoading}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {formData.parsingEnabled && (
            <div className="space-y-4">
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <h4 className="font-medium text-gray-800 mb-3">Настройки парсинга</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Интервал обновления (минуты)
                    </label>
                    <input
                      type="number"
                      min="15"
                      max="1440"
                      value={formData.parsingConfig.updateInterval}
                      onChange={(e) => updateParsingConfig('updateInterval', parseInt(e.target.value))}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      disabled={isLoading}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Количество попыток
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="10"
                      value={formData.parsingConfig.retryAttempts}
                      onChange={(e) => updateParsingConfig('retryAttempts', parseInt(e.target.value))}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      disabled={isLoading}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Таймаут (секунды)
                    </label>
                    <input
                      type="number"
                      min="10"
                      max="120"
                      value={formData.parsingConfig.timeout}
                      onChange={(e) => updateParsingConfig('timeout', parseInt(e.target.value))}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* CSS селекторы для парсинга */}
                <div className="mt-4">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-medium text-gray-800">CSS селекторы для курсов</h5>
                    <button
                      type="button"
                      onClick={testParsingSelectors}
                      disabled={testingSelectors || !formData.websiteUrl}
                      className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm flex items-center space-x-1"
                    >
                      {testingSelectors ? (
                        <RefreshCw className="w-3 h-3 animate-spin" />
                      ) : (
                        <CheckCircle className="w-3 h-3" />
                      )}
                      <span>{testingSelectors ? 'Тестирование...' : 'Тест'}</span>
                    </button>
                  </div>
                  
                  {errors.selectors && (
                    <div className="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                      {errors.selectors}
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      { key: 'thbRubBuy', label: 'THB/RUB Покупка', placeholder: '.rate-thb-rub-buy' },
                      { key: 'thbRubSell', label: 'THB/RUB Продажа', placeholder: '.rate-thb-rub-sell' },
                      { key: 'usdtBahtBuy', label: 'USDT/BAHT Покупка', placeholder: '.rate-usdt-baht-buy' },
                      { key: 'usdtBahtSell', label: 'USDT/BAHT Продажа', placeholder: '.rate-usdt-baht-sell' }
                    ].map(({ key, label, placeholder }) => (
                      <div key={key}>
                        <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center justify-between">
                          <span>{label}</span>
                          {selectorTestResults[key] !== undefined && (
                            <span className={`text-xs px-2 py-1 rounded ${
                              selectorTestResults[key] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {selectorTestResults[key] ? '✓ Найден' : '✗ Не найден'}
                            </span>
                          )}
                        </label>
                        <input
                          type="text"
                          value={formData.parsingConfig.selectors[key as keyof typeof formData.parsingConfig.selectors] || ''}
                          onChange={(e) => updateParsingSelector(key, e.target.value)}
                          className={`w-full p-2 border rounded text-sm font-mono transition-colors ${
                            selectorTestResults[key] === false ? 'border-red-300' : 
                            selectorTestResults[key] === true ? 'border-green-300' : 'border-gray-300'
                          }`}
                          placeholder={placeholder}
                          disabled={isLoading || testingSelectors}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Дополнительные офисы */}
        <div className="bg-green-50 rounded-lg p-6 border border-green-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center">
              <MapPin className="w-5 h-5 mr-2 text-green-600" />
              Дополнительные офисы ({formData.additionalOffices.length})
            </h3>
            <button
              type="button"
              onClick={addAdditionalOffice}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              disabled={isLoading}
            >
              <Plus className="w-4 h-4" />
              <span>Добавить офис</span>
            </button>
          </div>

          {formData.additionalOffices.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MapPin className="w-12 h-12 mx-auto mb-2 text-gray-400" />
              <p>Дополнительные офисы не добавлены</p>
              <p className="text-sm">Нажмите "Добавить офис" для создания филиалов</p>
            </div>
          ) : (
            <div className="space-y-4">
              {formData.additionalOffices.map((office, index) => (
                <div key={index} className="bg-white rounded-lg p-4 border border-gray-200">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-800">Офис #{index + 1}</h4>
                    <button
                      type="button"
                      onClick={() => removeAdditionalOffice(index)}
                      className="text-red-600 hover:text-red-800 transition-colors"
                      disabled={isLoading}
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Название офиса *
                      </label>
                      <input
                        type="text"
                        value={office.name}
                        onChange={(e) => updateAdditionalOffice(index, 'name', e.target.value)}
                        className={`w-full p-2 border rounded focus:ring-2 focus:ring-green-500 text-sm ${
                          errors[`office_${index}_name`] ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Филиал Карон"
                        disabled={isLoading}
                        required
                      />
                      {errors[`office_${index}_name`] && (
                        <p className="text-red-600 text-xs mt-1">{errors[`office_${index}_name`]}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Район
                      </label>
                      <select
                        value={office.district}
                        onChange={(e) => updateAdditionalOffice(index, 'district', e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-green-500 text-sm"
                        disabled={isLoading}
                      >
                        {districts.map(district => (
                          <option key={district.id} value={district.id}>{district.name}</option>
                        ))}
                      </select>
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Адрес *
                      </label>
                      <input
                        type="text"
                        value={office.address}
                        onChange={(e) => updateAdditionalOffice(index, 'address', e.target.value)}
                        className={`w-full p-2 border rounded focus:ring-2 focus:ring-green-500 text-sm ${
                          errors[`office_${index}_address`] ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Карон Бич Роуд, 567"
                        disabled={isLoading}
                        required
                      />
                      {errors[`office_${index}_address`] && (
                        <p className="text-red-600 text-xs mt-1">{errors[`office_${index}_address`]}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Телефон *
                      </label>
                      <input
                        type="tel"
                        value={office.phone}
                        onChange={(e) => updateAdditionalOffice(index, 'phone', e.target.value)}
                        className={`w-full p-2 border rounded focus:ring-2 focus:ring-green-500 text-sm ${
                          errors[`office_${index}_phone`] ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="+66 89 123 4568"
                        disabled={isLoading}
                        required
                      />
                      {errors[`office_${index}_phone`] && (
                        <p className="text-red-600 text-xs mt-1">{errors[`office_${index}_phone`]}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Часы работы
                      </label>
                      <input
                        type="text"
                        value={office.hours}
                        onChange={(e) => updateAdditionalOffice(index, 'hours', e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-green-500 text-sm"
                        placeholder="9:30 - 20:30"
                        disabled={isLoading}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Статус офиса
                      </label>
                      <select
                        value={office.status}
                        onChange={(e) => updateAdditionalOffice(index, 'status', e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-green-500 text-sm"
                        disabled={isLoading}
                      >
                        <option value="active">Активный</option>
                        <option value="inactive">Неактивный</option>
                      </select>
                    </div>
                    
                    {/* Координаты офиса */}
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Координаты (опционально)
                      </label>
                      <div className="grid grid-cols-2 gap-2">
                        <input
                          type="number"
                          step="any"
                          value={office.coordinates?.lat || ''}
                          onChange={(e) => updateAdditionalOffice(index, 'coordinates', {
                            ...office.coordinates,
                            lat: parseFloat(e.target.value) || 0
                          })}
                          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-green-500 text-sm"
                          placeholder="Широта (7.8963)"
                          disabled={isLoading}
                        />
                        <input
                          type="number"
                          step="any"
                          value={office.coordinates?.lng || ''}
                          onChange={(e) => updateAdditionalOffice(index, 'coordinates', {
                            ...office.coordinates,
                            lng: parseFloat(e.target.value) || 0
                          })}
                          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-green-500 text-sm"
                          placeholder="Долгота (98.2964)"
                          disabled={isLoading}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Кнопки действий */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <div className="flex-1 text-sm text-gray-500">
            * Обязательные поля
          </div>
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200"
            disabled={isLoading}
          >
            Отмена
          </button>
          <button
            type="submit"
            disabled={isLoading || Object.keys(errors).length > 0}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:shadow-md"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Сохранение...</span>
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4" />
                <span>{mode === 'create' ? 'Создать обменник' : 'Сохранить изменения'}</span>
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ExchangerForm;