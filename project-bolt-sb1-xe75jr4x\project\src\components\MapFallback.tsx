import React from 'react';
import { MapPin, Navigation, Phone, Clock, Star, AlertTriangle, ExternalLink } from 'lucide-react';
import { Exchanger } from '../types';

interface MapFallbackProps {
  exchangers: Exchanger[];
  searchQuery: string;
  selectedExchanger: number | null;
  onExchangerSelect: (id: number | null) => void;
  onViewReviews?: (exchangerId: number) => void;
}

const MapFallback: React.FC<MapFallbackProps> = ({
  exchangers,
  searchQuery,
  selectedExchanger,
  onExchangerSelect,
  onViewReviews
}) => {
  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-3 h-3 ${
              i < Math.floor(rating) 
                ? 'text-yellow-400 fill-current' 
                : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-xs text-gray-600">
          {rating.toFixed(1)}
        </span>
      </div>
    );
  };

  const openInGoogleMaps = (exchanger: Exchanger) => {
    const url = `https://www.google.com/maps/search/?api=1&query=${exchanger.coordinates.lat},${exchanger.coordinates.lng}`;
    window.open(url, '_blank');
  };

  return (
    <div className="space-y-6">
      {/* Fallback Notice */}
      <div className="bg-amber-50 border border-amber-200 rounded-xl p-6">
        <div className="flex items-center space-x-3 mb-4">
          <AlertTriangle className="w-6 h-6 text-amber-500" />
          <h3 className="text-lg font-semibold text-amber-800">Карта временно недоступна</h3>
        </div>
        <p className="text-amber-700 mb-4">
          Интерактивная карта не может быть загружена. Ниже представлен список всех обменников с возможностью открыть их местоположение в Google Maps.
        </p>
        <div className="text-sm text-amber-600">
          <p>• Проверьте подключение к интернету</p>
          <p>• Убедитесь, что Google Maps доступен в вашем регионе</p>
          <p>• Попробуйте обновить страницу</p>
        </div>
      </div>

      {/* Exchangers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {exchangers.map(exchanger => {
          const rubRate = exchanger.rates.find(r => r.currency === 'RUB');
          
          return (
            <div
              key={exchanger.id}
              className={`bg-white rounded-xl shadow-lg border transition-all duration-200 cursor-pointer ${
                selectedExchanger === exchanger.id
                  ? 'border-blue-500 shadow-xl'
                  : 'border-gray-100 hover:shadow-xl hover:border-gray-200'
              }`}
              onClick={() => onExchangerSelect(exchanger.id === selectedExchanger ? null : exchanger.id)}
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-800 leading-tight">
                    {exchanger.name}
                  </h3>
                  <MapPin className="w-5 h-5 text-blue-500 flex-shrink-0" />
                </div>

                {/* Rating */}
                <div className="mb-4">
                  {renderStars(exchanger.rating)}
                </div>

                {/* Contact Info */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-start space-x-3 text-sm text-gray-600">
                    <MapPin className="w-4 h-4 text-gray-400 flex-shrink-0 mt-0.5" />
                    <span className="leading-relaxed">{exchanger.address}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3 text-sm text-gray-600">
                    <Phone className="w-4 h-4 text-gray-400 flex-shrink-0" />
                    <span>{exchanger.phone}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3 text-sm text-gray-600">
                    <Clock className="w-4 h-4 text-gray-400 flex-shrink-0" />
                    <span>{exchanger.hours}</span>
                  </div>
                </div>

                {/* Rates */}
                {rubRate && (
                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <div className="text-xs text-gray-500 mb-2 font-medium">Курс RUB</div>
                    <div className="flex justify-between items-center">
                      <div className="text-center">
                        <div className="text-xs text-gray-500 mb-1">Покупка</div>
                        <div className="text-lg font-bold text-green-600">
                          {rubRate.buy.toFixed(2)}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-xs text-gray-500 mb-1">Продажа</div>
                        <div className="text-lg font-bold text-red-600">
                          {rubRate.sell.toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      openInGoogleMaps(exchanger);
                    }}
                    className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                  >
                    <ExternalLink className="w-4 h-4" />
                    <span>Открыть в Maps</span>
                  </button>
                  
                  {onViewReviews && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onViewReviews(exchanger.id);
                      }}
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium"
                    >
                      Отзывы
                    </button>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* No Results */}
      {exchangers.length === 0 && (
        <div className="text-center py-12 bg-white rounded-xl shadow-lg border border-gray-100">
          <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <div className="text-gray-500 text-lg">
            Обменники не найдены
          </div>
          <p className="text-gray-400 mt-2">
            Попробуйте изменить параметры поиска или фильтры
          </p>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <h4 className="text-lg font-semibold text-blue-800 mb-3">Как найти обменник</h4>
        <div className="space-y-2 text-blue-700">
          <p>• Нажмите "Открыть в Maps" чтобы увидеть точное местоположение</p>
          <p>• Используйте навигацию Google Maps для построения маршрута</p>
          <p>• Сохраните адрес или номер телефона для связи</p>
          <p>• Проверьте часы работы перед посещением</p>
        </div>
      </div>
    </div>
  );
};

export default MapFallback;
