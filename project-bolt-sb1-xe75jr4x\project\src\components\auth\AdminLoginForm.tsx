import React, { useState } from 'react';
import { User, Lock, Eye, EyeOff, AlertCircle, LogIn, Shield, ArrowLeft } from 'lucide-react';
import { adminAuthService } from '../../services/adminAuthService';
import { config } from '../../config/environment';

interface AdminLoginFormProps {
  onSuccess: () => void;
}

const AdminLoginForm: React.FC<AdminLoginFormProps> = ({ onSuccess }) => {
  const [formData, setFormData] = useState({
    login: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [attempts, setAttempts] = useState(0);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      // Rate limiting: max 5 attempts
      if (attempts >= 5) {
        throw new Error('Слишком много неудачных попыток. Попробуйте позже.');
      }

      await adminAuthService.login({
        login: formData.login,
        password: formData.password,
      });

      // Reset attempts on successful login
      setAttempts(0);
      onSuccess();
    } catch (err: any) {
      console.error('Admin login error:', err);
      setAttempts(prev => prev + 1);
      
      if (err.message.includes('Неверный логин или пароль')) {
        setError(`Неверный логин или пароль. Попытка ${attempts + 1} из 5.`);
      } else {
        setError(err.message || 'Ошибка входа в систему');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(''); // Clear error when user starts typing
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 relative">
        {/* Back to main site */}
        <div className="absolute top-4 left-4">
          <a
            href="/"
            className="text-gray-400 hover:text-gray-600 transition-colors flex items-center space-x-1 text-sm"
            title="Вернуться на главную страницу"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Главная</span>
          </a>
        </div>

        {/* Header */}
        <div className="text-center mb-8 mt-6">
          <div className="bg-gradient-to-br from-red-500 to-orange-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Административный вход</h1>
          <p className="text-gray-600">Обменники Таиланда - Панель управления</p>
        </div>

        {/* Demo credentials info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center space-x-2 text-blue-800 mb-2">
            <Shield className="w-4 h-4" />
            <span className="font-medium text-sm">Демо-доступ</span>
          </div>
          <div className="text-blue-700 text-sm space-y-1">
            <div><strong>Логин:</strong> admin</div>
            <div><strong>Пароль:</strong> admin123</div>
          </div>
          <p className="text-blue-600 text-xs mt-2">
            Используйте эти учетные данные для доступа к административной панели
          </p>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Login Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Логин администратора
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={formData.login}
                onChange={(e) => handleInputChange('login', e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                placeholder="admin"
                required
                disabled={isLoading || attempts >= 5}
                autoComplete="username"
                maxLength={50}
              />
            </div>
          </div>

          {/* Password Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Пароль
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                placeholder="Введите пароль"
                required
                disabled={isLoading || attempts >= 5}
                autoComplete="current-password"
                minLength={6}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                disabled={isLoading || attempts >= 5}
                tabIndex={-1}
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
              <span className="text-red-700 text-sm">{error}</span>
            </div>
          )}

          {/* Rate limiting warning */}
          {attempts >= 3 && attempts < 5 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-yellow-500 flex-shrink-0" />
              <span className="text-yellow-700 text-sm">
                Внимание: осталось {5 - attempts} попыток входа
              </span>
            </div>
          )}

          {/* Blocked message */}
          {attempts >= 5 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
              <span className="text-red-700 text-sm">
                Доступ временно заблокирован. Обновите страницу и попробуйте снова.
              </span>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading || !formData.login || !formData.password || attempts >= 5}
            className="w-full bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed text-white py-3 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Проверка...</span>
              </>
            ) : (
              <>
                <LogIn className="w-5 h-5" />
                <span>Войти в админку</span>
              </>
            )}
          </button>
        </form>

        {/* Security Notice */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex items-center space-x-2 text-gray-700">
            <Shield className="w-4 h-4" />
            <span className="text-sm font-medium">Безопасность</span>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Сессия администратора действительна 8 часов. Все действия логируются.
          </p>
        </div>

        {/* Development info */}
        {config.APP_ENV === 'development' && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 text-xs">
              <strong>Режим разработки:</strong> Используются жестко заданные учетные данные
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminLoginForm;