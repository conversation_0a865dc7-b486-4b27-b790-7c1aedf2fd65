// Типы для системы управления обменниками
export interface AdditionalOffice {
  id?: string;
  exchangerId?: string;
  name: string;
  address: string;
  district: string;
  phone: string;
  hours: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  status: 'active' | 'inactive';
  createdAt?: string;
  updatedAt?: string;
}

export interface ExchangerFormData {
  name: string;
  address: string;
  district: string;
  city?: string;
  phone: string;
  hours: string;
  websiteUrl?: string;
  parsingEnabled: boolean;
  parsingConfig: ParsingConfig;
  coordinates?: {
    lat: number;
    lng: number;
  };
  additionalOffices: AdditionalOffice[];
  status: 'active' | 'inactive' | 'pending';
}

export interface ParsingConfig {
  enabled: boolean;
  selectors: {
    thbRubBuy?: string;
    thbRubSell?: string;
    usdtBahtBuy?: string;
    usdtBahtSell?: string;
  };
  updateInterval: number; // в минутах
  retryAttempts: number;
  timeout: number; // в секундах
}

export interface ExchangeRate {
  id?: string;
  exchangerId: string;
  currencyPair: 'THB/RUB' | 'USDT/BAHT' | 'USD/THB' | 'RUB/THB';
  buyRate: number;
  sellRate: number;
  sourceUrl?: string;
  parsedAt: string;
  isActive: boolean;
  createdAt: string;
}

export interface ParsingLog {
  id?: string;
  exchangerId?: string;
  status: 'success' | 'error' | 'warning' | 'skipped';
  message: string;
  errorDetails?: any;
  parsedRatesCount: number;
  executionTimeMs: number;
  createdAt: string;
}

export interface Exchanger {
  id?: string;
  name: string;
  websiteUrl?: string;
  parsingEnabled: boolean;
  parsingConfig: ParsingConfig;
  lastParsedAt?: string;
  address: string;
  district: string;
  phone: string;
  hours: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  status: 'active' | 'inactive' | 'pending';
  additionalOffices?: AdditionalOffice[];
  currentRates?: ExchangeRate[];
  createdAt?: string;
  updatedAt?: string;
}