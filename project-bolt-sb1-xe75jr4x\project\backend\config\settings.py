"""
Configuration settings for the currency exchange platform
"""
import os
from typing import List, Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Application settings
    APP_NAME: str = "Thailand Exchange Platform"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # Database settings
    DATABASE_URL: str = "sqlite:///./thailand_exchange.db"
    REDIS_URL: str = "redis://localhost:6379"

    # Google Sheets API settings
    GOOGLE_SHEETS_CREDENTIALS_FILE: str = "./credentials/google-sheets-credentials.json"
    GOOGLE_SHEETS_SPREADSHEET_ID: str = "development-fallback-mode"
    GOOGLE_SHEETS_WORKSHEET_NAME: str = "Exchange Rates"

    # API settings
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = "development-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # CORS settings
    BACKEND_CORS_ORIGINS: str = "http://localhost:3000,http://localhost:5173,http://localhost:5174"
    
    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    
    # Cache settings
    CACHE_TTL_SECONDS: int = 300  # 5 minutes
    RATES_UPDATE_INTERVAL_MINUTES: int = 15
    
    # Monitoring
    SENTRY_DSN: Optional[str] = None
    LOG_LEVEL: str = "INFO"
    
    @property
    def cors_origins(self) -> List[str]:
        """Parse CORS origins from string"""
        if isinstance(self.BACKEND_CORS_ORIGINS, str):
            return [origin.strip() for origin in self.BACKEND_CORS_ORIGINS.split(",")]
        return self.BACKEND_CORS_ORIGINS

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()