# Пример файла переменных окружения для продакшна
# Скопируйте этот файл в .env и заполните реальными значениями

# Основные настройки приложения
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production

# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# API Configuration
VITE_API_BASE_URL=https://api.thailand-exchange.com/api
VITE_API_TIMEOUT=10000

# Аутентификация и безопасность
VITE_JWT_SECRET=your-super-secret-jwt-key-here
VITE_ENCRYPTION_KEY=your-encryption-key-here

# Внешние сервисы
VITE_SENTRY_DSN=https://<EMAIL>/project-id
VITE_GA_ID=GA-XXXXXXXXX-X

# Google Maps Configuration (Required for map functionality)
# Get your API key from https://console.cloud.google.com/
# Enable the following APIs: Maps JavaScript API, Places API, Geocoding API
VITE_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Legacy Map Support (Optional)
VITE_MAPBOX_TOKEN=pk.your-mapbox-token-here

# База данных (для бэкенда)
DATABASE_URL=postgresql://username:password@localhost:5432/thailand_exchange
REDIS_URL=redis://localhost:6379

# Email сервис
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Курсы валют
GOOGLE_SHEETS_API_KEY=your-google-sheets-api-key
EXCHANGE_RATES_API_KEY=your-exchange-rates-api-key

# Файловое хранилище
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=thailand-exchange-files
AWS_REGION=ap-southeast-1

# Мониторинг и логирование
LOG_LEVEL=error
ENABLE_ANALYTICS=true
ENABLE_ERROR_REPORTING=true

# Социальные сети и интеграции
FACEBOOK_APP_ID=your-facebook-app-id
GOOGLE_CLIENT_ID=your-google-client-id
TELEGRAM_BOT_TOKEN=your-telegram-bot-token

# Настройки кэширования
CACHE_TTL=900000
REDIS_TTL=3600

# Лимиты и ограничения
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100
MAX_FILE_SIZE=5242880

# Настройки безопасности
CORS_ORIGIN=https://thailand-exchange.com
COOKIE_SECURE=true
COOKIE_SAME_SITE=strict

# Webhook URLs
WEBHOOK_SECRET=your-webhook-secret
PAYMENT_WEBHOOK_URL=https://api.thailand-exchange.com/webhooks/payment

# Локализация
DEFAULT_LOCALE=ru
SUPPORTED_LOCALES=ru,en,th

# Настройки производительности
MAX_CONCURRENT_REQUESTS=50
REQUEST_TIMEOUT=30000
CONNECTION_POOL_SIZE=20