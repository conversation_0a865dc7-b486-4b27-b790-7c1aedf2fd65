# Google Sheets API Setup Guide

## 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the following APIs:
   - Google Sheets API
   - Google Drive API

## 2. Create Service Account

1. Go to **IAM & Admin** > **Service Accounts**
2. Click **Create Service Account**
3. Fill in the details:
   - Name: `thailand-exchange-sheets`
   - Description: `Service account for accessing exchange rates spreadsheet`
4. Click **Create and Continue**
5. Grant roles:
   - **Viewer** (for reading sheets)
6. Click **Done**

## 3. Generate Credentials

1. Click on the created service account
2. Go to **Keys** tab
3. Click **Add Key** > **Create New Key**
4. Select **JSON** format
5. Download the JSON file
6. Rename it to `google-sheets-credentials.json`
7. Place it in `backend/credentials/` directory

## 4. Create Google Sheets Document

### 4.1 Create Spreadsheet
1. Go to [Google Sheets](https://sheets.google.com)
2. Create a new spreadsheet
3. Name it "Thailand Exchange Rates"

### 4.2 Setup Worksheet Structure
Create a worksheet named "Exchange Rates" with the following columns:

| Column | Description | Example |
|--------|-------------|---------|
| A | Exchanger Name | Русский обменник №1 |
| B | District | patong |
| C | Address | Патонг Бич Роуд, 123 |
| D | Phone | +66 89 123 4567 |
| E | Hours | 9:00 - 21:00 |
| F | RUB_BUY | 36.2 |
| G | RUB_SELL | 37.8 |
| H | USD_BUY | 34.5 |
| I | USD_SELL | 35.2 |
| J | USDT_BUY | 34.8 |
| K | USDT_SELL | 35.0 |
| L | THB_RUB_BUY | 2.45 |
| M | THB_RUB_SELL | 2.52 |
| N | THB_USDT_BUY | 0.028 |
| O | THB_USDT_SELL | 0.029 |
| P | USDT_BAHT_BUY | 34.8 |
| Q | USDT_BAHT_SELL | 35.2 |

### 4.3 Sample Data
Add the header row and at least 5-10 sample exchangers with realistic data.

### 4.4 Share with Service Account
1. Click **Share** button in Google Sheets
2. Add the service account email (found in the JSON credentials file)
3. Give **Viewer** permissions
4. Click **Send**

## 5. Get Spreadsheet ID

The spreadsheet ID is in the URL:
```
https://docs.google.com/spreadsheets/d/[SPREADSHEET_ID]/edit
```

Copy this ID and add it to your `.env` file.

## 6. Environment Configuration

Update your `backend/.env` file:

```env
GOOGLE_SHEETS_CREDENTIALS_FILE="./credentials/google-sheets-credentials.json"
GOOGLE_SHEETS_SPREADSHEET_ID="your-spreadsheet-id-here"
GOOGLE_SHEETS_WORKSHEET_NAME="Exchange Rates"
```

## 7. Test Connection

Run the backend server and test the connection:

```bash
cd backend
python -m uvicorn main:app --reload
```

Then visit: `http://localhost:8000/api/v1/admin/sheets/test`

## 8. Security Best Practices

1. **Never commit credentials to git**
   - Add `credentials/` to `.gitignore`
   - Use environment variables in production

2. **Limit service account permissions**
   - Only grant necessary permissions
   - Use principle of least privilege

3. **Rotate credentials regularly**
   - Generate new keys periodically
   - Revoke old keys

4. **Monitor usage**
   - Check Google Cloud Console for API usage
   - Set up alerts for unusual activity

## 9. Production Deployment

For production:

1. Store credentials securely (e.g., AWS Secrets Manager, Azure Key Vault)
2. Use environment variables instead of files
3. Set up proper monitoring and logging
4. Configure rate limiting and quotas

## 10. Troubleshooting

### Common Issues:

1. **403 Forbidden Error**
   - Check if service account has access to the spreadsheet
   - Verify APIs are enabled in Google Cloud Console

2. **404 Not Found Error**
   - Check spreadsheet ID is correct
   - Verify worksheet name matches exactly

3. **Credentials Error**
   - Ensure JSON file path is correct
   - Check file permissions

4. **Rate Limiting**
   - Google Sheets API has quotas
   - Implement proper caching
   - Consider upgrading quota if needed

### Debug Commands:

```bash
# Test Google Sheets connection
curl http://localhost:8000/api/v1/admin/sheets/test

# Get system status
curl http://localhost:8000/api/v1/admin/status

# Force update rates
curl -X POST http://localhost:8000/api/v1/admin/rates/update
```