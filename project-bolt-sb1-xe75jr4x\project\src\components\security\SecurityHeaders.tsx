import { useEffect } from 'react';

// Component to set security headers and CSP
const SecurityHeaders: React.FC = () => {
  useEffect(() => {
    // Set Content Security Policy
    const csp = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' https://www.googletagmanager.com https://www.google-analytics.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://api.thailand-exchange.com wss: https://www.google-analytics.com",
      "frame-src 'none'",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
    ].join('; ');

    // Create meta tag for CSP
    const metaCSP = document.createElement('meta');
    metaCSP.httpEquiv = 'Content-Security-Policy';
    metaCSP.content = csp;
    document.head.appendChild(metaCSP);

    // Set other security headers via meta tags
    const securityHeaders = [
      { name: 'X-Content-Type-Options', content: 'nosniff' },
      { name: 'X-Frame-Options', content: 'DENY' },
      { name: 'X-XSS-Protection', content: '1; mode=block' },
      { name: 'Referrer-Policy', content: 'strict-origin-when-cross-origin' },
      { name: 'Permissions-Policy', content: 'camera=(), microphone=(), geolocation=()' },
    ];

    securityHeaders.forEach(header => {
      const meta = document.createElement('meta');
      meta.httpEquiv = header.name;
      meta.content = header.content;
      document.head.appendChild(meta);
    });

    // Cleanup function
    return () => {
      // Remove CSP meta tag on unmount
      document.head.removeChild(metaCSP);
      
      // Remove other security headers
      securityHeaders.forEach(header => {
        const meta = document.querySelector(`meta[http-equiv="${header.name}"]`);
        if (meta) {
          document.head.removeChild(meta);
        }
      });
    };
  }, []);

  return null; // This component doesn't render anything
};

export default SecurityHeaders;