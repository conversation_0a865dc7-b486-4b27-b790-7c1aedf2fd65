-- Migration: Create Historical Data Tables for Rate Parsing
-- Description: Add tables for storing historical parsing results and rate data

-- Table for storing detailed parsing operation results
CREATE TABLE IF NOT EXISTS parsing_results (
    id SERIAL PRIMARY KEY,
    exchanger_id VARCHAR(100) NOT NULL,
    exchanger_name VARCHAR(255),
    parsing_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'failed', 'error', 'partial')),
    execution_time_ms INTEGER DEFAULT 0,
    source_url TEXT,
    parsed_pairs_count INTEGER DEFAULT 0,
    total_pairs_expected INTEGER DEFAULT 0,
    error_message TEXT,
    error_details JSONB,
    raw_data JSONB,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table for storing historical exchange rate data
CREATE TABLE IF NOT EXISTS historical_rates (
    id SERIAL PRIMARY KEY,
    exchanger_id VARCHAR(100) NOT NULL,
    exchanger_name VARCHAR(255),
    currency_pair VARCHAR(20) NOT NULL,
    buy_rate DECIMAL(10, 4) NOT NULL,
    sell_rate DECIMAL(10, 4) NOT NULL,
    spread DECIMAL(10, 4) GENERATED ALWAYS AS (sell_rate - buy_rate) STORED,
    spread_percentage DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE 
            WHEN buy_rate > 0 THEN ((sell_rate - buy_rate) / buy_rate * 100)
            ELSE 0
        END
    ) STORED,
    parsing_result_id INTEGER REFERENCES parsing_results(id) ON DELETE CASCADE,
    source_url TEXT,
    data_quality_score INTEGER DEFAULT 100 CHECK (data_quality_score >= 0 AND data_quality_score <= 100),
    validation_status VARCHAR(20) DEFAULT 'valid' CHECK (validation_status IN ('valid', 'warning', 'invalid')),
    validation_notes TEXT,
    parsed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table for storing parsing operation summaries (daily/hourly aggregates)
CREATE TABLE IF NOT EXISTS parsing_summaries (
    id SERIAL PRIMARY KEY,
    date_period DATE NOT NULL,
    hour_period INTEGER CHECK (hour_period >= 0 AND hour_period <= 23),
    total_operations INTEGER DEFAULT 0,
    successful_operations INTEGER DEFAULT 0,
    failed_operations INTEGER DEFAULT 0,
    error_operations INTEGER DEFAULT 0,
    total_rates_parsed INTEGER DEFAULT 0,
    unique_exchangers_count INTEGER DEFAULT 0,
    unique_currency_pairs_count INTEGER DEFAULT 0,
    average_execution_time_ms DECIMAL(10, 2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date_period, hour_period)
);

-- Table for storing rate validation rules and thresholds
CREATE TABLE IF NOT EXISTS rate_validation_rules (
    id SERIAL PRIMARY KEY,
    currency_pair VARCHAR(20) NOT NULL UNIQUE,
    min_buy_rate DECIMAL(10, 4),
    max_buy_rate DECIMAL(10, 4),
    min_sell_rate DECIMAL(10, 4),
    max_sell_rate DECIMAL(10, 4),
    max_spread_percentage DECIMAL(5, 2),
    decimals_precision INTEGER DEFAULT 2,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_parsing_results_exchanger_timestamp 
    ON parsing_results(exchanger_id, parsing_timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_parsing_results_status_timestamp 
    ON parsing_results(status, parsing_timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_historical_rates_exchanger_currency_time 
    ON historical_rates(exchanger_id, currency_pair, parsed_at DESC);

CREATE INDEX IF NOT EXISTS idx_historical_rates_currency_time 
    ON historical_rates(currency_pair, parsed_at DESC);

CREATE INDEX IF NOT EXISTS idx_historical_rates_parsing_result 
    ON historical_rates(parsing_result_id);

CREATE INDEX IF NOT EXISTS idx_parsing_summaries_date_hour 
    ON parsing_summaries(date_period DESC, hour_period);

-- Triggers for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_parsing_results_updated_at 
    BEFORE UPDATE ON parsing_results 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_historical_rates_updated_at 
    BEFORE UPDATE ON historical_rates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_parsing_summaries_updated_at 
    BEFORE UPDATE ON parsing_summaries 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rate_validation_rules_updated_at 
    BEFORE UPDATE ON rate_validation_rules 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default validation rules
INSERT INTO rate_validation_rules (currency_pair, min_buy_rate, max_buy_rate, min_sell_rate, max_sell_rate, max_spread_percentage, decimals_precision)
VALUES 
    ('THB/RUB', 0.20, 0.60, 0.20, 0.60, 15.0, 3),
    ('RUB/THB', 1.50, 5.00, 1.50, 5.00, 15.0, 2),
    ('USDT/THB', 25.00, 45.00, 25.00, 45.00, 5.0, 2),
    ('USD/THB', 30.00, 40.00, 30.00, 40.00, 3.0, 2),
    ('EUR/THB', 35.00, 45.00, 35.00, 45.00, 3.0, 2)
ON CONFLICT (currency_pair) DO NOTHING;

-- Create view for latest rates by exchanger and currency pair
CREATE OR REPLACE VIEW latest_rates_by_exchanger AS
SELECT DISTINCT ON (exchanger_id, currency_pair)
    exchanger_id,
    exchanger_name,
    currency_pair,
    buy_rate,
    sell_rate,
    spread,
    spread_percentage,
    parsed_at,
    validation_status,
    data_quality_score
FROM historical_rates
ORDER BY exchanger_id, currency_pair, parsed_at DESC;

-- Create view for parsing operation statistics
CREATE OR REPLACE VIEW parsing_statistics AS
SELECT 
    DATE(parsing_timestamp) as date,
    COUNT(*) as total_operations,
    COUNT(*) FILTER (WHERE status = 'success') as successful_operations,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_operations,
    COUNT(*) FILTER (WHERE status = 'error') as error_operations,
    ROUND(AVG(execution_time_ms), 2) as avg_execution_time_ms,
    COUNT(DISTINCT exchanger_id) as unique_exchangers,
    SUM(parsed_pairs_count) as total_rates_parsed
FROM parsing_results
GROUP BY DATE(parsing_timestamp)
ORDER BY date DESC;

-- Comments for documentation
COMMENT ON TABLE parsing_results IS 'Stores detailed results of each parsing operation including success/failure status and execution metrics';
COMMENT ON TABLE historical_rates IS 'Stores all successfully parsed exchange rates with validation status and quality scores';
COMMENT ON TABLE parsing_summaries IS 'Stores aggregated parsing statistics by date and hour for performance monitoring';
COMMENT ON TABLE rate_validation_rules IS 'Defines validation rules and acceptable ranges for different currency pairs';

COMMENT ON COLUMN historical_rates.data_quality_score IS 'Quality score from 0-100 based on validation checks and data consistency';
COMMENT ON COLUMN historical_rates.spread IS 'Automatically calculated difference between sell and buy rates';
COMMENT ON COLUMN historical_rates.spread_percentage IS 'Automatically calculated spread as percentage of buy rate';
