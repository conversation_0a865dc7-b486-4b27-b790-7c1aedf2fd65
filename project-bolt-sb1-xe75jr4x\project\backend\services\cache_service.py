"""
Redis cache service for storing exchange rates
"""
import json
import logging
from typing import Optional, Dict, Any
import redis.asyncio as redis
from datetime import datetime, timedelta

from config.settings import settings

logger = logging.getLogger(__name__)


class CacheService:
    """Redis cache service for exchange rates"""
    
    def __init__(self):
        self.redis_client = None
        self.ttl = settings.CACHE_TTL_SECONDS
    
    async def initialize(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("Redis cache service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis cache: {e}")
            return False
    
    async def get_exchange_rates(self) -> Optional[Dict[str, Any]]:
        """Get cached exchange rates"""
        if not self.redis_client:
            await self.initialize()
        
        try:
            cached_data = await self.redis_client.get("exchange_rates")
            if cached_data:
                data = json.loads(cached_data)
                logger.info("Retrieved exchange rates from cache")
                return data
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving from cache: {e}")
            return None
    
    async def set_exchange_rates(self, data: Dict[str, Any]) -> bool:
        """Cache exchange rates"""
        if not self.redis_client:
            await self.initialize()
        
        try:
            await self.redis_client.setex(
                "exchange_rates",
                self.ttl,
                json.dumps(data, default=str)
            )
            logger.info("Exchange rates cached successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error caching data: {e}")
            return False
    
    async def get_cache_info(self) -> Dict[str, Any]:
        """Get cache information"""
        if not self.redis_client:
            await self.initialize()
        
        try:
            ttl = await self.redis_client.ttl("exchange_rates")
            exists = await self.redis_client.exists("exchange_rates")
            
            return {
                'exists': bool(exists),
                'ttl_seconds': ttl if ttl > 0 else 0,
                'expires_at': (datetime.now() + timedelta(seconds=ttl)).isoformat() if ttl > 0 else None
            }
            
        except Exception as e:
            logger.error(f"Error getting cache info: {e}")
            return {'exists': False, 'ttl_seconds': 0, 'expires_at': None}
    
    async def clear_cache(self) -> bool:
        """Clear exchange rates cache"""
        if not self.redis_client:
            await self.initialize()
        
        try:
            await self.redis_client.delete("exchange_rates")
            logger.info("Cache cleared successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False


# Global instance
cache_service = CacheService()