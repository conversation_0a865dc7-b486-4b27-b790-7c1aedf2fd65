import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Генерируем уникальный ID для ошибки
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // Логирование ошибки
    console.group('🚨 React Error Boundary Caught Error');
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
    console.error('Component Stack:', errorInfo.componentStack);
    console.groupEnd();

    // Вызов пользовательского обработчика
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Отправка в систему мониторинга
    this.reportError(error, errorInfo);

    // Попытка автоматического восстановления для некритических ошибок
    if (this.isRecoverableError(error)) {
      this.scheduleAutoRecovery();
    }
  }

  private isRecoverableError(error: Error): boolean {
    const recoverablePatterns = [
      'ChunkLoadError',
      'Loading chunk',
      'ResizeObserver loop limit exceeded',
      'Non-Error promise rejection captured'
    ];

    return recoverablePatterns.some(pattern => 
      error.message?.includes(pattern) || error.name?.includes(pattern)
    );
  }

  private scheduleAutoRecovery(): void {
    if (this.retryCount < this.maxRetries) {
      setTimeout(() => {
        this.retryCount++;
        console.log(`Auto-recovery attempt ${this.retryCount}/${this.maxRetries}`);
        this.setState({
          hasError: false,
          error: null,
          errorInfo: null,
          errorId: ''
        });
      }, 2000 * this.retryCount); // Увеличивающаяся задержка
    }
  }

  private reportError(error: Error, errorInfo: ErrorInfo): void {
    const errorReport = {
      errorId: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      retryCount: this.retryCount
    };

    // Отправка в Sentry (если настроен)
    if (window.Sentry) {
      window.Sentry.captureException(error, {
        tags: {
          component: 'ErrorBoundary',
          errorId: this.state.errorId
        },
        extra: errorReport
      });
    }

    // Отправка в собственную систему логирования
    try {
      fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorReport)
      }).catch(console.error);
    } catch (e) {
      console.warn('Failed to report error to logging service:', e);
    }
  }

  private handleRetry = (): void => {
    this.retryCount++;
    console.log(`Manual retry attempt ${this.retryCount}`);
    
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  private handleReload = (): void => {
    console.log('User initiated page reload from error boundary');
    window.location.reload();
  };

  private handleGoHome = (): void => {
    console.log('User navigated to home from error boundary');
    window.location.href = '/';
  };

  private copyErrorDetails = (): void => {
    const errorDetails = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      url: window.location.href
    };

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => {
        alert('Детали ошибки скопированы в буфер обмена');
      })
      .catch(() => {
        console.log('Error details:', errorDetails);
        alert('Детали ошибки выведены в консоль браузера');
      });
  };

  render() {
    if (this.state.hasError) {
      // Если предоставлен пользовательский fallback
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Стандартный UI для ошибки
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full p-8">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="bg-red-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <AlertTriangle className="w-10 h-10 text-red-600" />
              </div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">Упс! Что-то пошло не так</h1>
              <p className="text-gray-600 text-lg">
                Произошла неожиданная ошибка, но мы работаем над её устранением
              </p>
            </div>

            {/* Error Details (collapsible) */}
            <details className="mb-8 bg-gray-50 rounded-lg border border-gray-200">
              <summary className="p-4 cursor-pointer font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                <div className="flex items-center space-x-2">
                  <Bug className="w-4 h-4" />
                  <span>Технические детали ошибки</span>
                </div>
              </summary>
              <div className="p-4 border-t border-gray-200">
                <div className="space-y-3 text-sm">
                  <div>
                    <strong className="text-gray-700">ID ошибки:</strong>
                    <code className="ml-2 bg-gray-200 px-2 py-1 rounded font-mono text-xs">
                      {this.state.errorId}
                    </code>
                  </div>
                  
                  {this.state.error && (
                    <div>
                      <strong className="text-gray-700">Сообщение:</strong>
                      <div className="mt-1 bg-red-50 border border-red-200 rounded p-2 text-red-800 font-mono text-xs">
                        {this.state.error.message}
                      </div>
                    </div>
                  )}
                  
                  <div>
                    <strong className="text-gray-700">Время:</strong>
                    <span className="ml-2 text-gray-600">
                      {new Date().toLocaleString('ru-RU')}
                    </span>
                  </div>
                  
                  <div>
                    <strong className="text-gray-700">Страница:</strong>
                    <span className="ml-2 text-gray-600 break-all">
                      {window.location.href}
                    </span>
                  </div>
                  
                  <button
                    onClick={this.copyErrorDetails}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm transition-colors"
                  >
                    Скопировать детали для поддержки
                  </button>
                </div>
              </div>
            </details>

            {/* Action Buttons */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={this.handleRetry}
                  disabled={this.retryCount >= this.maxRetries}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-3 px-6 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2"
                >
                  <RefreshCw className="w-5 h-5" />
                  <span>
                    {this.retryCount >= this.maxRetries 
                      ? 'Превышен лимит попыток' 
                      : `Попробовать снова (${this.retryCount}/${this.maxRetries})`
                    }
                  </span>
                </button>

                <button
                  onClick={this.handleGoHome}
                  className="bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2"
                >
                  <Home className="w-5 h-5" />
                  <span>На главную страницу</span>
                </button>
              </div>

              <button
                onClick={this.handleReload}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2"
              >
                <RefreshCw className="w-5 h-5" />
                <span>Перезагрузить страницу</span>
              </button>
            </div>

            {/* Help Text */}
            <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-semibold text-blue-800 mb-2">Что можно сделать:</h3>
              <ul className="text-blue-700 text-sm space-y-1">
                <li>• Попробуйте обновить страницу (Ctrl+F5)</li>
                <li>• Очистите кэш браузера</li>
                <li>• Проверьте подключение к интернету</li>
                <li>• Попробуйте открыть сайт в режиме инкогнито</li>
                <li>• Обратитесь в техническую поддержку с ID ошибки</li>
              </ul>
            </div>

            {/* Development Info */}
            {import.meta.env.DEV && (
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h3 className="font-semibold text-yellow-800 mb-2">Информация для разработчиков:</h3>
                <div className="text-yellow-700 text-sm space-y-1">
                  <div><strong>Retry Count:</strong> {this.retryCount}</div>
                  <div><strong>Error Type:</strong> {this.state.error?.name}</div>
                  <div><strong>Component:</strong> {this.state.errorInfo?.componentStack?.split('\n')[1]?.trim()}</div>
                </div>
              </div>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;