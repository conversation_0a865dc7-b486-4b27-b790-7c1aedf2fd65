export interface User {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'moderator' | 'user';
  status: 'active' | 'inactive' | 'banned';
  createdAt: string;
  lastLogin?: string;
  permissions: string[];
}

export interface SystemSettings {
  id: string;
  category: string;
  key: string;
  value: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  description: string;
  updatedAt: string;
  updatedBy: string;
}

export interface ActivityLog {
  id: number;
  userId: number;
  username: string;
  action: string;
  resource: string;
  resourceId?: number;
  details: string;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
}

export interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalExchangers: number;
  activeExchangers: number;
  inactiveExchangers: number;
  totalReviews: number;
  pendingReviews: number;
  approvedReviews: number;
  rejectedReviews: number;
  totalRates: number;
  todayVisits: number;
  monthlyGrowth: number;
  topExchangers: Array<{
    id: number;
    name: string;
    rating: number;
    reviewCount: number;
  }>;
  recentActivity: ActivityLog[];
}

export interface ExchangerManagement {
  id: number;
  name: string;
  serviceId?: string;
  isMainOffice?: boolean;
  parentServiceName?: string;
  status: 'active' | 'inactive' | 'pending';
  rating: number;
  reviewCount: number;
  address: string;
  district: string;
  city?: string;
  phone: string;
  hours: string;
  website?: string;
  telegramBot?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  createdAt: string;
  lastUpdated: string;
  deletedAt?: string;
  isDeleted?: boolean;
  additionalOffices?: AdditionalOffice[];
}

export interface AdditionalOffice {
  id: number;
  exchangerId: number;
  name: string;
  address: string;
  district: string;
  phone: string;
  hours: string;
  status: 'active' | 'inactive';
  coordinates?: {
    lat: number;
    lng: number;
  };
  createdAt: string;
}

export interface ExchangerFormData {
  name: string;
  address: string;
  district: string;
  phone: string;
  hours: string;
  website?: string;
  telegramBot?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  additionalOffices: Omit<AdditionalOffice, 'id' | 'exchangerId' | 'createdAt'>[];
}

export interface ExchangerAnalytics {
  totalExchangers: number;
  activeExchangers: number;
  inactiveExchangers: number;
  pendingExchangers: number;
  totalOffices: number;
  averageRating: number;
  topPerformers: Array<{
    id: number;
    name: string;
    rating: number;
    reviewCount: number;
    totalOffices: number;
  }>;
  recentActivity: Array<{
    id: number;
    action: string;
    exchangerName: string;
    timestamp: string;
    adminUser: string;
  }>;
  monthlyStats: Array<{
    month: string;
    added: number;
    activated: number;
    deactivated: number;
  }>;
}