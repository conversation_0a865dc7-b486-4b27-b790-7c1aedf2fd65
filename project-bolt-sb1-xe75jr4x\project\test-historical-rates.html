<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест исторических курсов</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 300px; }
        .endpoint { font-family: monospace; background: #e9ecef; padding: 2px 4px; border-radius: 3px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🔍 Тест исторических курсов валют</h1>
    
    <div class="test-section info">
        <h2>📊 Тестирование API исторических курсов</h2>
        <p>Эта страница тестирует работу API исторических курсов валют:</p>
        <ul>
            <li><strong>Получение исторических данных</strong> - <span class="endpoint">GET /api/v1/parsing/historical-rates</span></li>
            <li><strong>Фильтрация по валютным парам</strong> - параметр <code>currency_pair</code></li>
            <li><strong>Фильтрация по обменникам</strong> - параметр <code>exchanger_id</code></li>
            <li><strong>Временные диапазоны</strong> - параметр <code>days_back</code></li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🧪 Тесты API</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
            <button onclick="testBasicHistoricalRates()">Базовый тест</button>
            <button onclick="testWithCurrencyPair()">Тест с валютной парой</button>
            <button onclick="testWithExchanger()">Тест с обменником</button>
            <button onclick="testWithTimeRange()">Тест временного диапазона</button>
            <button onclick="testAvailableFilters()">Тест доступных фильтров</button>
            <button onclick="testAllEndpoints()">Тест всех endpoints</button>
        </div>
    </div>

    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="test-section ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function testEndpoint(url, description) {
            try {
                log(`🔄 Тестирование: ${description}<br>Endpoint: <span class="endpoint">${url}</span>`, 'info');
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const ratesCount = data.data?.rates?.length || 0;
                    const totalCount = data.data?.total_count || 0;
                    const currencyPairs = data.data?.available_filters?.currency_pairs?.length || 0;
                    const exchangers = data.data?.available_filters?.exchangers?.length || 0;
                    
                    log(`✅ ${description} - УСПЕХ<br>
                        📊 Получено курсов: ${ratesCount}<br>
                        📈 Всего записей: ${totalCount}<br>
                        💱 Валютных пар: ${currencyPairs}<br>
                        🏪 Обменников: ${exchangers}<br>
                        <details><summary>Подробные данные</summary><pre>${JSON.stringify(data, null, 2)}</pre></details>`, 'success');
                    
                    // Показать таблицу с данными если есть курсы
                    if (ratesCount > 0) {
                        showRatesTable(data.data.rates);
                    }
                } else {
                    log(`❌ ${description} - ОШИБКА<br>Status: ${response.status}<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                log(`❌ ${description} - ИСКЛЮЧЕНИЕ<br>Error: ${error.message}`, 'error');
            }
        }

        function showRatesTable(rates) {
            if (!rates || rates.length === 0) return;
            
            let tableHtml = `
                <table>
                    <thead>
                        <tr>
                            <th>Валютная пара</th>
                            <th>Обменник</th>
                            <th>Курс покупки</th>
                            <th>Курс продажи</th>
                            <th>Дата</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            rates.slice(0, 10).forEach(rate => {
                tableHtml += `
                    <tr>
                        <td>${rate.currency_pair || 'N/A'}</td>
                        <td>${rate.exchanger_name || 'N/A'}</td>
                        <td>${rate.buy_rate ? rate.buy_rate.toFixed(4) : 'N/A'}</td>
                        <td>${rate.sell_rate ? rate.sell_rate.toFixed(4) : 'N/A'}</td>
                        <td>${rate.parsed_at ? new Date(rate.parsed_at).toLocaleString('ru-RU') : 'N/A'}</td>
                    </tr>
                `;
            });
            
            tableHtml += `
                    </tbody>
                </table>
                ${rates.length > 10 ? `<p><em>Показано первых 10 из ${rates.length} записей</em></p>` : ''}
            `;
            
            log(`📋 Таблица исторических курсов:<br>${tableHtml}`, 'info');
        }

        async function testBasicHistoricalRates() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/historical-rates?limit=10&days_back=7`,
                'Базовое получение исторических курсов'
            );
        }

        async function testWithCurrencyPair() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/historical-rates?currency_pair=RUB/THB&limit=10&days_back=7`,
                'Фильтрация по валютной паре RUB/THB'
            );
        }

        async function testWithExchanger() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/historical-rates?exchanger_id=superrich&limit=10&days_back=7`,
                'Фильтрация по обменнику SuperRich'
            );
        }

        async function testWithTimeRange() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/historical-rates?limit=20&days_back=30`,
                'Тест временного диапазона (30 дней)'
            );
        }

        async function testAvailableFilters() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/historical-rates?limit=1&days_back=7`,
                'Проверка доступных фильтров'
            );
        }

        async function testAllEndpoints() {
            log('🚀 Запуск комплексного тестирования...', 'info');
            
            await testBasicHistoricalRates();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testWithCurrencyPair();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testWithExchanger();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testWithTimeRange();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAvailableFilters();
            
            log('🎉 Комплексное тестирование завершено!', 'success');
        }

        // Автоматический запуск базового теста при загрузке страницы
        window.addEventListener('load', () => {
            log('🔧 Страница тестирования исторических курсов загружена', 'info');
            setTimeout(testBasicHistoricalRates, 1000);
        });
    </script>
</body>
</html>
