import React, { useState, useEffect } from 'react';
import { RefreshCw, AlertCircle } from 'lucide-react';
import { config } from '../../config/environment';

interface ParsingResult {
  id: number;
  exchanger_name: string;
  status: 'success' | 'failed' | 'error' | 'partial';
  parsing_timestamp: string;
  execution_time_ms: number;
  parsed_pairs_count: number;
  error_message?: string;
}

const ParsingResultsTableSimple: React.FC = () => {
  const [results, setResults] = useState<ParsingResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastFetch, setLastFetch] = useState<Date | null>(null);

  const fetchResults = async () => {
    console.log('🔄 ParsingResultsTableSimple: Starting fetch...');
    setIsLoading(true);
    setError(null);
    
    try {
      const url = `${config.API_BASE_URL}/parsing/results?limit=50&offset=0&hours_back=24`;
      console.log('📡 ParsingResultsTableSimple: Fetching from:', url);
      
      const response = await fetch(url);
      console.log('📊 ParsingResultsTableSimple: Response status:', response.status);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('✅ ParsingResultsTableSimple: Data received:', data);
      
      if (data.success && data.data?.results) {
        setResults(data.data.results);
        setLastFetch(new Date());
        console.log(`📋 ParsingResultsTableSimple: Loaded ${data.data.results.length} results`);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('❌ ParsingResultsTableSimple: Error:', errorMessage);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Загружаем данные при монтировании компонента
  useEffect(() => {
    console.log('🚀 ParsingResultsTableSimple: Component mounted, fetching data...');
    fetchResults();
    
    // Автоматическое обновление каждые 30 секунд
    const interval = setInterval(() => {
      console.log('⏰ ParsingResultsTableSimple: Auto-refresh triggered');
      fetchResults();
    }, 30000);
    
    return () => {
      console.log('🛑 ParsingResultsTableSimple: Component unmounted, clearing interval');
      clearInterval(interval);
    };
  }, []);

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";
    switch (status) {
      case 'success':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'failed':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'error':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'partial':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatExecutionTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Parsing Results (Simple)</h2>
            <p className="text-gray-600">Simple version for debugging</p>
          </div>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center space-x-2 text-red-800">
            <AlertCircle className="w-5 h-5" />
            <span className="font-medium">Error loading parsing results</span>
          </div>
          <p className="text-red-600 mt-2">{error}</p>
          <button
            onClick={fetchResults}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Parsing Results (Simple)</h2>
          <p className="text-gray-600">Simple version for debugging - API: {config.API_BASE_URL}</p>
          {lastFetch && (
            <p className="text-sm text-gray-500">Last updated: {lastFetch.toLocaleTimeString()}</p>
          )}
        </div>
        <button
          onClick={fetchResults}
          disabled={isLoading}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          <span>{isLoading ? 'Loading...' : 'Refresh'}</span>
        </button>
      </div>

      {/* Results Table */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading parsing results...</span>
          </div>
        ) : results.length === 0 ? (
          <div className="text-center py-12">
            <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
            <p className="text-gray-600">No parsing results available.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Exchanger
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Timestamp
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Execution Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Parsed Pairs
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {results.slice(0, 10).map((result) => (
                  <tr key={result.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {result.exchanger_name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={getStatusBadge(result.status)}>
                        {result.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatTimestamp(result.parsing_timestamp)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatExecutionTime(result.execution_time_ms)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {result.parsed_pairs_count}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      <div className="text-sm text-gray-500">
        Total results: {results.length} | Component: ParsingResultsTableSimple
      </div>
    </div>
  );
};

export default ParsingResultsTableSimple;
