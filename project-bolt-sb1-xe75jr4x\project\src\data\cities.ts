export interface CityData {
  id: string;
  name: string;
  nameEn: string;
  country: string;
  timezone: string;
  currency: string;
  exchangerCount: number;
  description: string;
  image: string;
  popularDistricts: string[];
  isPopular?: boolean;
  region?: string;
}

export const cities: CityData[] = [
  // Основные туристические центры
  {
    id: 'phuket',
    name: '<PERSON>ху<PERSON><PERSON><PERSON>',
    nameEn: 'Phuket',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 45,
    description: 'Крупнейший остров и популярный курорт с множеством обменных пунктов',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Патонг', 'Карон', 'Ката', 'Пхукет Таун', 'Равай', 'Камала'],
    isPopular: true,
    region: 'Южный Таиланд'
  },
  {
    id: 'bangkok',
    name: 'Бангкок',
    nameEn: 'Bangkok',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 120,
    description: 'Столица Таиланда с наибольшим количеством обменников',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Сукхумвит', 'Силом', 'Каосан Роуд', 'Чатучак', 'Пратунам', 'Чайнатаун'],
    isPopular: true,
    region: 'Центральный Таиланд'
  },
  {
    id: 'pattaya',
    name: 'Паттайя',
    nameEn: 'Pattaya',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 35,
    description: 'Популярный курортный город с удобными обменными пунктами',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Центральная Паттайя', 'Джомтьен', 'Наклуа', 'Пратамнак', 'Вонгамат'],
    isPopular: true,
    region: 'Восточный Таиланд'
  },

  // Островные курорты
  {
    id: 'samui',
    name: 'Самуи',
    nameEn: 'Koh Samui',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 18,
    description: 'Тропический остров с белоснежными пляжами и кокосовыми пальмами',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Чавенг', 'Ламай', 'Бопхут', 'Натон', 'Маенам'],
    isPopular: true,
    region: 'Южный Таиланд'
  },
  {
    id: 'phi-phi',
    name: 'Пхи-Пхи',
    nameEn: 'Phi Phi Islands',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 8,
    description: 'Живописные острова с кристально чистой водой',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Тон Сай', 'Лох Далам', 'Лаем Тонг', 'Ло Ба Као'],
    region: 'Южный Таиланд'
  },
  {
    id: 'koh-tao',
    name: 'Ко Тао',
    nameEn: 'Koh Tao',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 12,
    description: 'Остров дайвинга с богатым подводным миром',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Сайри Бич', 'Мае Хаад', 'Чалок Бан Као', 'Танот Бей'],
    region: 'Южный Таиланд'
  },
  {
    id: 'koh-phangan',
    name: 'Ко Панган',
    nameEn: 'Koh Phangan',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 15,
    description: 'Остров знаменитых Full Moon Party и уединенных пляжей',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Хаад Рин', 'Тонг Сала', 'Срит Тану', 'Хаад Салад'],
    region: 'Южный Таиланд'
  },

  // Материковые курорты
  {
    id: 'krabi',
    name: 'Краби',
    nameEn: 'Krabi',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 22,
    description: 'Провинция с известняковыми скалами и красивыми пляжами',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Ао Нанг', 'Рейли', 'Краби Таун', 'Клонг Муанг', 'Тубкаек'],
    region: 'Южный Таиланд'
  },
  {
    id: 'hua-hin',
    name: 'Хуа Хин',
    nameEn: 'Hua Hin',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 16,
    description: 'Королевский курорт с полями для гольфа и спокойной атмосферой',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Центр Хуа Хин', 'Као Такиаб', 'Као Тао', 'Ча Ам'],
    region: 'Центральный Таиланд'
  },
  {
    id: 'kanchanaburi',
    name: 'Канчанабури',
    nameEn: 'Kanchanaburi',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 0,
    description: 'Историческая провинция с мостом через реку Квай',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Канчанабури Таун', 'Сай Йок', 'Тонг Пха Пхум', 'Эраван'],
    region: 'Западный Таиланд'
  },

  // Северные города
  {
    id: 'chiang-mai',
    name: 'Чиангмай',
    nameEn: 'Chiang Mai',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 28,
    description: 'Культурная столица севера с древними храмами',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Старый город', 'Нимман', 'Сантитам', 'Чанг Кхлан', 'Хангдонг'],
    region: 'Северный Таиланд'
  },
  {
    id: 'chiang-rai',
    name: 'Чианграй',
    nameEn: 'Chiang Rai',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 14,
    description: 'Северный город с Белым и Синим храмами',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Чианграй Таун', 'Мае Сай', 'Чианг Саен', 'Пхан'],
    region: 'Северный Таиланд'
  },
  {
    id: 'pai',
    name: 'Пай',
    nameEn: 'Pai',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 0,
    description: 'Горный городок с хиппи-атмосферой и красивой природой',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Пай Таун', 'Мае Хонг Сон', 'Вианг Тай', 'Тхам Лот'],
    region: 'Северный Таиланд'
  },

  // Восточные курорты
  {
    id: 'koh-chang',
    name: 'Ко Чанг',
    nameEn: 'Koh Chang',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 11,
    description: 'Второй по величине остров Таиланда с джунглями и водопадами',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Вайт Санд', 'Клонг Прао', 'Кай Бае', 'Лонли Бич'],
    region: 'Восточный Таиланд'
  },
  {
    id: 'rayong',
    name: 'Районг',
    nameEn: 'Rayong',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 13,
    description: 'Промышленная провинция с красивыми пляжами',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Районг Таун', 'Мае Рампхынг', 'Клаенг', 'Бан Чанг'],
    region: 'Восточный Таиланд'
  },

  // Южные курорты
  {
    id: 'koh-lanta',
    name: 'Ко Ланта',
    nameEn: 'Koh Lanta',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 10,
    description: 'Спокойный остров с длинными песчаными пляжами',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Салатан', 'Клонг Дао', 'Пра Ае', 'Клонг Хин'],
    region: 'Южный Таиланд'
  },
  {
    id: 'trang',
    name: 'Транг',
    nameEn: 'Trang',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 8,
    description: 'Провинция с нетронутыми островами и пещерами',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Транг Таун', 'Хат Сампан', 'Палиан', 'Хуай Йот'],
    region: 'Южный Таиланд'
  },
  {
    id: 'satun',
    name: 'Сатун',
    nameEn: 'Satun',
    country: 'Таиланд',
    timezone: 'UTC+7',
    currency: 'THB',
    exchangerCount: 0,
    description: 'Южная провинция у границы с Малайзией',
    image: 'https://images.pexels.com/photos/1007657/pexels-photo-1007657.jpeg?auto=compress&cs=tinysrgb&w=400',
    popularDistricts: ['Сатун Таун', 'Ла-Нгу', 'Тхунг Ва', 'Мананг'],
    region: 'Южный Таиланд'
  }
];

export const getCurrentCity = (cityId: string): CityData | undefined => {
  return cities.find(city => city.id === cityId);
};

export const getPopularCities = (): CityData[] => {
  return cities.filter(city => city.isPopular);
};

export const getCitiesByRegion = (region: string): CityData[] => {
  return cities.filter(city => city.region === region);
};

export const getAllRegions = (): string[] => {
  const regions = cities.map(city => city.region).filter(Boolean);
  return [...new Set(regions)] as string[];
};