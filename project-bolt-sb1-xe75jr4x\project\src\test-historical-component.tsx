import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import HistoricalRates from './components/HistoricalRates';

// Create a query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const TestHistoricalComponent: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h1 className="text-xl font-bold text-blue-800 mb-2">
              🧪 Тестирование компонента HistoricalRates
            </h1>
            <p className="text-blue-600">
              Эта страница тестирует компонент исторических курсов в изолированной среде.
              Откройте консоль браузера для просмотра отладочной информации.
            </p>
          </div>
          
          <HistoricalRates />
        </div>
      </div>
    </QueryClientProvider>
  );
};

export default TestHistoricalComponent;
