import React from 'react';
import { TrendingUp, Mail, Phone, MapPin, Clock, Shield, Star, Users, Globe, ExternalLink } from 'lucide-react';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const cities = [
    { name: 'Пхукет', count: 45, popular: true },
    { name: 'Бангкок', count: 120, popular: true },
    { name: 'Паттайя', count: 35, popular: true },
    { name: 'Саму<PERSON>', count: 18, popular: false },
    { name: 'Краб<PERSON>', count: 12, popular: false }
  ];

  const services = [
    { name: 'Курсы валют', href: '#rates' },
    { name: 'ТОП обменников', href: '#top' },
    { name: 'Отзывы клиентов', href: '#reviews' },
    { name: 'Карта обменников', href: '#map' },
    { name: 'Рейтинги', href: '#ratings' },
    { name: 'Сравнение курсов', href: '#compare' }
  ];

  const currencies = [
    { code: 'RUB', name: 'Российский рубль', flag: '🇷🇺' },
    { code: 'USD', name: 'Доллар США', flag: '🇺🇸' },
    { code: 'EUR', name: 'Евро', flag: '🇪🇺' },
    { code: 'THB', name: 'Тайский бат', flag: '🇹🇭' },
    { code: 'USDT', name: 'Tether', flag: '₮' }
  ];

  return (
    <footer className="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white mt-20">
      {/* Trust Section */}
      <div className="bg-gradient-to-r from-blue-800/50 to-purple-800/50 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-400 to-blue-500 rounded-full mb-4">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold mb-2">Надежность и безопасность</h3>
            <p className="text-blue-100 max-w-2xl mx-auto">
              Все обменники проходят тщательную проверку. Мы гарантируем актуальность информации и безопасность сделок.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="bg-white/10 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <Shield className="w-6 h-6 text-green-400" />
              </div>
              <h4 className="font-semibold mb-1">Проверенные</h4>
              <p className="text-sm text-blue-200">Все обменники лицензированы</p>
            </div>
            <div className="text-center">
              <div className="bg-white/10 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <Star className="w-6 h-6 text-yellow-400" />
              </div>
              <h4 className="font-semibold mb-1">Высокие рейтинги</h4>
              <p className="text-sm text-blue-200">Только лучшие обменники</p>
            </div>
            <div className="text-center">
              <div className="bg-white/10 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <Users className="w-6 h-6 text-purple-400" />
              </div>
              <h4 className="font-semibold mb-1">Тысячи клиентов</h4>
              <p className="text-sm text-blue-200">Проверено отзывами</p>
            </div>
            <div className="text-center">
              <div className="bg-white/10 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <Clock className="w-6 h-6 text-orange-400" />
              </div>
              <h4 className="font-semibold mb-1">Актуальные курсы</h4>
              <p className="text-sm text-blue-200">Обновление 3 раза в день</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 p-2 rounded-lg">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold">Обменники Таиланда</h3>
                <p className="text-blue-200 text-sm">Лучшие курсы валют</p>
              </div>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              Ведущая платформа для поиска и сравнения курсов валют в Таиланде. 
              Мы помогаем туристам найти лучшие обменники с выгодными курсами и высокими рейтингами.
            </p>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm">
                <Globe className="w-4 h-4 text-blue-400" />
                <span className="text-gray-300">200+ обменников</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <Users className="w-4 h-4 text-green-400" />
                <span className="text-gray-300">50k+ пользователей</span>
              </div>
            </div>
          </div>

          {/* Cities */}
          <div>
            <h4 className="text-lg font-semibold mb-4 flex items-center">
              <MapPin className="w-5 h-5 mr-2 text-blue-400" />
              Города
            </h4>
            <ul className="space-y-3">
              {cities.map((city, index) => (
                <li key={index}>
                  <a 
                    href={`#${city.name.toLowerCase()}`} 
                    className="text-gray-300 hover:text-white transition-colors text-sm flex items-center justify-between group"
                  >
                    <span className="flex items-center">
                      {city.name}
                      {city.popular && (
                        <span className="ml-2 bg-orange-500 text-white text-xs px-2 py-0.5 rounded-full">
                          Популярный
                        </span>
                      )}
                    </span>
                    <span className="text-blue-300 text-xs">
                      {city.count} обменников
                    </span>
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-4 flex items-center">
              <Star className="w-5 h-5 mr-2 text-yellow-400" />
              Сервисы
            </h4>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index}>
                  <a 
                    href={service.href} 
                    className="text-gray-300 hover:text-white transition-colors text-sm flex items-center group"
                  >
                    <ExternalLink className="w-3 h-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity" />
                    {service.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact & Currencies */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Контакты и валюты</h4>
            <div className="space-y-3 mb-6">
              <div className="flex items-center space-x-3">
                <Mail className="w-4 h-4 text-blue-400" />
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white text-sm transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-4 h-4 text-green-400" />
                <a href="tel:+66891234567" className="text-gray-300 hover:text-white text-sm transition-colors">
                  +66 89 123 4567
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="w-4 h-4 text-orange-400" />
                <span className="text-gray-300 text-sm">Поддержка: 9:00 - 21:00</span>
              </div>
            </div>

            <div>
              <h5 className="font-medium mb-3 text-sm">Поддерживаемые валюты:</h5>
              <div className="space-y-2">
                {currencies.slice(0, 3).map((currency, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <span className="text-lg">{currency.flag}</span>
                    <span className="text-gray-300">{currency.code}</span>
                    <span className="text-gray-500 text-xs">{currency.name}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="border-t border-gray-700 bg-gray-900/50">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © {currentYear} Обменники Таиланда. Все права защищены.
            </div>
            <div className="flex flex-wrap items-center space-x-6">
              <a href="#privacy" className="text-gray-400 hover:text-white transition-colors text-sm">
                Политика конфиденциальности
              </a>
              <a href="#terms" className="text-gray-400 hover:text-white transition-colors text-sm">
                Условия использования
              </a>
              <a href="#sitemap" className="text-gray-400 hover:text-white transition-colors text-sm">
                Карта сайта
              </a>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-800">
            <div className="text-center">
              <p className="text-gray-500 text-xs leading-relaxed">
                <strong>Важное уведомление:</strong> Курсы валют носят информационный характер и могут отличаться от фактических. 
                Перед совершением операций обязательно уточняйте актуальные курсы непосредственно в обменных пунктах. 
                Администрация сайта не несет ответственности за возможные расхождения в курсах валют.
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;