"""
API routes for exchangers
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional, Dict, Any
import logging

from services.exchange_service import exchange_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=Dict[str, Any])
async def get_exchangers(
    district: Optional[str] = Query(None, description="Filter by district"),
    search: Optional[str] = Query(None, description="Search by name or address"),
    limit: Optional[int] = Query(50, ge=1, le=100, description="Limit results"),
    force_refresh: bool = Query(False, description="Force refresh from Google Sheets")
):
    """Get list of exchangers with optional filtering"""
    try:
        if search or district:
            # Use search functionality
            exchangers = await exchange_service.search_exchangers(
                query=search or "", 
                district=district
            )
        else:
            # Get all exchangers
            rates_data = await exchange_service.get_exchange_rates(force_refresh=force_refresh)
            if not rates_data:
                raise HTTPException(status_code=503, detail="Exchange rates service unavailable")
            
            exchangers = rates_data.get('exchangers', [])
        
        # Apply limit
        limited_exchangers = exchangers[:limit] if limit else exchangers
        
        return {
            'success': True,
            'data': {
                'exchangers': limited_exchangers,
                'total_count': len(limited_exchangers),
                'filtered_count': len(exchangers) if search or district else len(limited_exchangers)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting exchangers: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/top", response_model=Dict[str, Any])
async def get_top_exchangers(
    limit: int = Query(10, ge=1, le=50, description="Number of top exchangers to return")
):
    """Get top-rated exchangers"""
    try:
        top_exchangers = await exchange_service.get_top_exchangers(limit=limit)
        
        return {
            'success': True,
            'data': {
                'exchangers': top_exchangers,
                'total_count': len(top_exchangers)
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting top exchangers: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{exchanger_id}", response_model=Dict[str, Any])
async def get_exchanger_by_id(exchanger_id: int):
    """Get specific exchanger by ID"""
    try:
        exchanger = await exchange_service.get_exchanger_by_id(exchanger_id)
        
        if not exchanger:
            raise HTTPException(status_code=404, detail="Exchanger not found")
        
        return {
            'success': True,
            'data': exchanger
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting exchanger {exchanger_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{exchanger_id}/rates", response_model=Dict[str, Any])
async def get_exchanger_rates(exchanger_id: int):
    """Get rates for specific exchanger"""
    try:
        exchanger = await exchange_service.get_exchanger_by_id(exchanger_id)
        
        if not exchanger:
            raise HTTPException(status_code=404, detail="Exchanger not found")
        
        return {
            'success': True,
            'data': {
                'exchanger_name': exchanger.get('name'),
                'rates': exchanger.get('rates', {}),
                'last_updated': exchanger.get('last_updated')
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting rates for exchanger {exchanger_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")