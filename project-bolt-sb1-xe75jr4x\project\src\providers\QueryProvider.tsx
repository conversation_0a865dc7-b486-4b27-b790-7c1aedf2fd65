// React Query provider setup
import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // Prevent infinite retry loops
        if (failureCount >= 2) return false;
        
        // Don't retry on certain errors that might cause reloads
        const nonRetryableErrors = [
          'Network Error',
          'Failed to fetch',
          'ERR_INTERNET_DISCONNECTED'
        ];
        
        if (nonRetryableErrors.some(pattern => 
          error?.message?.includes(pattern)
        )) {
          return false;
        }
        
        return true;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 2 * 60 * 1000, // 2 minutes to reduce aggressive refetching
      gcTime: 5 * 60 * 1000, // 5 minutes to prevent memory issues
      refetchOnWindowFocus: false,
      refetchOnMount: false, // Prevent excessive refetching
      refetchInterval: false, // Disable automatic intervals by default
    },
    mutations: {
      retry: 1,
      onError: (error) => {
        console.error('Mutation error:', error);
        // Don't let mutation errors cause page reloads
      },
    },
  },
});

interface QueryProviderProps {
  children: React.ReactNode;
}

export const QueryProvider: React.FC<QueryProviderProps> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
};

export default QueryProvider;