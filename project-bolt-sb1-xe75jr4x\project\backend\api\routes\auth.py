"""
Authentication routes for user registration and login
"""
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, EmailStr
from typing import Optional
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# Request models
class RegisterRequest(BaseModel):
    email: EmailStr
    password: str
    name: str
    role: Optional[str] = "user"

class LoginRequest(BaseModel):
    email: EmailStr
    password: str

class AuthResponse(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None

@router.post("/register", response_model=AuthResponse)
async def register_user(request: RegisterRequest):
    """
    Register a new user account
    """
    try:
        logger.info(f"Registration attempt for email: {request.email}")
        
        # For now, return a success response since we're using Supabase on the frontend
        # In a real implementation, this would create a user in the database
        return AuthResponse(
            success=True,
            message="Регистрация успешна! Добро пожаловать!",
            data={
                "user": {
                    "id": f"user-{hash(request.email)}",
                    "email": request.email,
                    "name": request.name,
                    "role": request.role
                },
                "token": "mock-jwt-token"
            }
        )
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Ошибка при регистрации пользователя"
        )

@router.post("/login", response_model=AuthResponse)
async def login_user(request: LoginRequest):
    """
    Login user
    """
    try:
        logger.info(f"Login attempt for email: {request.email}")
        
        # For now, return a success response since we're using Supabase on the frontend
        return AuthResponse(
            success=True,
            message="Вход выполнен успешно!",
            data={
                "user": {
                    "id": f"user-{hash(request.email)}",
                    "email": request.email,
                    "role": "user"
                },
                "token": "mock-jwt-token"
            }
        )
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Ошибка при входе в систему"
        )

@router.post("/logout", response_model=AuthResponse)
async def logout_user():
    """
    Logout user
    """
    return AuthResponse(
        success=True,
        message="Выход выполнен успешно!"
    )

@router.post("/refresh", response_model=AuthResponse)
async def refresh_token():
    """
    Refresh authentication token
    """
    return AuthResponse(
        success=True,
        message="Токен обновлен",
        data={"token": "new-mock-jwt-token"}
    )

@router.post("/forgot-password", response_model=AuthResponse)
async def forgot_password(request: dict):
    """
    Send password reset email
    """
    email = request.get("email")
    if not email:
        raise HTTPException(status_code=400, detail="Email обязателен")
    
    return AuthResponse(
        success=True,
        message="Инструкции по восстановлению пароля отправлены на email"
    )
