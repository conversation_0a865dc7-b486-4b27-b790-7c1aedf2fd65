// Security utilities and helpers
import { config } from '../config/environment';

// CSRF Token management
class CSRFTokenManager {
  private static instance: CSRFTokenManager;
  private token: string | null = null;

  private constructor() {}

  public static getInstance(): CSRFTokenManager {
    if (!CSRFTokenManager.instance) {
      CSRFTokenManager.instance = new CSRFTokenManager();
    }
    return CSRFTokenManager.instance;
  }

  // Generate CSRF token
  public generateToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    this.token = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    
    // Store in session storage (not localStorage for security)
    sessionStorage.setItem('csrf_token', this.token);
    
    return this.token;
  }

  // Get current CSRF token
  public getToken(): string | null {
    if (!this.token) {
      this.token = sessionStorage.getItem('csrf_token');
    }
    return this.token;
  }

  // Validate CSRF token
  public validateToken(token: string): boolean {
    const currentToken = this.getToken();
    return currentToken !== null && currentToken === token;
  }

  // Clear CSRF token
  public clearToken(): void {
    this.token = null;
    sessionStorage.removeItem('csrf_token');
  }
}

export const csrfTokenManager = CSRFTokenManager.getInstance();

// Input sanitization
export const sanitizeInput = (input: string): string => {
  // Remove potentially dangerous characters
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

// Email validation
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
};

// Password strength validation
export const validatePasswordStrength = (password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} => {
  const feedback: string[] = [];
  let score = 0;

  if (password.length < 8) {
    feedback.push('Пароль должен содержать минимум 8 символов');
  } else {
    score++;
  }

  if (!/[a-z]/.test(password)) {
    feedback.push('Добавьте строчные буквы');
  } else {
    score++;
  }

  if (!/[A-Z]/.test(password)) {
    feedback.push('Добавьте заглавные буквы');
  } else {
    score++;
  }

  if (!/[0-9]/.test(password)) {
    feedback.push('Добавьте цифры');
  } else {
    score++;
  }

  if (!/[^A-Za-z0-9]/.test(password)) {
    feedback.push('Добавьте специальные символы');
  } else {
    score++;
  }

  // Check for common passwords
  const commonPasswords = [
    'password', '123456', 'qwerty', 'admin', 'letmein',
    'welcome', 'monkey', '1234567890', 'password123'
  ];
  
  if (commonPasswords.includes(password.toLowerCase())) {
    feedback.push('Не используйте распространенные пароли');
    score = Math.max(0, score - 2);
  }

  return {
    isValid: score >= 3 && password.length >= 8,
    score,
    feedback,
  };
};

// Rate limiting for client-side
class RateLimiter {
  private attempts: Map<string, number[]> = new Map();

  // Check if action is rate limited
  public isRateLimited(key: string, maxAttempts: number, windowMs: number): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(timestamp => now - timestamp < windowMs);
    
    // Update attempts
    this.attempts.set(key, validAttempts);
    
    return validAttempts.length >= maxAttempts;
  }

  // Record an attempt
  public recordAttempt(key: string): void {
    const attempts = this.attempts.get(key) || [];
    attempts.push(Date.now());
    this.attempts.set(key, attempts);
  }

  // Clear attempts for a key
  public clearAttempts(key: string): void {
    this.attempts.delete(key);
  }
}

export const rateLimiter = new RateLimiter();

// Secure data storage
export const secureStorage = {
  // Store sensitive data with encryption (basic implementation)
  setItem: (key: string, value: string): void => {
    try {
      // In production, use proper encryption
      const encrypted = btoa(value); // Basic base64 encoding (use proper encryption in production)
      localStorage.setItem(key, encrypted);
    } catch (error) {
      console.error('Error storing secure data:', error);
    }
  },

  // Get and decrypt sensitive data
  getItem: (key: string): string | null => {
    try {
      const encrypted = localStorage.getItem(key);
      if (!encrypted) return null;
      
      // In production, use proper decryption
      return atob(encrypted); // Basic base64 decoding
    } catch (error) {
      console.error('Error retrieving secure data:', error);
      return null;
    }
  },

  // Remove sensitive data
  removeItem: (key: string): void => {
    localStorage.removeItem(key);
  },

  // Clear all sensitive data
  clear: (): void => {
    localStorage.clear();
    sessionStorage.clear();
  },
};

// XSS Protection
export const escapeHtml = (unsafe: string): string => {
  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
};

// Generate secure random string
export const generateSecureRandom = (length: number = 32): string => {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

// Validate URL to prevent open redirect attacks
export const isValidRedirectUrl = (url: string): boolean => {
  try {
    const parsedUrl = new URL(url, window.location.origin);
    
    // Only allow same origin redirects
    return parsedUrl.origin === window.location.origin;
  } catch {
    return false;
  }
};

// Security audit logger
export const logSecurityEvent = (event: string, details: any = {}) => {
  if (config.ENABLE_ERROR_REPORTING) {
    console.warn('Security Event:', {
      event,
      details,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    });
    
    // In production, send to security monitoring service
    if (config.APP_ENV === 'production') {
      // Send to security monitoring service
      fetch('/api/security/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event,
          details,
          timestamp: new Date().toISOString(),
        }),
      }).catch(console.error);
    }
  }
};