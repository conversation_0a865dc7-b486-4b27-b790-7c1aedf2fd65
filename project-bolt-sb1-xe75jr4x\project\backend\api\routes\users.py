"""
User management API routes for admin panel
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Dict, Any, Optional, List
from datetime import datetime
from pydantic import BaseModel, EmailStr
import logging

from services.database_service import database_service

logger = logging.getLogger(__name__)
router = APIRouter()

class UserResponse(BaseModel):
    id: str
    email: str
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    phone: Optional[str] = None
    role: str = "user"
    isActive: bool = True
    createdAt: str
    lastLogin: Optional[str] = None
    registrationSource: str = "fallback"  # "supabase", "backend", "fallback"

class UserUpdateRequest(BaseModel):
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    phone: Optional[str] = None
    role: Optional[str] = None
    isActive: Optional[bool] = None

class UserCreateRequest(BaseModel):
    email: EmailStr
    password: str
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    phone: Optional[str] = None
    role: str = "user"

class UsersListResponse(BaseModel):
    success: bool
    data: Dict[str, Any]
    message: Optional[str] = None

# Simple admin authentication check (in production, use proper JWT validation)
def verify_admin_access():
    # For now, we'll skip authentication since this is a demo
    # In production, implement proper JWT token validation
    return True

@router.get("/", response_model=UsersListResponse)
async def get_users(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search by email or name"),
    role: Optional[str] = Query(None, description="Filter by role"),
    status: Optional[str] = Query(None, description="Filter by status (active/inactive)"),
    admin_access: bool = Depends(verify_admin_access)
):
    """Get paginated list of users with filtering"""
    try:
        # Since we don't have a database yet, we'll work with localStorage data
        # In a real implementation, this would query the database
        
        # For now, return mock data structure that matches what the frontend expects
        # The frontend will handle the actual data fetching from localStorage
        
        users = []
        total_count = 0
        
        # This endpoint will be enhanced when we have proper database integration
        # For now, it serves as a placeholder for the API structure
        
        return UsersListResponse(
            success=True,
            data={
                "users": users,
                "totalCount": total_count,
                "totalPages": max(1, (total_count + limit - 1) // limit),
                "currentPage": page,
                "filters": {
                    "search": search,
                    "role": role,
                    "status": status
                }
            },
            message="User data will be fetched from frontend storage"
        )
        
    except Exception as e:
        logger.error(f"Error getting users: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{user_id}", response_model=UsersListResponse)
async def get_user(
    user_id: str,
    admin_access: bool = Depends(verify_admin_access)
):
    """Get specific user by ID"""
    try:
        # Placeholder for user retrieval
        # In production, this would query the database
        
        return UsersListResponse(
            success=True,
            data={"user": None},
            message="User retrieval not implemented yet"
        )
        
    except Exception as e:
        logger.error(f"Error getting user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/{user_id}", response_model=UsersListResponse)
async def update_user(
    user_id: str,
    user_data: UserUpdateRequest,
    admin_access: bool = Depends(verify_admin_access)
):
    """Update user information"""
    try:
        # Placeholder for user update
        # In production, this would update the database
        
        return UsersListResponse(
            success=True,
            data={"user": user_data.dict()},
            message="User update functionality will be implemented with database integration"
        )
        
    except Exception as e:
        logger.error(f"Error updating user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.delete("/{user_id}", response_model=UsersListResponse)
async def delete_user(
    user_id: str,
    admin_access: bool = Depends(verify_admin_access)
):
    """Delete user"""
    try:
        # Placeholder for user deletion
        # In production, this would delete from database
        
        return UsersListResponse(
            success=True,
            data={"deleted": True},
            message="User deletion functionality will be implemented with database integration"
        )
        
    except Exception as e:
        logger.error(f"Error deleting user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/", response_model=UsersListResponse)
async def create_user(
    user_data: UserCreateRequest,
    admin_access: bool = Depends(verify_admin_access)
):
    """Create new user"""
    try:
        # Placeholder for user creation
        # In production, this would create in database
        
        new_user = {
            "id": f"admin-created-{datetime.now().timestamp()}",
            "email": user_data.email,
            "firstName": user_data.firstName,
            "lastName": user_data.lastName,
            "phone": user_data.phone,
            "role": user_data.role,
            "isActive": True,
            "createdAt": datetime.now().isoformat(),
            "registrationSource": "admin"
        }
        
        return UsersListResponse(
            success=True,
            data={"user": new_user},
            message="User creation functionality will be implemented with database integration"
        )
        
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.patch("/{user_id}/status", response_model=UsersListResponse)
async def update_user_status(
    user_id: str,
    is_active: bool,
    admin_access: bool = Depends(verify_admin_access)
):
    """Update user active status"""
    try:
        # Placeholder for status update
        # In production, this would update the database
        
        return UsersListResponse(
            success=True,
            data={"user_id": user_id, "isActive": is_active},
            message="User status update functionality will be implemented with database integration"
        )
        
    except Exception as e:
        logger.error(f"Error updating user status {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.patch("/{user_id}/role", response_model=UsersListResponse)
async def update_user_role(
    user_id: str,
    role: str,
    admin_access: bool = Depends(verify_admin_access)
):
    """Update user role"""
    try:
        # Validate role
        valid_roles = ["user", "moderator", "admin"]
        if role not in valid_roles:
            raise HTTPException(status_code=400, detail=f"Invalid role. Must be one of: {valid_roles}")
        
        # Placeholder for role update
        # In production, this would update the database
        
        return UsersListResponse(
            success=True,
            data={"user_id": user_id, "role": role},
            message="User role update functionality will be implemented with database integration"
        )
        
    except Exception as e:
        logger.error(f"Error updating user role {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/stats/summary", response_model=UsersListResponse)
async def get_user_stats(
    admin_access: bool = Depends(verify_admin_access)
):
    """Get user statistics for admin dashboard"""
    try:
        # Placeholder for user statistics
        # In production, this would query the database for real stats
        
        stats = {
            "totalUsers": 0,
            "activeUsers": 0,
            "inactiveUsers": 0,
            "usersByRole": {
                "admin": 0,
                "moderator": 0,
                "user": 0
            },
            "recentRegistrations": {
                "today": 0,
                "thisWeek": 0,
                "thisMonth": 0
            },
            "registrationSources": {
                "supabase": 0,
                "backend": 0,
                "fallback": 0
            }
        }
        
        return UsersListResponse(
            success=True,
            data={"stats": stats},
            message="User statistics will be calculated from actual data"
        )
        
    except Exception as e:
        logger.error(f"Error getting user stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
