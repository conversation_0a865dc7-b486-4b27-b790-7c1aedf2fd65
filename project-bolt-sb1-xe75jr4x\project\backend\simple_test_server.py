#!/usr/bin/env python3
"""
Простейший тестовый сервер на порту 8001
"""

import json
import http.server
import socketserver
from datetime import datetime
import random

PORT = 8001

class SimpleTestHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        # CORS заголовки
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        if self.path == '/health':
            response = {"status": "ok", "server": "simple_test_server", "port": PORT}
        elif '/parsing/results' in self.path:
            response = {
                "success": True,
                "data": {
                    "results": [
                        {
                            "id": 1,
                            "exchanger_name": "Test Exchanger 1",
                            "status": "success",
                            "parsing_timestamp": datetime.now().isoformat(),
                            "execution_time_ms": 1500,
                            "parsed_pairs_count": 5
                        },
                        {
                            "id": 2,
                            "exchanger_name": "Test Exchanger 2",
                            "status": "failed",
                            "parsing_timestamp": datetime.now().isoformat(),
                            "execution_time_ms": 2000,
                            "parsed_pairs_count": 0,
                            "error_message": "Connection timeout"
                        }
                    ]
                }
            }
        elif '/historical-rates/trends' in self.path:
            response = {
                "success": True,
                "data": {
                    "trends": [
                        {
                            "time_period": "2025-08-11",
                            "currency_pair": "RUB/THB",
                            "avg_buy_rate": 2.45,
                            "avg_sell_rate": 2.55,
                            "data_points": 10
                        },
                        {
                            "time_period": "2025-08-10",
                            "currency_pair": "RUB/THB",
                            "avg_buy_rate": 2.44,
                            "avg_sell_rate": 2.54,
                            "data_points": 12
                        }
                    ]
                }
            }
        else:
            response = {"error": "Not found", "path": self.path}
        
        json_data = json.dumps(response)
        self.wfile.write(json_data.encode('utf-8'))
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

if __name__ == "__main__":
    print(f"🚀 Запуск простого тестового сервера на порту {PORT}")
    print(f"🔗 Health: http://localhost:{PORT}/health")
    
    with socketserver.TCPServer(("", PORT), SimpleTestHandler) as httpd:
        print(f"✅ Сервер запущен на порту {PORT}")
        httpd.serve_forever()
