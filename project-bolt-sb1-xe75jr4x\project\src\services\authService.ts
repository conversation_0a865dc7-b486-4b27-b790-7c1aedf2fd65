// Authentication service with JWT and secure session management
import { createClient, SupabaseClient, AuthError } from '@supabase/supabase-js';
import { config } from '../config/environment';
import { supabaseService } from './supabaseClient';

export interface User {
  id: string;
  email: string;
  role: 'admin' | 'moderator' | 'user';
  permissions: string[];
  lastLogin?: string;
  isActive: boolean;
  twoFactorEnabled: boolean;
}

export interface LoginCredentials {
  email: string;
  password: string;
  twoFactorCode?: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role?: 'user';
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}
class AuthService {
  private tokenRefreshTimer: NodeJS.Timeout | null = null;
  constructor() {
    this.setupTokenRefresh();
  }
  // Get Supabase client from centralized service
  private get supabase(): SupabaseClient | null {
    return supabaseService.client;
  }

  // Check if we're in fallback mode
  private get fallbackMode(): boolean {
    return !supabaseService.isConnected;
  }

  // Login with email and password
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    if (this.fallbackMode) {
      return this.fallbackLogin(credentials);
    }

    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        // If network error, try fallback
        if (error.message.includes('Failed to fetch') || error.message.includes('fetch')) {
          console.warn('Supabase connection failed, using fallback authentication');
          return this.fallbackLogin(credentials);
        }
        throw new Error(this.getErrorMessage(error.message));
      }

      if (!data.user || !data.session) {
        throw new Error('Ошибка аутентификации');
      }

      // Get user profile with role and permissions
      const userProfile = await this.getUserProfile(data.user.id);
      
      const authResponse: AuthResponse = {
        user: {
          id: data.user.id,
          email: data.user.email!,
          role: userProfile.role,
          permissions: userProfile.permissions,
          lastLogin: new Date().toISOString(),
          isActive: true,
          twoFactorEnabled: userProfile.twoFactorEnabled || false,
        },
        accessToken: data.session.access_token,
        refreshToken: data.session.refresh_token,
        expiresIn: data.session.expires_in || 3600,
      };

      // Store tokens securely
      this.storeTokens(authResponse);
      
      // Update last login
      await this.updateLastLogin(data.user.id);

      return authResponse;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // Register new user
  async register(userData: RegisterData): Promise<AuthResponse> {
    if (this.fallbackMode) {
      return this.fallbackRegister(userData);
    }

    try {
      const { data, error } = await this.supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            name: userData.name,
            role: userData.role || 'user',
          },
        },
      });

      if (error) {
        // If network error, try fallback
        if (error.message.includes('Failed to fetch') || error.message.includes('fetch')) {
          console.warn('Supabase connection failed, using fallback authentication');
          return this.fallbackRegister(userData);
        }
        throw new Error(this.getErrorMessage(error.message));
      }

      if (!data.user || !data.session) {
        throw new Error('Пользователь создан, но требуется подтверждение email');
      }

      // Create user profile
      await this.createUserProfile(data.user.id, userData);

      const authResponse: AuthResponse = {
        user: {
          id: data.user.id,
          email: data.user.email!,
          role: userData.role || 'user',
          permissions: this.getDefaultPermissions(userData.role || 'user'),
          isActive: true,
          twoFactorEnabled: false,
        },
        accessToken: data.session.access_token,
        refreshToken: data.session.refresh_token,
        expiresIn: data.session.expires_in || 3600,
      };

      this.storeTokens(authResponse);
      return authResponse;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  // Logout user
  async logout(): Promise<void> {
    if (this.fallbackMode) {
      this.clearTokens();
      this.clearTokenRefreshTimer();
      return;
    }

    try {
      await this.supabase.auth.signOut();
      this.clearTokens();
      this.clearTokenRefreshTimer();
    } catch (error) {
      console.error('Logout error:', error);
      // Clear local tokens even if server logout fails
      this.clearTokens();
    }
  }

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    if (this.fallbackMode) {
      return this.getFallbackCurrentUser();
    }

    try {
      const { data: { user } } = await this.supabase.auth.getUser();

      if (!user) {
        return this.getFallbackCurrentUser();
      }

      const userProfile = await this.getUserProfile(user.id);

      return {
        id: user.id,
        email: user.email!,
        role: userProfile.role,
        permissions: userProfile.permissions,
        lastLogin: userProfile.lastLogin,
        isActive: userProfile.isActive,
        twoFactorEnabled: userProfile.twoFactorEnabled || false,
      };
    } catch (error) {
      console.error('Get current user error:', error);
      // If Supabase fails, fall back to local auth
      return this.getFallbackCurrentUser();
    }
  }

  // Refresh access token
  async refreshToken(): Promise<string | null> {
    if (this.fallbackMode) {
      return this.fallbackRefreshToken();
    }

    try {
      const { data, error } = await this.supabase.auth.refreshSession();
      
      if (error || !data.session) {
        console.warn('Token refresh failed:', error?.message || 'No session data');
        this.logout(); // Force logout on refresh failure
        return null;
      }

      // Update stored tokens
      const currentAuth = this.getStoredAuth();
      if (currentAuth) {
        currentAuth.accessToken = data.session.access_token;
        currentAuth.refreshToken = data.session.refresh_token;
        this.storeTokens(currentAuth);
      }

      return data.session.access_token;
    } catch (error) {
      console.error('Token refresh error:', error);
      this.logout(); // Force logout on refresh failure
      return null;
    }
  }

  // Fallback authentication methods
  private async fallbackLogin(credentials: LoginCredentials): Promise<AuthResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // First, check against users registered via userRegistrationService
    const registeredUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
    const registeredUser = registeredUsers.find((user: any) =>
      user.email === credentials.email && user.password === credentials.password
    );

    if (registeredUser) {
      const authResponse: AuthResponse = {
        user: {
          id: registeredUser.id || `user-${Date.now()}`,
          email: registeredUser.email,
          role: registeredUser.role || 'user',
          permissions: this.getDefaultPermissions(registeredUser.role || 'user'),
          isActive: true,
          twoFactorEnabled: false,
          lastLogin: new Date().toISOString(),
        },
        accessToken: `fallback-token-${Date.now()}`,
        refreshToken: `fallback-refresh-${Date.now()}`,
        expiresIn: 3600,
      };

      this.storeTokens(authResponse);
      return authResponse;
    }

    // Then check against users registered via authService fallback
    const fallbackUsers = JSON.parse(localStorage.getItem('fallback_users') || '[]');
    const fallbackUser = fallbackUsers.find((user: any) =>
      user.email === credentials.email && user.password === credentials.password
    );

    if (fallbackUser) {
      const authResponse: AuthResponse = {
        user: {
          id: fallbackUser.id,
          email: fallbackUser.email,
          role: fallbackUser.role,
          permissions: this.getDefaultPermissions(fallbackUser.role),
          isActive: true,
          twoFactorEnabled: false,
          lastLogin: new Date().toISOString(),
        },
        accessToken: `fallback-token-${Date.now()}`,
        refreshToken: `fallback-refresh-${Date.now()}`,
        expiresIn: 3600,
      };

      this.storeTokens(authResponse);
      return authResponse;
    }

    // Finally, check against hardcoded admin credentials for demo
    const validCredentials = [
      { email: '<EMAIL>', password: 'admin123', role: 'admin' as const },
      { email: '<EMAIL>', password: 'mod123', role: 'moderator' as const },
      { email: '<EMAIL>', password: 'user123', role: 'user' as const },
    ];

    const validUser = validCredentials.find(
      cred => cred.email === credentials.email && cred.password === credentials.password
    );

    if (!validUser) {
      throw new Error('Неверный email или пароль');
    }

    const authResponse: AuthResponse = {
      user: {
        id: `fallback-${Date.now()}`,
        email: validUser.email,
        role: validUser.role,
        permissions: this.getDefaultPermissions(validUser.role),
        lastLogin: new Date().toISOString(),
        isActive: true,
        twoFactorEnabled: false,
      },
      accessToken: `fallback-token-${Date.now()}`,
      refreshToken: `fallback-refresh-${Date.now()}`,
      expiresIn: 3600,
    };

    this.storeTokens(authResponse);
    return authResponse;
  }

  private async fallbackRegister(userData: RegisterData): Promise<AuthResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Use the same storage key as userRegistrationService for consistency
    const existingUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
    const userExists = existingUsers.some((user: any) => user.email === userData.email);

    if (userExists) {
      throw new Error('Пользователь с таким email уже существует');
    }

    // Create new user with same structure as userRegistrationService
    const newUser = {
      id: `user-${Date.now()}`,
      email: userData.email,
      password: userData.password, // Store password for login validation
      firstName: userData.name.split(' ')[0] || '',
      lastName: userData.name.split(' ').slice(1).join(' ') || '',
      phone: '',
      role: userData.role || 'user',
      isActive: true,
      createdAt: new Date().toISOString(),
    };

    existingUsers.push(newUser);
    localStorage.setItem('registered_users', JSON.stringify(existingUsers));

    const authResponse: AuthResponse = {
      user: {
        id: newUser.id,
        email: newUser.email,
        role: newUser.role as 'admin' | 'moderator' | 'user',
        permissions: this.getDefaultPermissions(newUser.role),
        isActive: true,
        twoFactorEnabled: false,
      },
      accessToken: `fallback-token-${Date.now()}`,
      refreshToken: `fallback-refresh-${Date.now()}`,
      expiresIn: 3600,
    };

    this.storeTokens(authResponse);
    return authResponse;
  }

  private getFallbackCurrentUser(): User | null {
    const auth = this.getStoredAuth();
    return auth?.user || null;
  }

  private async fallbackRefreshToken(): Promise<string | null> {
    const auth = this.getStoredAuth();
    if (!auth) return null;

    // Generate new token
    const newToken = `fallback-token-${Date.now()}`;
    auth.accessToken = newToken;
    auth.expiresAt = Date.now() + (3600 * 1000);
    
    this.storeTokens(auth);
    return newToken;
  }

  // Check if user has permission
  hasPermission(permission: string): boolean {
    const user = this.getStoredAuth()?.user;
    if (!user) return false;
    
    // Admin has all permissions
    if (user.role === 'admin') return true;
    
    return user.permissions.includes(permission);
  }

  // Check if user has role
  hasRole(role: string): boolean {
    const user = this.getStoredAuth()?.user;
    return user?.role === role;
  }

  // Get user profile from database
  private async getUserProfile(userId: string) {
    try {
      const { data, error } = await this.supabase!
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.warn('User profile not found, creating default:', error);
        // Return default profile if not found
        return {
          role: 'user' as const,
          permissions: this.getDefaultPermissions('user'),
          isActive: true,
          twoFactorEnabled: false,
          lastLogin: undefined,
        };
      }

      return {
        role: data.role,
        permissions: Array.isArray(data.permissions) ? data.permissions : this.getDefaultPermissions(data.role),
        isActive: data.is_active,
        twoFactorEnabled: data.two_factor_enabled,
        lastLogin: data.last_login,
      };
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return {
        role: 'user' as const,
        permissions: this.getDefaultPermissions('user'),
        isActive: true,
        twoFactorEnabled: false,
        lastLogin: undefined,
      };
    }
  }

  // Create user profile
  private async createUserProfile(userId: string, userData: RegisterData) {
    try {
      const { error } = await this.supabase!
        .from('user_profiles')
        .insert({
          id: userId,
          name: userData.name,
          role: userData.role || 'user',
          permissions: this.getDefaultPermissions(userData.role || 'user'),
          is_active: true,
          two_factor_enabled: false,
          created_at: new Date().toISOString(),
        });

      if (error) {
        console.error('Error creating user profile:', error);
        // Don't throw error, as the user is already created in auth.users
        // The trigger should handle profile creation
      }
    } catch (error) {
      console.error('Error creating user profile:', error);
      // Don't throw error, continue with registration
    }
  }

  // Update last login timestamp
  private async updateLastLogin(userId: string) {
    try {
      await this.supabase!
        .from('user_profiles')
        .update({ last_login: new Date().toISOString() })
        .eq('id', userId);
    } catch (error) {
      console.error('Error updating last login:', error);
      // Don't throw error, this is not critical
    }
  }

  // Get default permissions for role
  private getDefaultPermissions(role: string): string[] {
    switch (role) {
      case 'admin':
        return ['*']; // All permissions
      case 'moderator':
        return ['reviews.moderate', 'users.view', 'exchangers.edit'];
      case 'user':
        return ['reviews.create', 'reviews.view'];
      default:
        return [];
    }
  }

  // Store tokens securely
  private storeTokens(auth: AuthResponse): void {
    try {
      // Use secure storage
      const authData = {
        user: auth.user,
        accessToken: auth.accessToken,
        refreshToken: auth.refreshToken,
        expiresAt: Date.now() + (auth.expiresIn * 1000),
      };
      
      localStorage.setItem('auth', JSON.stringify(authData));

      // Set up automatic token refresh
      this.setupTokenRefresh();

      // Dispatch custom event to notify auth context
      window.dispatchEvent(new CustomEvent('authStateChanged'));
    } catch (error) {
      console.error('Error storing tokens:', error);
    }
  }

  // Get stored authentication data
  private getStoredAuth(): AuthResponse | null {
    try {
      const stored = localStorage.getItem('auth');
      if (!stored) return null;

      const auth = JSON.parse(stored);

      // Check if token is expired
      if (Date.now() >= auth.expiresAt) {
        this.clearTokens();
        return null;
      }

      return auth;
    } catch (error) {
      console.error('Error getting stored auth:', error);
      this.clearTokens();
      return null;
    }
  }

  // Clear stored tokens
  private clearTokens(): void {
    localStorage.removeItem('auth');
    this.clearTokenRefreshTimer();

    // Dispatch custom event to notify auth context
    window.dispatchEvent(new CustomEvent('authStateChanged'));
  }

  // Setup automatic token refresh
  private setupTokenRefresh(): void {
    this.clearTokenRefreshTimer();
    
    const auth = this.getStoredAuth();
    if (!auth) return;
    
    // Refresh token 5 minutes before expiry
    const refreshTime = auth.expiresAt - Date.now() - (5 * 60 * 1000);
    
    if (refreshTime > 0) {
      this.tokenRefreshTimer = setTimeout(() => {
        this.refreshToken();
      }, refreshTime);
    }
  }

  // Clear token refresh timer
  private clearTokenRefreshTimer(): void {
    if (this.tokenRefreshTimer) {
      clearTimeout(this.tokenRefreshTimer);
      this.tokenRefreshTimer = null;
    }
  }

  // Get user-friendly error messages
  private getErrorMessage(error: string): string {
    switch (error) {
      case 'Invalid login credentials':
        return 'Неверный email или пароль';
      case 'Email not confirmed':
        return 'Подтвердите email перед входом';
      case 'Too many requests':
        return 'Слишком много попыток входа. Попробуйте позже';
      case 'User not found':
        return 'Пользователь не найден';
      case 'Weak password':
        return 'Пароль должен содержать минимум 8 символов';
      default:
        return error || 'Произошла ошибка аутентификации';
    }
  }

  // Get access token for API requests
  getAccessToken(): string | null {
    const auth = this.getStoredAuth();
    return auth?.accessToken || null;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.getStoredAuth() !== null;
  }

  // Check if user is admin
  isAdmin(): boolean {
    return this.hasRole('admin');
  }

  // Check if Supabase is properly configured
  isSupabaseConfigured(): boolean {
    return supabaseService.isConnected;
  }
}

export const authService = new AuthService();
export default authService;