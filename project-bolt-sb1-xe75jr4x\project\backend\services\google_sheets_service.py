"""
Google Sheets service for fetching exchange rates
"""
import logging
import gspread
from google.oauth2.service_account import Credentials
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor

from config.settings import settings

logger = logging.getLogger(__name__)


class GoogleSheetsService:
    """Service for interacting with Google Sheets API"""
    
    def __init__(self):
        self.credentials_file = settings.GOOGLE_SHEETS_CREDENTIALS_FILE
        self.spreadsheet_id = settings.GOOGLE_SHEETS_SPREADSHEET_ID
        self.worksheet_name = settings.GOOGLE_SHEETS_WORKSHEET_NAME
        self.client = None
        self.spreadsheet = None
        self.worksheet = None
        self._executor = ThreadPoolExecutor(max_workers=2)
        
    async def initialize(self) -> bool:
        """Initialize Google Sheets client"""
        try:
            # Check if credentials file exists
            import os
            if not os.path.exists(self.credentials_file):
                logger.warning(f"Google Sheets credentials file not found: {self.credentials_file}")
                logger.info("Running in fallback mode without Google Sheets integration")
                return False

            # Define the scope
            scope = [
                'https://www.googleapis.com/auth/spreadsheets.readonly',
                'https://www.googleapis.com/auth/drive.readonly'
            ]

            # Load credentials
            credentials = Credentials.from_service_account_file(
                self.credentials_file,
                scopes=scope
            )

            # Initialize client in thread pool
            loop = asyncio.get_event_loop()
            self.client = await loop.run_in_executor(
                self._executor,
                gspread.authorize,
                credentials
            )
            
            # Open spreadsheet
            self.spreadsheet = await loop.run_in_executor(
                self._executor,
                self.client.open_by_key,
                self.spreadsheet_id
            )
            
            # Get worksheet
            self.worksheet = await loop.run_in_executor(
                self._executor,
                self.spreadsheet.worksheet,
                self.worksheet_name
            )
            
            logger.info("Google Sheets service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Google Sheets service: {e}")
            return False
    
    async def fetch_exchange_rates(self) -> Optional[Dict[str, Any]]:
        """Fetch exchange rates from Google Sheets"""
        if not self.worksheet:
            if not await self.initialize():
                logger.info("Google Sheets not available, using fallback data")
                return await self._get_fallback_rates()
        
        try:
            loop = asyncio.get_event_loop()
            
            # Get all records from the worksheet
            records = await loop.run_in_executor(
                self._executor,
                self.worksheet.get_all_records
            )
            
            if not records:
                logger.warning("No records found in Google Sheets")
                return None
            
            # Process the data
            exchange_rates = {}
            exchangers_data = []
            
            for record in records:
                try:
                    exchanger_name = record.get('Exchanger Name', '').strip()
                    if not exchanger_name:
                        continue
                    
                    # Extract rates
                    rates = {}
                    rate_fields = [
                        ('RUB_BUY', 'RUB'),
                        ('RUB_SELL', 'RUB'),
                        ('USD_BUY', 'USD'),
                        ('USD_SELL', 'USD'),
                        ('USDT_BUY', 'USDT'),
                        ('USDT_SELL', 'USDT'),
                        ('THB_RUB_BUY', 'THB/RUB'),
                        ('THB_RUB_SELL', 'THB/RUB'),
                        ('THB_USDT_BUY', 'THB/USDT'),
                        ('THB_USDT_SELL', 'THB/USDT'),
                        ('USDT_BAHT_BUY', 'USDT/BAHT'),
                        ('USDT_BAHT_SELL', 'USDT/BAHT')
                    ]
                    
                    for field, currency in rate_fields:
                        value = record.get(field)
                        if value and str(value).replace('.', '').replace('-', '').isdigit():
                            if currency not in rates:
                                rates[currency] = {}
                            
                            if 'BUY' in field:
                                rates[currency]['buy'] = float(value)
                            else:
                                rates[currency]['sell'] = float(value)
                    
                    # Calculate changes (mock for now)
                    for currency in rates:
                        if 'buy' in rates[currency] and 'sell' in rates[currency]:
                            rates[currency]['change'] = round(
                                (rates[currency]['sell'] - rates[currency]['buy']) / rates[currency]['buy'] * 100, 2
                            )
                    
                    exchanger_data = {
                        'name': exchanger_name,
                        'rates': rates,
                        'last_updated': datetime.now().isoformat(),
                        'district': record.get('District', 'unknown'),
                        'address': record.get('Address', ''),
                        'phone': record.get('Phone', ''),
                        'hours': record.get('Hours', '9:00 - 20:00')
                    }
                    
                    exchangers_data.append(exchanger_data)
                    
                except Exception as e:
                    logger.warning(f"Error processing record for {exchanger_name}: {e}")
                    continue
            
            result = {
                'exchangers': exchangers_data,
                'last_updated': datetime.now().isoformat(),
                'total_count': len(exchangers_data)
            }
            
            logger.info(f"Successfully fetched {len(exchangers_data)} exchangers from Google Sheets")
            return result
            
        except Exception as e:
            logger.error(f"Error fetching exchange rates from Google Sheets: {e}")
            return None
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test connection to Google Sheets"""
        try:
            if not await self.initialize():
                return {
                    'success': False,
                    'message': 'Failed to initialize Google Sheets connection'
                }
            
            loop = asyncio.get_event_loop()
            
            # Try to get basic info about the spreadsheet
            spreadsheet_info = await loop.run_in_executor(
                self._executor,
                lambda: {
                    'title': self.spreadsheet.title,
                    'id': self.spreadsheet.id,
                    'worksheets': [ws.title for ws in self.spreadsheet.worksheets()]
                }
            )
            
            # Try to get a few rows
            sample_data = await loop.run_in_executor(
                self._executor,
                lambda: self.worksheet.get_all_records()[:3]
            )
            
            return {
                'success': True,
                'message': 'Connection successful',
                'spreadsheet_info': spreadsheet_info,
                'sample_data': sample_data
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Connection failed: {str(e)}'
            }

    async def _get_fallback_rates(self) -> Dict[str, Any]:
        """Generate fallback exchange rates when Google Sheets is not available"""
        import random

        logger.info("Generating fallback exchange rates")

        # Generate realistic exchange rates with some variation
        base_rates = {
            'RUB': {'buy': 35.2, 'sell': 36.8},
            'USD': {'buy': 34.5, 'sell': 35.2},
            'USDT': {'buy': 34.8, 'sell': 35.5},
            'THB/RUB': {'buy': 2.1, 'sell': 2.3},
            'THB/USDT': {'buy': 0.029, 'sell': 0.031},
            'USDT/BAHT': {'buy': 32.1, 'sell': 34.5}
        }

        # Add some random variation (±2%)
        exchange_rates = {}
        exchangers_data = []

        fallback_exchangers = [
            {'name': 'Bangkok Exchange Center', 'district': 'Sukhumvit', 'address': '123 Sukhumvit Rd'},
            {'name': 'Pattaya Money Exchange', 'district': 'Pattaya', 'address': '456 Beach Rd'},
            {'name': 'Phuket Currency Exchange', 'district': 'Patong', 'address': '789 Bangla Rd'},
            {'name': 'Chiang Mai Exchange', 'district': 'Old City', 'address': '321 Thapae Rd'},
            {'name': 'Hua Hin Money Changer', 'district': 'Hua Hin', 'address': '654 Petchkasem Rd'}
        ]

        for exchanger in fallback_exchangers:
            rates = {}
            for currency, base_rate in base_rates.items():
                variation = random.uniform(0.98, 1.02)  # ±2% variation
                rates[currency] = {
                    'buy': round(base_rate['buy'] * variation, 3),
                    'sell': round(base_rate['sell'] * variation, 3)
                }

            exchanger_data = {
                'name': exchanger['name'],
                'district': exchanger['district'],
                'address': exchanger['address'],
                'phone': f"+66 {random.randint(80, 99)} {random.randint(100, 999)} {random.randint(1000, 9999)}",
                'hours': '09:00-18:00',
                'rates': rates,
                'last_updated': datetime.now().isoformat(),
                'status': 'active'
            }

            exchangers_data.append(exchanger_data)
            exchange_rates[exchanger['name']] = exchanger_data

        return {
            'success': True,
            'source': 'fallback',
            'message': 'Using fallback exchange rates (Google Sheets not configured)',
            'data': exchange_rates,
            'exchangers': exchangers_data,
            'last_updated': datetime.now().isoformat(),
            'total_exchangers': len(exchangers_data)
        }


# Global instance
google_sheets_service = GoogleSheetsService()