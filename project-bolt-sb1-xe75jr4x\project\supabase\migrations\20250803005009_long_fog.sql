/*
  # Расширение системы обменников

  1. Новые таблицы
    - `additional_offices` - дополнительные офисы обменников
      - `id` (uuid, primary key)
      - `exchanger_id` (uuid, foreign key)
      - `name` (text)
      - `address` (text)
      - `district` (text)
      - `phone` (text)
      - `hours` (text)
      - `coordinates` (jsonb)
      - `status` (text)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `exchange_rates` - история курсов валют
      - `id` (uuid, primary key)
      - `exchanger_id` (uuid, foreign key)
      - `currency_pair` (text)
      - `buy_rate` (decimal)
      - `sell_rate` (decimal)
      - `source_url` (text)
      - `parsed_at` (timestamp)
      - `is_active` (boolean)
      - `created_at` (timestamp)
    
    - `parsing_logs` - логи парсинга
      - `id` (uuid, primary key)
      - `exchanger_id` (uuid, foreign key)
      - `status` (text)
      - `message` (text)
      - `error_details` (jsonb)
      - `parsed_rates_count` (integer)
      - `execution_time_ms` (integer)
      - `created_at` (timestamp)
    
    - `exchangers` - основная таблица обменников
      - `id` (uuid, primary key)
      - `name` (text)
      - `website_url` (text)
      - `parsing_enabled` (boolean)
      - `parsing_config` (jsonb)
      - `last_parsed_at` (timestamp)
      - `address` (text)
      - `district` (text)
      - `phone` (text)
      - `hours` (text)
      - `coordinates` (jsonb)
      - `status` (text)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Безопасность
    - Включить RLS для всех таблиц
    - Политики для администраторов и пользователей
    - Индексы для оптимизации запросов

  3. Триггеры
    - Автоматическое обновление updated_at
    - Валидация данных
*/

-- Создание таблицы обменников
CREATE TABLE IF NOT EXISTS exchangers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  website_url text,
  parsing_enabled boolean DEFAULT false,
  parsing_config jsonb DEFAULT '{}',
  last_parsed_at timestamptz,
  address text NOT NULL,
  district text NOT NULL,
  phone text NOT NULL,
  hours text NOT NULL DEFAULT '9:00 - 20:00',
  coordinates jsonb,
  status text DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Создание таблицы дополнительных офисов
CREATE TABLE IF NOT EXISTS additional_offices (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  exchanger_id uuid NOT NULL REFERENCES exchangers(id) ON DELETE CASCADE,
  name text NOT NULL,
  address text NOT NULL,
  district text NOT NULL,
  phone text NOT NULL,
  hours text NOT NULL DEFAULT '9:00 - 20:00',
  coordinates jsonb,
  status text DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Создание таблицы курсов валют
CREATE TABLE IF NOT EXISTS exchange_rates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  exchanger_id uuid NOT NULL REFERENCES exchangers(id) ON DELETE CASCADE,
  currency_pair text NOT NULL CHECK (currency_pair IN ('THB/RUB', 'USDT/BAHT', 'USD/THB', 'RUB/THB')),
  buy_rate decimal(10,4) NOT NULL,
  sell_rate decimal(10,4) NOT NULL,
  source_url text,
  parsed_at timestamptz DEFAULT now(),
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Создание таблицы логов парсинга
CREATE TABLE IF NOT EXISTS parsing_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  exchanger_id uuid REFERENCES exchangers(id) ON DELETE CASCADE,
  status text NOT NULL CHECK (status IN ('success', 'error', 'warning', 'skipped')),
  message text NOT NULL,
  error_details jsonb,
  parsed_rates_count integer DEFAULT 0,
  execution_time_ms integer DEFAULT 0,
  created_at timestamptz DEFAULT now()
);

-- Создание индексов для оптимизации
CREATE INDEX IF NOT EXISTS idx_additional_offices_exchanger_id ON additional_offices(exchanger_id);
CREATE INDEX IF NOT EXISTS idx_additional_offices_status ON additional_offices(status);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_exchanger_id ON exchange_rates(exchanger_id);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_currency_pair ON exchange_rates(currency_pair);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_is_active ON exchange_rates(is_active);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_parsed_at ON exchange_rates(parsed_at);
CREATE INDEX IF NOT EXISTS idx_parsing_logs_exchanger_id ON parsing_logs(exchanger_id);
CREATE INDEX IF NOT EXISTS idx_parsing_logs_status ON parsing_logs(status);
CREATE INDEX IF NOT EXISTS idx_parsing_logs_created_at ON parsing_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_exchangers_status ON exchangers(status);
CREATE INDEX IF NOT EXISTS idx_exchangers_parsing_enabled ON exchangers(parsing_enabled);

-- Включение RLS для всех таблиц
ALTER TABLE exchangers ENABLE ROW LEVEL SECURITY;
ALTER TABLE additional_offices ENABLE ROW LEVEL SECURITY;
ALTER TABLE exchange_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE parsing_logs ENABLE ROW LEVEL SECURITY;

-- Политики для таблицы exchangers
CREATE POLICY "Администраторы могут управлять обменниками"
  ON exchangers
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role = 'admin'
    )
  );

CREATE POLICY "Пользователи могут читать активные обменники"
  ON exchangers
  FOR SELECT
  TO authenticated
  USING (status = 'active');

-- Политики для таблицы additional_offices
CREATE POLICY "Администраторы могут управлять офисами"
  ON additional_offices
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role = 'admin'
    )
  );

CREATE POLICY "Пользователи могут читать активные офисы"
  ON additional_offices
  FOR SELECT
  TO authenticated
  USING (status = 'active');

-- Политики для таблицы exchange_rates
CREATE POLICY "Администраторы могут управлять курсами"
  ON exchange_rates
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role = 'admin'
    )
  );

CREATE POLICY "Пользователи могут читать активные курсы"
  ON exchange_rates
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- Политики для таблицы parsing_logs
CREATE POLICY "Администраторы могут читать логи парсинга"
  ON parsing_logs
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role = 'admin'
    )
  );

-- Функция для обновления updated_at
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Триггеры для автоматического обновления updated_at
CREATE TRIGGER update_exchangers_updated_at
  BEFORE UPDATE ON exchangers
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_additional_offices_updated_at
  BEFORE UPDATE ON additional_offices
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();