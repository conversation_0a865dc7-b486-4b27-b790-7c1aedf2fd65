#!/usr/bin/env python3
"""
Минимальный тестовый сервер для диагностики проблем
"""
import json
from datetime import datetime, timedelta
import random
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Создаем FastAPI приложение
app = FastAPI(title="Test Historical Rates API", version="1.0.0")

# Добавляем CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5174", "http://localhost:3000", "http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def generate_mock_rates(limit=10, currency_pair=None, exchanger_id=None, days_back=7):
    """Генерация тестовых данных"""
    exchangers = [
        {"id": "superrich", "name": "SuperRich Thailand"},
        {"id": "vasu", "name": "Vasu Exchange"},
        {"id": "happy_rich", "name": "Happy Rich Exchange"},
        {"id": "grand_superrich", "name": "Grand SuperRich"},
        {"id": "siam_exchange", "name": "Siam Exchange"}
    ]
    
    currency_pairs = ["RUB/THB", "THB/RUB", "USDT/THB", "USD/THB", "EUR/THB"]
    
    rates = []
    for i in range(limit):
        exchanger = random.choice(exchangers)
        pair = currency_pair if currency_pair else random.choice(currency_pairs)
        
        # Фильтрация по обменнику
        if exchanger_id and exchanger["id"] != exchanger_id:
            continue
            
        timestamp = datetime.now() - timedelta(days=random.randint(0, days_back))
        
        # Генерация реалистичных курсов
        if pair == "RUB/THB":
            buy_rate = round(random.uniform(2.40, 2.60), 4)
            sell_rate = round(buy_rate + random.uniform(0.05, 0.15), 4)
        elif pair == "THB/RUB":
            buy_rate = round(random.uniform(0.38, 0.42), 4)
            sell_rate = round(buy_rate + random.uniform(0.01, 0.03), 4)
        elif pair == "USDT/THB":
            buy_rate = round(random.uniform(34.50, 36.50), 2)
            sell_rate = round(buy_rate + random.uniform(0.20, 0.50), 2)
        else:
            buy_rate = round(random.uniform(30.00, 40.00), 2)
            sell_rate = round(buy_rate + random.uniform(0.50, 1.00), 2)
        
        rate = {
            "id": i + 1,
            "exchanger_id": exchanger["id"],
            "exchanger_name": exchanger["name"],
            "currency_pair": pair,
            "buy_rate": buy_rate,
            "sell_rate": sell_rate,
            "spread": round(sell_rate - buy_rate, 4),
            "spread_percentage": round(((sell_rate - buy_rate) / buy_rate) * 100, 2),
            "data_quality_score": random.randint(85, 100),
            "validation_status": "valid",
            "parsed_at": timestamp.isoformat()
        }
        rates.append(rate)
    
    return rates

@app.get("/")
async def root():
    """Корневой endpoint для проверки работы сервера"""
    return {
        "message": "Test Historical Rates API is running!",
        "timestamp": datetime.now().isoformat(),
        "status": "ok"
    }

@app.get("/api/v1/parsing/historical-rates")
async def get_historical_rates(
    limit: int = 100,
    days_back: int = 7,
    currency_pair: str = None,
    exchanger_id: str = None
):
    """Получение исторических курсов валют"""
    try:
        print(f"📊 Request: limit={limit}, days_back={days_back}, currency_pair={currency_pair}, exchanger_id={exchanger_id}")
        
        # Генерируем тестовые данные
        rates = generate_mock_rates(limit, currency_pair, exchanger_id, days_back)
        
        # Доступные фильтры
        currency_pairs = [
            {"currency_pair": "RUB/THB", "records_count": 150},
            {"currency_pair": "THB/RUB", "records_count": 120},
            {"currency_pair": "USDT/THB", "records_count": 100},
            {"currency_pair": "USD/THB", "records_count": 80},
            {"currency_pair": "EUR/THB", "records_count": 60}
        ]
        
        exchangers = [
            {"exchanger_id": "superrich", "exchanger_name": "SuperRich Thailand", "records_count": 100},
            {"exchanger_id": "vasu", "exchanger_name": "Vasu Exchange", "records_count": 95},
            {"exchanger_id": "happy_rich", "exchanger_name": "Happy Rich Exchange", "records_count": 90},
            {"exchanger_id": "grand_superrich", "exchanger_name": "Grand SuperRich", "records_count": 85},
            {"exchanger_id": "siam_exchange", "exchanger_name": "Siam Exchange", "records_count": 80}
        ]
        
        response = {
            "success": True,
            "data": {
                "rates": rates,
                "total_count": len(rates) + random.randint(50, 200),  # Симулируем больше данных
                "filters": {
                    "currency_pair": currency_pair,
                    "exchanger_id": exchanger_id,
                    "days_back": days_back,
                    "date_range": {
                        "start": (datetime.now() - timedelta(days=days_back)).date().isoformat(),
                        "end": datetime.now().date().isoformat()
                    }
                },
                "available_filters": {
                    "currency_pairs": currency_pairs,
                    "exchangers": exchangers
                }
            }
        }
        
        print(f"✅ Response: {len(rates)} rates generated")
        return response
        
    except Exception as e:
        print(f"❌ Error in get_historical_rates: {e}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e),
            "data": {
                "rates": [],
                "total_count": 0,
                "filters": {},
                "available_filters": {"currency_pairs": [], "exchangers": []}
            }
        }

@app.get("/api/v1/parsing/results")
async def get_parsing_results(limit: int = 100):
    """Получение результатов парсинга"""
    return {
        "success": True,
        "data": {
            "results": [],
            "pagination": {"total": 0, "limit": limit, "offset": 0, "has_more": False},
            "filters": {}
        }
    }

@app.get("/health")
async def health_check():
    """Проверка здоровья сервера"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "test-historical-rates-api"
    }

if __name__ == "__main__":
    print("🚀 Starting Test Historical Rates API Server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📊 Historical rates endpoint: http://localhost:8000/api/v1/parsing/historical-rates")
    print("🔍 Health check: http://localhost:8000/health")
    print("=" * 60)
    
    try:
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=8000, 
            log_level="info",
            access_log=True
        )
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        import traceback
        traceback.print_exc()
