// Система мониторинга стабильности страницы
export class PageStabilityMonitor {
  private static instance: PageStabilityMonitor;
  private reloadCount = 0;
  private lastReloadTime = 0;
  private errorCount = 0;
  private performanceIssues: string[] = [];

  private constructor() {
    this.initializeMonitoring();
  }

  public static getInstance(): PageStabilityMonitor {
    if (!PageStabilityMonitor.instance) {
      PageStabilityMonitor.instance = new PageStabilityMonitor();
    }
    return PageStabilityMonitor.instance;
  }

  private initializeMonitoring(): void {
    // Мониторинг перезагрузок страницы
    this.detectPageReloads();
    
    // Мониторинг JavaScript ошибок
    this.monitorJavaScriptErrors();
    
    // Мониторинг производительности
    this.monitorPerformance();
    
    // Мониторинг React ошибок
    this.monitorReactErrors();
  }

  private detectPageReloads(): void {
    // Проверка типа навигации
    if (performance.navigation) {
      const navigationType = performance.navigation.type;
      
      if (navigationType === 1) { // TYPE_RELOAD
        this.reloadCount++;
        this.lastReloadTime = Date.now();
        
        console.warn('Page reload detected:', {
          count: this.reloadCount,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        });
        
        // Если слишком много перезагрузок за короткое время
        if (this.reloadCount > 3) {
          this.handleExcessiveReloads();
        }
      }
    }

    // Современный API для мониторинга навигации
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            
            if (navEntry.type === 'reload') {
              console.warn('Navigation reload detected:', {
                type: navEntry.type,
                duration: navEntry.duration,
                timestamp: new Date().toISOString()
              });
            }
          }
        }
      });
      
      observer.observe({ entryTypes: ['navigation'] });
    }
  }

  private monitorJavaScriptErrors(): void {
    // Глобальные JavaScript ошибки
    window.addEventListener('error', (event) => {
      this.errorCount++;
      
      const errorInfo = {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error?.stack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      };
      
      console.error('JavaScript Error detected:', errorInfo);
      
      // Отправка в систему мониторинга
      this.reportError('javascript_error', errorInfo);
      
      // Предотвращение перезагрузки страницы
      if (this.shouldPreventReload(event.error)) {
        event.preventDefault();
        this.showErrorNotification('Обнаружена ошибка, но страница остается стабильной');
      }
    });

    // Необработанные Promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.errorCount++;
      
      const errorInfo = {
        reason: event.reason,
        promise: event.promise,
        timestamp: new Date().toISOString(),
        url: window.location.href
      };
      
      console.error('Unhandled Promise Rejection:', errorInfo);
      
      // Отправка в систему мониторинга
      this.reportError('promise_rejection', errorInfo);
      
      // Предотвращение перезагрузки
      event.preventDefault();
    });
  }

  private monitorPerformance(): void {
    // Мониторинг длительных задач
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) { // Задачи длиннее 50ms
            console.warn('Long task detected:', {
              duration: entry.duration,
              startTime: entry.startTime,
              name: entry.name
            });
            
            this.performanceIssues.push(`Long task: ${entry.duration}ms`);
          }
        }
      });
      
      try {
        observer.observe({ entryTypes: ['longtask'] });
      } catch (e) {
        console.warn('Long task monitoring not supported');
      }
    }

    // Мониторинг использования памяти
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        const memoryUsage = {
          used: Math.round(memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
        };
        
        // Предупреждение при высоком использовании памяти
        if (memoryUsage.used > memoryUsage.limit * 0.8) {
          console.warn('High memory usage detected:', memoryUsage);
          this.performanceIssues.push(`High memory: ${memoryUsage.used}MB`);
        }
      }, 30000); // Каждые 30 секунд
    }
  }

  private monitorReactErrors(): void {
    // Мониторинг React ошибок через Error Boundary
    const originalConsoleError = console.error;
    
    console.error = (...args) => {
      // Проверка на React ошибки
      const errorMessage = args.join(' ');
      
      if (errorMessage.includes('Warning:') || errorMessage.includes('React')) {
        console.warn('React issue detected:', errorMessage);
        
        // Специальная обработка для критических React ошибок
        if (errorMessage.includes('Maximum update depth exceeded')) {
          this.handleInfiniteRenderLoop();
        }
      }
      
      // Вызов оригинального console.error
      originalConsoleError.apply(console, args);
    };
  }

  private shouldPreventReload(error: Error): boolean {
    // Определяем, стоит ли предотвращать перезагрузку
    const nonCriticalErrors = [
      'ResizeObserver loop limit exceeded',
      'Non-Error promise rejection captured',
      'ChunkLoadError'
    ];
    
    return nonCriticalErrors.some(pattern => 
      error?.message?.includes(pattern) || error?.name?.includes(pattern)
    );
  }

  private handleExcessiveReloads(): void {
    console.error('Excessive page reloads detected!', {
      count: this.reloadCount,
      timespan: Date.now() - this.lastReloadTime
    });
    
    // Показать пользователю предупреждение
    this.showErrorNotification(
      'Обнаружены частые перезагрузки страницы. Проверьте консоль браузера для диагностики.'
    );
    
    // Отправить критическое уведомление разработчикам
    this.reportCriticalIssue('excessive_reloads', {
      reloadCount: this.reloadCount,
      userAgent: navigator.userAgent,
      url: window.location.href
    });
  }

  private handleInfiniteRenderLoop(): void {
    console.error('Infinite render loop detected!');
    
    // Попытка стабилизации через принудительную очистку состояния
    try {
      // Очистка localStorage от потенциально проблемных данных
      const keysToCheck = ['auth', 'exchangerManagement', 'adminSession'];
      keysToCheck.forEach(key => {
        const data = localStorage.getItem(key);
        if (data) {
          try {
            JSON.parse(data);
          } catch {
            console.warn(`Removing corrupted localStorage key: ${key}`);
            localStorage.removeItem(key);
          }
        }
      });
      
      this.showErrorNotification(
        'Обнаружен бесконечный цикл рендеринга. Выполнена автоматическая стабилизация.'
      );
    } catch (stabilizationError) {
      console.error('Failed to stabilize infinite render loop:', stabilizationError);
    }
  }

  private reportError(type: string, details: any): void {
    // Отправка ошибки в систему мониторинга
    const errorReport = {
      type,
      details,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      reloadCount: this.reloadCount,
      errorCount: this.errorCount,
      performanceIssues: this.performanceIssues
    };
    
    // В продакшне отправлять в Sentry, LogRocket и т.д.
    if (window.Sentry) {
      window.Sentry.captureException(new Error(`${type}: ${JSON.stringify(details)}`));
    }
    
    // Локальное логирование для разработки
    console.group('🚨 Error Report');
    console.error('Type:', type);
    console.error('Details:', details);
    console.error('Context:', {
      reloadCount: this.reloadCount,
      errorCount: this.errorCount,
      performanceIssues: this.performanceIssues
    });
    console.groupEnd();
  }

  private reportCriticalIssue(type: string, details: any): void {
    // Критические проблемы требуют немедленного внимания
    console.error('🚨 CRITICAL ISSUE:', type, details);
    
    // В продакшне отправить уведомление разработчикам
    // Например, через Slack webhook, email alert и т.д.
  }

  private showErrorNotification(message: string): void {
    // Показать пользователю уведомление о проблеме
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #fee2e2;
      border: 1px solid #fecaca;
      color: #991b1b;
      padding: 12px 16px;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      z-index: 10000;
      max-width: 400px;
      font-family: system-ui, -apple-system, sans-serif;
      font-size: 14px;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Автоматическое удаление через 10 секунд
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 10000);
  }

  // Публичные методы для использования в компонентах
  public getStabilityReport(): {
    reloadCount: number;
    errorCount: number;
    performanceIssues: string[];
    isStable: boolean;
  } {
    return {
      reloadCount: this.reloadCount,
      errorCount: this.errorCount,
      performanceIssues: [...this.performanceIssues],
      isStable: this.reloadCount < 3 && this.errorCount < 5
    };
  }

  public resetCounters(): void {
    this.reloadCount = 0;
    this.errorCount = 0;
    this.performanceIssues = [];
    console.log('Page stability counters reset');
  }
}

// Инициализация мониторинга
export const pageStabilityMonitor = PageStabilityMonitor.getInstance();

// Утилитарные функции
export const initializePageStabilityMonitoring = () => {
  pageStabilityMonitor; // Просто создаем экземпляр
  console.log('Page stability monitoring initialized');
};

export const getPageStabilityReport = () => {
  return pageStabilityMonitor.getStabilityReport();
};

export const resetStabilityCounters = () => {
  pageStabilityMonitor.resetCounters();
};