#!/usr/bin/env python3
"""
Минимальный сервер для исторических курсов
"""
import json
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
from datetime import datetime
import random

PORT = 8000

class Handler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.end_headers()
        
        parsed = urlparse(self.path)
        
        if parsed.path == '/health':
            response = {"status": "healthy", "timestamp": datetime.now().isoformat()}
        elif parsed.path == '/api/v1/parsing/historical-rates':
            # Генерируем тестовые данные
            rates = []
            for i in range(10):
                rates.append({
                    "id": i + 1,
                    "exchanger_id": f"test_exchanger_{i+1}",
                    "exchanger_name": f"Test Exchanger {i+1}",
                    "currency_pair": "RUB/THB",
                    "buy_rate": round(2.45 + random.uniform(-0.1, 0.1), 4),
                    "sell_rate": round(2.55 + random.uniform(-0.1, 0.1), 4),
                    "parsed_at": datetime.now().isoformat()
                })
            
            response = {
                "success": True,
                "data": {
                    "rates": rates,
                    "total_count": len(rates),
                    "available_filters": {
                        "currency_pairs": [
                            {"currency_pair": "RUB/THB", "records_count": 100},
                            {"currency_pair": "THB/RUB", "records_count": 80}
                        ],
                        "exchangers": [
                            {"exchanger_id": "test_exchanger_1", "exchanger_name": "Test Exchanger 1", "records_count": 50}
                        ]
                    }
                }
            }
        else:
            response = {"message": "Minimal API Server", "timestamp": datetime.now().isoformat()}
        
        self.wfile.write(json.dumps(response).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.end_headers()

if __name__ == "__main__":
    print(f"Starting server on port {PORT}...")
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"Server running at http://localhost:{PORT}")
        httpd.serve_forever()
