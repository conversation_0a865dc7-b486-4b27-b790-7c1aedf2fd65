#!/usr/bin/env python3
"""
Минимальный сервер для исторических курсов
"""
import json
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
from datetime import datetime, timedelta
import random

PORT = 8000

class Handler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.end_headers()
        
        parsed = urlparse(self.path)
        
        if parsed.path == '/health':
            response = {"status": "healthy", "timestamp": datetime.now().isoformat()}
        elif parsed.path == '/api/v1/parsing/historical-rates':
            # Генерируем тестовые данные для исторических курсов
            rates = []
            for i in range(10):
                rates.append({
                    "id": i + 1,
                    "exchanger_id": f"test_exchanger_{i+1}",
                    "exchanger_name": f"Test Exchanger {i+1}",
                    "currency_pair": "RUB/THB",
                    "buy_rate": round(2.45 + random.uniform(-0.1, 0.1), 4),
                    "sell_rate": round(2.55 + random.uniform(-0.1, 0.1), 4),
                    "parsed_at": datetime.now().isoformat()
                })

            response = {
                "success": True,
                "data": {
                    "rates": rates,
                    "total_count": len(rates),
                    "available_filters": {
                        "currency_pairs": [
                            {"currency_pair": "RUB/THB", "records_count": 100},
                            {"currency_pair": "THB/RUB", "records_count": 80}
                        ],
                        "exchangers": [
                            {"exchanger_id": "test_exchanger_1", "exchanger_name": "Test Exchanger 1", "records_count": 50}
                        ]
                    }
                }
            }

        elif parsed.path == '/api/v1/parsing/results':
            # Генерируем тестовые данные для результатов парсинга (админка)
            parsing_results = []
            statuses = ['success', 'failed', 'error', 'partial']
            exchangers = ['SuperRich Thailand', 'Vasu Exchange', 'Happy Rich Exchange', 'Grand SuperRich', 'Siam Exchange']

            for i in range(15):
                status = random.choice(statuses)
                exchanger = random.choice(exchangers)
                timestamp = datetime.now() - timedelta(hours=random.randint(1, 24))

                parsing_results.append({
                    "id": i + 1,
                    "exchanger_id": exchanger.lower().replace(' ', '_'),
                    "exchanger_name": exchanger,
                    "parsing_timestamp": timestamp.isoformat(),
                    "status": status,
                    "execution_time_ms": random.randint(500, 5000),
                    "source_url": f"https://{exchanger.lower().replace(' ', '')}.com/rates",
                    "parsed_pairs_count": random.randint(1, 5) if status == 'success' else 0,
                    "total_pairs_expected": 5,
                    "error_message": "Connection timeout" if status in ['failed', 'error'] else None,
                    "error_details": {"timeout": True} if status in ['failed', 'error'] else None,
                    "raw_data": {"rates": {"RUB/THB": {"buy": 2.45, "sell": 2.55}}} if status == 'success' else None,
                    "metadata": {"parser_version": "1.0", "retry_count": 0},
                    "created_at": timestamp.isoformat()
                })

            response = {
                "success": True,
                "data": {
                    "results": parsing_results,
                    "pagination": {
                        "total": len(parsing_results),
                        "limit": 100,
                        "offset": 0,
                        "has_more": False
                    },
                    "filters": {
                        "exchanger_id": None,
                        "status": None,
                        "hours_back": 24
                    }
                }
            }

        elif parsed.path == '/api/v1/parsing/historical-rates/trends':
            # Генерируем тестовые данные для трендов курсов (админка)
            trends = []

            for i in range(30):  # 30 дней данных
                date = datetime.now() - timedelta(days=i)
                base_buy_rate = 2.45
                base_sell_rate = 2.55

                # Добавляем небольшую волатильность
                volatility = random.uniform(-0.05, 0.05)

                trends.append({
                    "time_period": date.isoformat(),
                    "currency_pair": "RUB/THB",
                    "avg_buy_rate": round(base_buy_rate + volatility, 4),
                    "avg_sell_rate": round(base_sell_rate + volatility, 4),
                    "min_buy_rate": round(base_buy_rate + volatility - 0.02, 4),
                    "max_buy_rate": round(base_buy_rate + volatility + 0.02, 4),
                    "min_sell_rate": round(base_sell_rate + volatility - 0.02, 4),
                    "max_sell_rate": round(base_sell_rate + volatility + 0.02, 4),
                    "avg_spread_percentage": round(((base_sell_rate - base_buy_rate) / base_buy_rate) * 100, 2),
                    "data_points": random.randint(10, 50),
                    "unique_exchangers": random.randint(3, 5)
                })

            response = {
                "success": True,
                "data": {
                    "trends": trends,
                    "currency_pair": "RUB/THB",
                    "interval": "daily",
                    "period": {
                        "days_back": 30,
                        "start_date": (datetime.now() - timedelta(days=30)).date().isoformat(),
                        "end_date": datetime.now().date().isoformat()
                    }
                }
            }
        elif parsed.path == '/api/v1/parsing/results/summary':
            # Генерируем сводную статистику для админки
            response = {
                "success": True,
                "data": {
                    "daily_summary": [
                        {
                            "date": (datetime.now() - timedelta(days=i)).date().isoformat(),
                            "total_operations": random.randint(20, 100),
                            "successful_operations": random.randint(15, 90),
                            "failed_operations": random.randint(0, 10),
                            "error_operations": random.randint(0, 5),
                            "avg_execution_time_ms": round(random.uniform(1000, 3000), 2),
                            "unique_exchangers": random.randint(3, 5),
                            "total_rates_parsed": random.randint(50, 300)
                        } for i in range(7)
                    ],
                    "overall_statistics": {
                        "total_operations": 500,
                        "successful_operations": 425,
                        "failed_operations": 50,
                        "error_operations": 25,
                        "avg_execution_time_ms": 2150.5,
                        "unique_exchangers": 5,
                        "total_rates_parsed": 1250,
                        "success_rate_percentage": 85.0
                    },
                    "period": {
                        "days_back": 7,
                        "start_date": (datetime.now() - timedelta(days=7)).date().isoformat(),
                        "end_date": datetime.now().date().isoformat()
                    }
                }
            }

        elif parsed.path == '/api/v1/parsing/current-session':
            # Текущая сессия парсинга
            response = {
                "success": True,
                "data": {
                    "session_id": "session_" + datetime.now().strftime("%Y%m%d_%H%M%S"),
                    "status": "active",
                    "started_at": datetime.now().isoformat(),
                    "results": [],
                    "total_exchangers": 5,
                    "completed_exchangers": 0,
                    "progress_percentage": 0
                }
            }

        else:
            response = {"message": "Minimal API Server", "timestamp": datetime.now().isoformat()}
        
        self.wfile.write(json.dumps(response).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.end_headers()

if __name__ == "__main__":
    print(f"Starting server on port {PORT}...")
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"Server running at http://localhost:{PORT}")
        httpd.serve_forever()
