// React hooks для работы с API
import { useState, useEffect, useCallback } from 'react';
import { api, apiUtils } from '../services/api';

// Типы для хуков
interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

interface UseApiOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

// Базовый хук для API запросов
export function useApi<T>(
  apiCall: () => Promise<any>,
  options: UseApiOptions = {}
): UseApiState<T> {
  const { immediate = true, onSuccess, onError } = options;
  
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiCall();
      const result = apiUtils.extractData(response);
      
      setData(result);
      onSuccess?.(result);
    } catch (err) {
      const errorMessage = apiUtils.handleError(err);
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [apiCall, onSuccess, onError]);

  useEffect(() => {
    if (immediate) {
      fetchData();
    }
  }, [fetchData, immediate]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
}

// Хук для получения списка обменников
export function useExchangers(params?: {
  district?: string;
  search?: string;
  page?: number;
  limit?: number;
}) {
  return useApi(
    () => api.exchangers.getAll(params),
    {
      immediate: true,
    }
  );
}

// Хук для получения топ обменников
export function useTopExchangers(limit = 10) {
  return useApi(
    () => api.exchangers.getTopRated(limit),
    {
      immediate: true,
    }
  );
}

// Хук для получения отзывов
export function useReviews(params?: {
  exchangerId?: number;
  page?: number;
  limit?: number;
}) {
  return useApi(
    () => api.reviews.getAll(params),
    {
      immediate: true,
    }
  );
}

// Хук для получения текущих курсов
export function useCurrentRates() {
  return useApi(
    () => api.rates.getCurrent(),
    {
      immediate: true,
    }
  );
}

// Хук для административной статистики
export function useAdminStats() {
  return useApi(
    () => api.admin.getStats(),
    {
      immediate: true,
    }
  );
}

// Хук для мутаций (создание, обновление, удаление)
export function useMutation<TData, TVariables = any>(
  mutationFn: (variables: TVariables) => Promise<any>,
  options: {
    onSuccess?: (data: TData) => void;
    onError?: (error: string) => void;
  } = {}
) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mutate = useCallback(async (variables: TVariables) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await mutationFn(variables);
      const result = apiUtils.extractData(response);
      
      options.onSuccess?.(result);
      return result;
    } catch (err) {
      const errorMessage = apiUtils.handleError(err);
      setError(errorMessage);
      options.onError?.(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [mutationFn, options]);

  return {
    mutate,
    loading,
    error,
  };
}

// Специализированные хуки для мутаций
export function useCreateReview() {
  return useMutation(
    (reviewData: Omit<Review, 'id' | 'date' | 'moderated'>) => 
      api.reviews.create(reviewData)
  );
}

export function useModerateReview() {
  return useMutation(
    ({ id, action }: { id: number; action: 'approve' | 'reject' }) => 
      api.reviews.moderate(id, action)
  );
}

export function useUpdateRates() {
  return useMutation(
    (source: 'google_sheets' | 'manual') => 
      api.rates.updateFromSource(source)
  );
}

// Хук для аутентификации
export function useAuth() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const login = useMutation(
    (credentials: { email: string; password: string }) => 
      api.auth.login(credentials),
    {
      onSuccess: (data: any) => {
        localStorage.setItem('authToken', data.token);
        setUser(data.user);
      },
    }
  );

  const logout = useCallback(async () => {
    try {
      await api.auth.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('authToken');
      setUser(null);
    }
  }, []);

  const register = useMutation(
    (userData: { email: string; password: string; name: string }) => 
      api.auth.register(userData),
    {
      onSuccess: (data: any) => {
        localStorage.setItem('authToken', data.token);
        setUser(data.user);
      },
    }
  );

  return {
    user,
    loading,
    login: login.mutate,
    logout,
    register: register.mutate,
    loginLoading: login.loading,
    loginError: login.error,
    registerLoading: register.loading,
    registerError: register.error,
  };
}