// Intelligent rate processing utilities
export interface RateData {
  currency: string;
  buy: number;
  sell: number;
  change: number;
  converted?: boolean;
  originalRate?: any;
  swapped?: boolean;
  outOfRange?: boolean;
  processedAt?: string;
  source?: string;
  calculated?: boolean;
  sourcePair?: string;
  flagged?: boolean;
  flagReason?: string;
  unavailable?: boolean;
  reason?: string;
}

export interface RateValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Rate validation rules (updated to match backend)
const RATE_VALIDATION_RULES = {
  'THB/RUB': { min: 0.25, max: 0.50, decimals: 3 },
  'RUB/THB': { min: 2.0, max: 4.0, decimals: 2 },
  'USDT/THB': { min: 30.0, max: 40.0, decimals: 2 },
  'USDT/BAHT': { min: 30.0, max: 40.0, decimals: 2 },
  'USD/THB': { min: 30.0, max: 40.0, decimals: 2 },
  'EUR/THB': { min: 35.0, max: 45.0, decimals: 2 },
  'RUB': { min: 30.0, max: 40.0, decimals: 2 },
  'USD': { min: 30.0, max: 40.0, decimals: 2 },
  'USDT': { min: 30.0, max: 40.0, decimals: 2 }
};

// Get mandatory currency pairs from settings
const getMandatoryPairs = (): string[] => {
  try {
    const settingsAPI = (window as any).systemSettings;
    if (settingsAPI) {
      return settingsAPI.getTyped('mandatory_currency_pairs', ['THB/RUB', 'RUB/THB', 'USDT/THB']);
    }
  } catch (error) {
    console.warn('Could not load mandatory pairs from settings, using default:', error);
  }
  return ['THB/RUB', 'RUB/THB', 'USDT/THB'];
};

// Mandatory currency pairs that must be present
const MANDATORY_PAIRS = ['THB/RUB', 'RUB/THB', 'USDT/THB'];

// Allowed currency pairs for display (filter out unwanted pairs)
const ALLOWED_PAIRS = ['THB/RUB', 'RUB/THB', 'USDT/THB', 'THB/USDT', 'USD/THB', 'THB/USD'];

// Check if strict validation is enabled
const isStrictValidationEnabled = (): boolean => {
  try {
    const settingsAPI = (window as any).systemSettings;
    if (settingsAPI) {
      return settingsAPI.getTyped('rate_validation_strict', true);
    }
  } catch (error) {
    console.warn('Could not load validation setting, using default:', error);
  }
  return true; // Default to strict
};

// Threshold for detecting inverse rates
const INVERSE_RATE_THRESHOLD = 1.0;

/**
 * Intelligent rate processing with automatic conversion
 * Handles inverse rates, validation, and standardization
 */
export class RateProcessor {
  private static instance: RateProcessor;

  private constructor() {}

  public static getInstance(): RateProcessor {
    if (!RateProcessor.instance) {
      RateProcessor.instance = new RateProcessor();
    }
    return RateProcessor.instance;
  }

  /**
   * Process rates with intelligent conversion and validation
   */
  public processRates(rates: any[]): RateData[] {
    const processedRates: RateData[] = [];
    
    console.log(`Starting intelligent rate processing for ${rates.length} rates`);
    
    rates.forEach((rate, index) => {
      try {
        const processed = this.processIndividualRate(rate);
        if (processed) {
          processedRates.push(processed);
        }
      } catch (error) {
        console.error(`Error processing rate at index ${index}:`, error);
      }
    });
    
    console.log(`Completed processing: ${processedRates.length} valid rates from ${rates.length} input rates`);
    return processedRates;
  }

  /**
   * Process individual rate with intelligent conversion
   */
  private processIndividualRate(rate: any): RateData | null {
    // Validate input
    if (!rate || !rate.currency) {
      console.warn('Invalid rate object:', rate);
      return null;
    }

    const buyRate = parseFloat(rate.buy);
    const sellRate = parseFloat(rate.sell);

    if (isNaN(buyRate) || isNaN(sellRate)) {
      console.warn(`Invalid numeric values for ${rate.currency}:`, { buy: rate.buy, sell: rate.sell });
      return null;
    }

    let processedRate: RateData = {
      currency: rate.currency,
      buy: buyRate,
      sell: sellRate,
      change: parseFloat(rate.change) || 0,
      processedAt: new Date().toISOString(),
      source: rate.source || 'unknown'
    };

    // Detect and convert inverse rates
    if (this.isInverseRate(rate.currency, buyRate, sellRate)) {
      processedRate = this.convertInverseRate(processedRate);
    }

    // Validate and fix rate spread
    if (processedRate.sell < processedRate.buy) {
      console.warn(`Invalid spread for ${processedRate.currency}, swapping buy/sell`);
      const temp = processedRate.buy;
      processedRate.buy = processedRate.sell;
      processedRate.sell = temp;
      processedRate.swapped = true;
    }

    // Apply rounding based on currency
    processedRate = this.roundRateValues(processedRate);

    // Validate against expected ranges
    const validation = this.validateRateRange(processedRate);
    if (!validation.isValid) {
      console.warn(`Rate validation warnings for ${processedRate.currency}:`, validation.warnings);
      processedRate.outOfRange = true;
    }

    return processedRate;
  }

  /**
   * Detect if a rate is in inverse format
   */
  private isInverseRate(currency: string, buyRate: number, sellRate: number): boolean {
    // Check for explicit inverse currency pairs
    if (currency === 'RUB/THB' || currency === 'USD/THB' || currency === 'EUR/THB') {
      return true;
    }

    // Check for rates below threshold (likely inverse)
    if (currency === 'THB/RUB' && buyRate < INVERSE_RATE_THRESHOLD && sellRate < INVERSE_RATE_THRESHOLD) {
      return true;
    }

    return false;
  }

  /**
   * Convert inverse rate to direct format
   */
  private convertInverseRate(rate: RateData): RateData {
    console.log(`Converting inverse rate for ${rate.currency}:`, { buy: rate.buy, sell: rate.sell });

    const convertedRate: RateData = {
      ...rate,
      currency: this.getDirectCurrencyPair(rate.currency),
      buy: parseFloat((1 / rate.sell).toFixed(6)), // Inverse of sell becomes buy
      sell: parseFloat((1 / rate.buy).toFixed(6)), // Inverse of buy becomes sell
      converted: true,
      originalRate: {
        currency: rate.currency,
        buy: rate.buy,
        sell: rate.sell
      }
    };

    console.log(`Converted to direct rate:`, { 
      currency: convertedRate.currency, 
      buy: convertedRate.buy, 
      sell: convertedRate.sell 
    });

    return convertedRate;
  }

  /**
   * Get direct currency pair format
   */
  private getDirectCurrencyPair(currency: string): string {
    switch (currency) {
      case 'RUB/THB': return 'THB/RUB';
      case 'USD/THB': return 'THB/USD';
      case 'EUR/THB': return 'THB/EUR';
      case 'USDT/THB': return 'THB/USDT';
      default: return currency;
    }
  }

  /**
   * Round rate values to appropriate decimal places
   */
  private roundRateValues(rate: RateData): RateData {
    const rules = RATE_VALIDATION_RULES[rate.currency as keyof typeof RATE_VALIDATION_RULES];
    const decimals = rules?.decimals || 4;

    return {
      ...rate,
      buy: parseFloat(rate.buy.toFixed(decimals)),
      sell: parseFloat(rate.sell.toFixed(decimals))
    };
  }

  /**
   * Validate rate against expected ranges
   */
  private validateRateRange(rate: RateData): RateValidationResult {
    const rules = RATE_VALIDATION_RULES[rate.currency as keyof typeof RATE_VALIDATION_RULES];
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!rules) {
      warnings.push(`No validation rules defined for ${rate.currency}`);
      return { isValid: true, errors, warnings };
    }

    // Check buy rate range
    if (rate.buy < rules.min || rate.buy > rules.max) {
      warnings.push(`Buy rate ${rate.buy} is outside expected range (${rules.min}-${rules.max})`);
    }

    // Check sell rate range
    if (rate.sell < rules.min || rate.sell > rules.max) {
      warnings.push(`Sell rate ${rate.sell} is outside expected range (${rules.min}-${rules.max})`);
    }

    // Check spread (sell should be higher than buy)
    if (rate.sell <= rate.buy) {
      errors.push(`Sell rate (${rate.sell}) must be higher than buy rate (${rate.buy})`);
    }

    // Check reasonable spread (not too wide)
    const spread = ((rate.sell - rate.buy) / rate.buy) * 100;
    if (spread > 10) { // More than 10% spread might be unusual
      warnings.push(`Large spread detected: ${spread.toFixed(2)}%`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Batch validate multiple rates
   */
  public validateRates(rates: RateData[]): {
    valid: RateData[];
    invalid: Array<{ rate: RateData; errors: string[] }>;
    warnings: Array<{ rate: RateData; warnings: string[] }>;
  } {
    const valid: RateData[] = [];
    const invalid: Array<{ rate: RateData; errors: string[] }> = [];
    const warnings: Array<{ rate: RateData; warnings: string[] }> = [];

    rates.forEach(rate => {
      const validation = this.validateRateRange(rate);
      
      if (validation.isValid) {
        valid.push(rate);
      } else {
        invalid.push({ rate, errors: validation.errors });
      }

      if (validation.warnings.length > 0) {
        warnings.push({ rate, warnings: validation.warnings });
      }
    });

    return { valid, invalid, warnings };
  }

  /**
   * Format rate for display
   */
  public formatRate(rate: number, currency: string): string {
    const rules = RATE_VALIDATION_RULES[currency as keyof typeof RATE_VALIDATION_RULES];
    const decimals = rules?.decimals || 4;
    return rate.toFixed(decimals);
  }

  /**
   * Calculate rate spread percentage
   */
  public calculateSpread(buyRate: number, sellRate: number): number {
    if (buyRate <= 0) return 0;
    return ((sellRate - buyRate) / buyRate) * 100;
  }

  /**
   * Get rate trend indicator
   */
  public getTrendIndicator(change: number): { icon: string; color: string; text: string } {
    if (change > 0) {
      return { icon: '↗️', color: 'text-green-600', text: `+${change.toFixed(2)}%` };
    } else if (change < 0) {
      return { icon: '↘️', color: 'text-red-600', text: `${change.toFixed(2)}%` };
    } else {
      return { icon: '➡️', color: 'text-gray-600', text: '0.00%' };
    }
  }

  /**
   * Calculate inverse rates for THB/RUB ↔ RUB/THB
   */
  public calculateInverseRates(thbRubBuy: number, thbRubSell: number): RateData | null {
    try {
      // Enhanced validation with more specific checks
      if (!thbRubBuy || !thbRubSell ||
          thbRubBuy <= 0 || thbRubSell <= 0 ||
          !isFinite(thbRubBuy) || !isFinite(thbRubSell) ||
          isNaN(thbRubBuy) || isNaN(thbRubSell)) {
        console.warn('Invalid THB/RUB rates for inverse calculation:', { thbRubBuy, thbRubSell });
        return null;
      }

      // Additional safety check for very small values that could cause overflow
      if (thbRubBuy < 0.001 || thbRubSell < 0.001) {
        console.warn('THB/RUB rates too small for safe inverse calculation:', { thbRubBuy, thbRubSell });
        return null;
      }

      // For inverse calculation:
      // RUB/THB buy = 1 / THB/RUB sell (customer buys RUB, sells THB)
      // RUB/THB sell = 1 / THB/RUB buy (customer sells RUB, buys THB)

      const rubThbBuy = 1 / thbRubSell;
      const rubThbSell = 1 / thbRubBuy;

      // Validate calculated results
      if (!isFinite(rubThbBuy) || !isFinite(rubThbSell) ||
          isNaN(rubThbBuy) || isNaN(rubThbSell) ||
          rubThbBuy <= 0 || rubThbSell <= 0) {
        console.error('Invalid inverse calculation results:', { rubThbBuy, rubThbSell });
        return null;
      }

      return {
        currency: 'RUB/THB',
        buy: parseFloat(rubThbBuy.toFixed(2)),
        sell: parseFloat(rubThbSell.toFixed(2)),
        change: 0,
        calculated: true,
        sourcePair: 'THB/RUB',
        processedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error calculating inverse rates:', error, { thbRubBuy, thbRubSell });
      return null;
    }
  }

  /**
   * Ensure all mandatory currency pairs are present
   */
  public ensureMandatoryPairs(rates: RateData[]): RateData[] {
    const rateMap = new Map<string, RateData>();

    // Index existing rates
    rates.forEach(rate => {
      rateMap.set(rate.currency, rate);
    });

    // Get mandatory pairs from settings
    const mandatoryPairs = getMandatoryPairs();

    // Check for missing mandatory pairs
    const missingPairs: string[] = [];
    mandatoryPairs.forEach(pair => {
      if (!rateMap.has(pair)) {
        missingPairs.push(pair);
      }
    });

    if (missingPairs.length > 0) {
      console.log('Missing mandatory pairs:', missingPairs);

      // Try to derive missing pairs
      missingPairs.forEach(pair => {
        const derivedRate = this.deriveMissingRate(pair, rateMap);
        if (derivedRate) {
          rateMap.set(pair, derivedRate);
          console.log(`Derived missing rate for ${pair}:`, derivedRate);
        } else {
          // Add unavailable placeholder
          rateMap.set(pair, {
            currency: pair,
            buy: 0,
            sell: 0,
            change: 0,
            unavailable: true,
            reason: 'Could not derive from available rates',
            processedAt: new Date().toISOString()
          });
          console.warn(`Could not derive rate for ${pair}, marked as unavailable`);
        }
      });
    }

    return Array.from(rateMap.values());
  }

  /**
   * Try to derive a missing currency pair from available rates
   */
  private deriveMissingRate(pair: string, rateMap: Map<string, RateData>): RateData | null {
    try {
      // THB/RUB can be derived from RUB/THB
      if (pair === 'THB/RUB' && rateMap.has('RUB/THB')) {
        const rubThb = rateMap.get('RUB/THB')!;

        // Enhanced validation
        if (rubThb.buy > 0 && rubThb.sell > 0 &&
            isFinite(rubThb.buy) && isFinite(rubThb.sell) &&
            !isNaN(rubThb.buy) && !isNaN(rubThb.sell) &&
            rubThb.buy >= 0.001 && rubThb.sell >= 0.001) {

          const thbRubBuy = 1 / rubThb.sell;
          const thbRubSell = 1 / rubThb.buy;

          // Validate calculated results
          if (isFinite(thbRubBuy) && isFinite(thbRubSell) &&
              !isNaN(thbRubBuy) && !isNaN(thbRubSell) &&
              thbRubBuy > 0 && thbRubSell > 0) {

            return {
              currency: 'THB/RUB',
              buy: parseFloat(thbRubBuy.toFixed(3)),
              sell: parseFloat(thbRubSell.toFixed(3)),
              change: 0,
              calculated: true,
              sourcePair: 'RUB/THB',
              processedAt: new Date().toISOString()
            };
          } else {
            console.warn('Invalid derived THB/RUB calculation:', { thbRubBuy, thbRubSell, source: rubThb });
          }
        }
      }

      // RUB/THB can be derived from THB/RUB
      if (pair === 'RUB/THB' && rateMap.has('THB/RUB')) {
        const thbRub = rateMap.get('THB/RUB')!;
        if (thbRub.buy > 0 && thbRub.sell > 0 &&
            isFinite(thbRub.buy) && isFinite(thbRub.sell) &&
            !isNaN(thbRub.buy) && !isNaN(thbRub.sell)) {
          return this.calculateInverseRates(thbRub.buy, thbRub.sell);
        }
      }

      return null;
    } catch (error) {
      console.error(`Error deriving rate for ${pair}:`, error);
      return null;
    }
  }

  /**
   * Filter rates to only include allowed currency pairs
   */
  private filterAllowedPairs(rates: RateData[]): RateData[] {
    return rates.filter(rate => {
      const isAllowed = ALLOWED_PAIRS.includes(rate.currency);
      if (!isAllowed) {
        console.log(`Filtering out currency pair: ${rate.currency}`);
      }
      return isAllowed;
    });
  }

  /**
   * Process rates with automatic inverse calculation and mandatory pair handling
   */
  public processRatesWithEnhancements(rates: any[]): RateData[] {
    // First, apply standard intelligent processing
    let processedRates = this.processRates(rates);

    // Add inverse calculations for THB/RUB rates
    const thbRubRate = processedRates.find(r => r.currency === 'THB/RUB');
    if (thbRubRate && thbRubRate.buy > 0 && thbRubRate.sell > 0) {
      const inverseRate = this.calculateInverseRates(thbRubRate.buy, thbRubRate.sell);
      if (inverseRate) {
        // Check if RUB/THB already exists
        const existingInverse = processedRates.find(r => r.currency === 'RUB/THB');
        if (!existingInverse) {
          processedRates.push(inverseRate);
        }
      }
    }

    // Ensure all mandatory pairs are present
    processedRates = this.ensureMandatoryPairs(processedRates);

    // Filter to only show allowed currency pairs
    processedRates = this.filterAllowedPairs(processedRates);

    return processedRates;
  }
}

// Export singleton instance
export const rateProcessor = RateProcessor.getInstance();

// Convenience functions
export const processIntelligentRates = (rates: any[]): RateData[] => {
  return rateProcessor.processRates(rates);
};

export const validateRateData = (rates: RateData[]) => {
  return rateProcessor.validateRates(rates);
};

export const formatRateDisplay = (rate: number, currency: string): string => {
  return rateProcessor.formatRate(rate, currency);
};

export const calculateRateSpread = (buyRate: number, sellRate: number): number => {
  return rateProcessor.calculateSpread(buyRate, sellRate);
};

export const processRatesWithEnhancements = (rates: any[]): RateData[] => {
  return rateProcessor.processRatesWithEnhancements(rates);
};

export const calculateInverseRates = (thbRubBuy: number, thbRubSell: number): RateData | null => {
  return rateProcessor.calculateInverseRates(thbRubBuy, thbRubSell);
};

export const ensureMandatoryPairs = (rates: RateData[]): RateData[] => {
  return rateProcessor.ensureMandatoryPairs(rates);
};

export const getRateTrend = (change: number) => {
  return rateProcessor.getTrendIndicator(change);
};