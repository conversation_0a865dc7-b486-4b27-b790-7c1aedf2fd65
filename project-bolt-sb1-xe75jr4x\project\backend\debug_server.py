#!/usr/bin/env python3
"""
Отладочный сервер для админки - максимально простой
"""

import json
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
from datetime import datetime, timedelta
import random
import sys
import traceback

PORT = 8000

class DebugHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        try:
            print(f"🔄 [{datetime.now().strftime('%H:%M:%S')}] GET {self.path}")
            sys.stdout.flush()
            
            # CORS заголовки
            self.send_response(200)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
            self.end_headers()
            
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            query_params = parse_qs(parsed_url.query)
            
            print(f"📍 Path: {path}")
            print(f"📋 Query: {query_params}")
            sys.stdout.flush()
            
            if path == '/health':
                response = {
                    "status": "ok",
                    "timestamp": datetime.now().isoformat(),
                    "server": "debug_server.py",
                    "port": PORT
                }
                
            elif path == '/api/v1/parsing/results':
                # Генерируем тестовые данные для результатов парсинга
                results = []
                for i in range(20):
                    results.append({
                        "id": i + 1,
                        "exchanger_name": f"Test Exchanger {i + 1}",
                        "status": "success" if i % 3 != 0 else ("failed" if i % 3 == 1 else "error"),
                        "parsing_timestamp": (datetime.now() - timedelta(hours=i)).isoformat(),
                        "execution_time_ms": random.randint(500, 3000),
                        "parsed_pairs_count": random.randint(1, 8),
                        "error_message": None if i % 3 == 0 else f"Test error message {i}"
                    })
                
                response = {
                    "success": True,
                    "data": {
                        "results": results,
                        "pagination": {
                            "total": len(results),
                            "limit": int(query_params.get('limit', ['50'])[0]),
                            "offset": int(query_params.get('offset', ['0'])[0]),
                            "has_more": False
                        }
                    }
                }
                
            elif path == '/api/v1/parsing/historical-rates/trends':
                # Генерируем тестовые данные для трендов
                trends = []
                for i in range(30):
                    trends.append({
                        "time_period": (datetime.now() - timedelta(days=i)).date().isoformat(),
                        "currency_pair": "RUB/THB",
                        "avg_buy_rate": round(2.45 + random.uniform(-0.1, 0.1), 4),
                        "avg_sell_rate": round(2.55 + random.uniform(-0.1, 0.1), 4),
                        "data_points": random.randint(5, 25)
                    })
                
                response = {
                    "success": True,
                    "data": {
                        "trends": trends,
                        "currency_pair": "RUB/THB",
                        "interval": "daily"
                    }
                }
                
            else:
                response = {
                    "error": "Not found",
                    "path": path,
                    "available_endpoints": [
                        "/health",
                        "/api/v1/parsing/results",
                        "/api/v1/parsing/historical-rates/trends"
                    ]
                }
            
            # Отправляем ответ
            json_data = json.dumps(response, ensure_ascii=False, indent=2)
            self.wfile.write(json_data.encode('utf-8'))
            
            print(f"✅ [{datetime.now().strftime('%H:%M:%S')}] Response sent: {len(json_data)} bytes")
            sys.stdout.flush()
            
        except Exception as e:
            error_msg = f"❌ [{datetime.now().strftime('%H:%M:%S')}] ERROR: {e}"
            print(error_msg)
            print(traceback.format_exc())
            sys.stdout.flush()
            
            try:
                self.send_error(500, str(e))
            except:
                pass
    
    def do_OPTIONS(self):
        try:
            print(f"🔄 [{datetime.now().strftime('%H:%M:%S')}] OPTIONS {self.path}")
            sys.stdout.flush()
            
            self.send_response(200)
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
            self.end_headers()
            
            print(f"✅ [{datetime.now().strftime('%H:%M:%S')}] OPTIONS response sent")
            sys.stdout.flush()
            
        except Exception as e:
            print(f"❌ OPTIONS error: {e}")
            sys.stdout.flush()
    
    def log_message(self, format, *args):
        # Отключаем стандартное логирование
        pass

if __name__ == "__main__":
    try:
        print("🚀 Запуск отладочного сервера...")
        print(f"📍 Порт: {PORT}")
        print(f"🔗 Health: http://localhost:{PORT}/health")
        print(f"📊 Results: http://localhost:{PORT}/api/v1/parsing/results")
        print(f"📈 Trends: http://localhost:{PORT}/api/v1/parsing/historical-rates/trends")
        print("=" * 60)
        sys.stdout.flush()
        
        with socketserver.TCPServer(("", PORT), DebugHandler) as httpd:
            print(f"✅ Отладочный сервер запущен на порту {PORT}")
            print("🔍 Все запросы будут логироваться в консоль")
            print("🛑 Нажмите Ctrl+C для остановки")
            print("=" * 60)
            sys.stdout.flush()
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Сервер остановлен пользователем")
    except Exception as e:
        print(f"❌ Критическая ошибка: {e}")
        print(traceback.format_exc())
        sys.exit(1)
