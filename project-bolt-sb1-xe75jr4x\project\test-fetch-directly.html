<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Прямой тест fetch</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: white; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; max-height: 300px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Прямой тест fetch</h1>
        
        <div class="card info">
            <h2>📋 Информация</h2>
            <p>Эта страница делает прямые fetch запросы к API, как это делают компоненты админки.</p>
        </div>

        <div class="card">
            <h2>🧪 Тесты</h2>
            <div class="grid">
                <button onclick="testHealth()">Тест Health</button>
                <button onclick="testParsingResults()">Тест Parsing Results</button>
                <button onclick="testRateTrends()">Тест Rate Trends</button>
                <button onclick="testAll()">Тест всех endpoints</button>
                <button onclick="clearResults()">Очистить</button>
            </div>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="card ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testHealth() {
            try {
                log('🔄 Тестирование Health endpoint...', 'info');
                
                const url = 'http://localhost:8000/health';
                log(`📡 URL: ${url}`, 'info');
                
                const response = await fetch(url);
                log(`📊 Response Status: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`✅ Health - УСПЕХ<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                
            } catch (error) {
                log(`❌ Health - ОШИБКА<br>🚨 Error: ${error.message}`, 'error');
                console.error('Health error:', error);
            }
        }

        async function testParsingResults() {
            try {
                log('🔄 Тестирование Parsing Results...', 'info');
                
                const url = `${API_BASE_URL}/parsing/results?limit=50&offset=0&hours_back=24`;
                log(`📡 URL: ${url}`, 'info');
                
                const response = await fetch(url);
                log(`📊 Response Status: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    const resultsCount = data.data?.results?.length || 0;
                    log(`✅ Parsing Results - УСПЕХ<br>
                        📊 Записей: ${resultsCount}<br>
                        📋 Первая запись: <pre>${JSON.stringify(data.data.results[0], null, 2)}</pre>`, 'success');
                } else {
                    throw new Error('API вернул success: false');
                }
                
            } catch (error) {
                log(`❌ Parsing Results - ОШИБКА<br>🚨 Error: ${error.message}`, 'error');
                console.error('Parsing results error:', error);
            }
        }

        async function testRateTrends() {
            try {
                log('🔄 Тестирование Rate Trends...', 'info');
                
                const url = `${API_BASE_URL}/parsing/historical-rates/trends?currency_pair=RUB/THB&days_back=30&interval=daily`;
                log(`📡 URL: ${url}`, 'info');
                
                const response = await fetch(url);
                log(`📊 Response Status: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    const trendsCount = data.data?.trends?.length || 0;
                    log(`✅ Rate Trends - УСПЕХ<br>
                        📊 Записей: ${trendsCount}<br>
                        📋 Первая запись: <pre>${JSON.stringify(data.data.trends[0], null, 2)}</pre>`, 'success');
                } else {
                    throw new Error('API вернул success: false');
                }
                
            } catch (error) {
                log(`❌ Rate Trends - ОШИБКА<br>🚨 Error: ${error.message}`, 'error');
                console.error('Rate trends error:', error);
            }
        }

        async function testAll() {
            clearResults();
            log('🚀 Запуск полного теста...', 'info');
            
            await testHealth();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testParsingResults();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testRateTrends();
            
            log('🎯 Все тесты завершены', 'info');
        }

        // Автоматический запуск при загрузке
        window.addEventListener('load', () => {
            log('🔧 Страница прямого тестирования fetch загружена', 'info');
            
            // Автоматически запускаем тест через 1 секунду
            setTimeout(testAll, 1000);
        });

        // Обработка ошибок
        window.addEventListener('unhandledrejection', event => {
            log(`🚨 Необработанная ошибка Promise: ${event.reason}`, 'error');
            console.error('Unhandled promise rejection:', event.reason);
        });

        window.addEventListener('error', event => {
            log(`🚨 JavaScript ошибка: ${event.message}`, 'error');
            console.error('JavaScript error:', event.error);
        });
    </script>
</body>
</html>
