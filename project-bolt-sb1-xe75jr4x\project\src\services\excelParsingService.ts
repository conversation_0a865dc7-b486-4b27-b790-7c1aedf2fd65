// Сервис для парсинга Excel файлов с курсами валют
import * as XLSX from 'xlsx';

export interface ExcelExchangeRate {
  site: string;
  method: string;
  currencyFrom: string;
  currencyTo: string;
  buyRate?: number;
  sellRate?: number;
  minAmount?: number;
  maxAmount?: number;
  reserve?: string;
  contact?: string;
  timestamp: string;
}

export interface ParsedExchangeData {
  exchangerName: string;
  rates: Array<{
    currency: string;
    buy: number;
    sell: number;
    change: number;
  }>;
  lastUpdated: string;
  district: string;
  address: string;
  phone: string;
  hours: string;
}

class ExcelParsingService {
  private static instance: ExcelParsingService;

  private constructor() {}

  public static getInstance(): ExcelParsingService {
    if (!ExcelParsingService.instance) {
      ExcelParsingService.instance = new ExcelParsingService();
    }
    return ExcelParsingService.instance;
  }

  // Парсинг Excel/CSV файла
  public async parseExcelFile(file: File): Promise<{
    success: boolean;
    data?: ParsedExchangeData[];
    error?: string;
  }> {
    console.log('Starting file parsing:', file.name, 'Type:', file.type, 'Size:', file.size);

    try {
      let rawData: any[][];

      if (file.name.toLowerCase().endsWith('.csv')) {
        // Обработка CSV файла
        const text = await file.text();
        console.log('CSV file read successfully, length:', text.length);

        const lines = text.split('\n').filter(line => line.trim());
        rawData = lines.map(line => {
          // Обработка CSV с учетом запятых в кавычках
          const result = [];
          let current = '';
          let inQuotes = false;

          for (let i = 0; i < line.length; i++) {
            const char = line[i];
            if (char === '"') {
              inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
              result.push(current.trim());
              current = '';
            } else {
              current += char;
            }
          }
          result.push(current.trim());
          return result;
        });

        console.log('CSV data parsed, rows:', rawData.length);
      } else {
        // Обработка Excel файла
        const arrayBuffer = await file.arrayBuffer();
        console.log('Excel file read successfully, size:', arrayBuffer.byteLength);

        const workbook = XLSX.read(arrayBuffer, { type: 'array' });
        console.log('Workbook parsed, sheets:', workbook.SheetNames);

        // Получаем первый лист
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Конвертируем в JSON
        rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];
        console.log('Excel sheet data extracted, rows:', rawData.length);
      }
      
      if (rawData.length < 2) {
        throw new Error('Excel файл должен содержать заголовки и данные');
      }

      // Парсим заголовки
      const headers = rawData[0] as string[];
      const dataRows = rawData.slice(1);

      // Определяем индексы колонок
      const columnMap = this.mapColumns(headers);
      
      // Парсим данные
      const exchangeRates = this.parseExcelData(dataRows, columnMap);
      
      // Группируем по обменникам
      const groupedData = this.groupByExchanger(exchangeRates);
      
      console.log(`Excel parsing completed: ${groupedData.length} exchangers extracted`);
      
      return {
        success: true,
        data: groupedData
      };
    } catch (error) {
      console.error('Error parsing Excel file:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ошибка парсинга Excel файла'
      };
    }
  }

  // Определение колонок Excel файла
  private mapColumns(headers: string[]): Record<string, number> {
    const columnMap: Record<string, number> = {};
    
    headers.forEach((header, index) => {
      const normalizedHeader = header.toLowerCase().trim();
      
      if (normalizedHeader.includes('сайт') || normalizedHeader === 'a') {
        columnMap.site = index;
      } else if (normalizedHeader.includes('метод') || normalizedHeader === 'b') {
        columnMap.method = index;
      } else if (normalizedHeader.includes('валюта от') || normalizedHeader === 'c') {
        columnMap.currencyFrom = index;
      } else if (normalizedHeader.includes('валюта к') || normalizedHeader === 'd') {
        columnMap.currencyTo = index;
      } else if (normalizedHeader.includes('курс покупки') || normalizedHeader === 'e') {
        columnMap.buyRate = index;
      } else if (normalizedHeader.includes('курс продажи') || normalizedHeader === 'f') {
        columnMap.sellRate = index;
      } else if (normalizedHeader.includes('мин') || normalizedHeader === 'g') {
        columnMap.minAmount = index;
      } else if (normalizedHeader.includes('макс') || normalizedHeader === 'h') {
        columnMap.maxAmount = index;
      } else if (normalizedHeader.includes('резерв') || normalizedHeader === 'i') {
        columnMap.reserve = index;
      } else if (normalizedHeader.includes('контакт') || normalizedHeader === 'j') {
        columnMap.contact = index;
      } else if (normalizedHeader.includes('время') || normalizedHeader === 'k') {
        columnMap.timestamp = index;
      }
    });

    return columnMap;
  }

  // Парсинг данных из Excel
  private parseExcelData(rows: any[][], columnMap: Record<string, number>): ExcelExchangeRate[] {
    const rates: ExcelExchangeRate[] = [];

    rows.forEach((row, index) => {
      console.log(`Processing row ${index + 2}:`, row);
      
      try {
        if (!row || row.length === 0) return;

        const site = row[columnMap.site]?.toString().trim();
        if (!site) return;

        const rate: ExcelExchangeRate = {
          site,
          method: row[columnMap.method]?.toString() || 'excel_import',
          currencyFrom: row[columnMap.currencyFrom]?.toString().toUpperCase() || '',
          currencyTo: row[columnMap.currencyTo]?.toString().toUpperCase() || '',
          buyRate: this.parseNumber(row[columnMap.buyRate]),
          sellRate: this.parseNumber(row[columnMap.sellRate]),
          minAmount: this.parseNumber(row[columnMap.minAmount]),
          maxAmount: this.parseNumber(row[columnMap.maxAmount]),
          reserve: row[columnMap.reserve]?.toString() || '',
          contact: row[columnMap.contact]?.toString() || '',
          timestamp: row[columnMap.timestamp]?.toString() || new Date().toISOString(),
        };

        console.log(`Parsed rate:`, rate);
        
        // Validate rate data
        if (rate.site && (rate.buyRate || rate.sellRate)) {
          rates.push(rate);
          console.log(`Added valid rate for ${rate.site}`);
        } else {
          console.warn(`Invalid rate data at row ${index + 2}:`, rate);
        }
      } catch (error) {
        console.warn(`Ошибка парсинга строки ${index + 2}:`, error);
      }
    });

    return rates;
  }

  // Группировка по обменникам
  private groupByExchanger(rates: ExcelExchangeRate[]): ParsedExchangeData[] {
    const grouped = new Map<string, ExcelExchangeRate[]>();

    // Группируем по названию сайта
    rates.forEach(rate => {
      const key = rate.site;
      if (!grouped.has(key)) {
        grouped.set(key, []);
      }
      grouped.get(key)!.push(rate);
    });

    // Конвертируем в формат приложения
    const result: ParsedExchangeData[] = [];

    grouped.forEach((siteRates, siteName) => {
      const exchangerData: ParsedExchangeData = {
        exchangerName: siteName,
        rates: this.convertToAppRates(siteRates),
        lastUpdated: new Date().toISOString(),
        district: this.guessDistrict(siteName),
        address: this.guessAddress(siteName),
        phone: this.extractPhone(siteRates),
        hours: '9:00 - 20:00', // Значение по умолчанию
      };

      result.push(exchangerData);
    });

    return result;
  }

  // Конвертация в формат курсов приложения
  private convertToAppRates(rates: ExcelExchangeRate[]): Array<{
    currency: string;
    buy: number;
    sell: number;
    change: number;
  }> {
    const appRates: Array<{ currency: string; buy: number; sell: number; change: number }> = [];
    const currencyPairs = new Map<string, { buy?: number; sell?: number }>();

    // Группируем курсы по валютным парам
    rates.forEach(rate => {
      const pair = this.getCurrencyPair(rate.currencyFrom, rate.currencyTo);
      if (!pair) return;

      if (!currencyPairs.has(pair)) {
        currencyPairs.set(pair, {});
      }

      const pairData = currencyPairs.get(pair)!;
      
      if (rate.buyRate) {
        pairData.buy = rate.buyRate;
      }
      if (rate.sellRate) {
        pairData.sell = rate.sellRate;
      }
    });

    // Конвертируем в формат приложения
    currencyPairs.forEach((data, pair) => {
      if (data.buy && data.sell) {
        // Validate and process rates
        const buyRate = parseFloat(data.buy.toString());
        const sellRate = parseFloat(data.sell.toString());
        
        if (isNaN(buyRate) || isNaN(sellRate)) {
          console.warn(`Invalid rate data for ${pair}: buy=${data.buy}, sell=${data.sell}`);
          return;
        }
        
        appRates.push({
          currency: pair,
          buy: buyRate,
          sell: sellRate,
          change: 0, // Set to 0 for newly imported rates
          source: 'excel_import',
          timestamp: new Date().toISOString()
        });
      }
    });

    return appRates;
  }

  // Определение валютной пары
  private getCurrencyPair(from: string, to: string): string | null {
    const pair = `${from}/${to}`;
    
    // Поддерживаемые пары
    const supportedPairs = [
      'RUB/THB', 'THB/RUB',
      'USD/THB', 'THB/USD',
      'EUR/THB', 'THB/EUR',
      'USDT/THB', 'THB/USDT',
      'USDT/BAHT',
      'RUB', 'USD', 'EUR', 'USDT' // Single currencies
    ];

    if (supportedPairs.includes(pair)) {
      // Нормализуем некоторые пары
      if (pair === 'RUB/THB') return 'THB/RUB';
      if (pair === 'USDT/THB') return 'USDT/BAHT';
      if (pair === 'THB/USD') return 'USD/THB';
      if (pair === 'THB/EUR') return 'EUR/THB';
      return pair;
    }
    
    // Handle single currencies (assume THB as base)
    if (supportedPairs.includes(from)) {
      return `${from}/THB`;
    }
    if (supportedPairs.includes(to)) {
      return `THB/${to}`;
    }

    return null;
  }

  // Вспомогательные методы
  private parseNumber(value: any): number | undefined {
    if (value === null || value === undefined || value === '') return undefined;
    const num = parseFloat(value.toString().replace(',', '.'));
    return isNaN(num) ? undefined : num;
  }

  private guessDistrict(siteName: string): string {
    const name = siteName.toLowerCase();
    if (name.includes('patong')) return 'patong';
    if (name.includes('karon')) return 'karon';
    if (name.includes('kata')) return 'kata';
    if (name.includes('phuket')) return 'phuket-town';
    return 'patong'; // По умолчанию
  }

  private guessAddress(siteName: string): string {
    const districts = {
      'patong': 'Патонг Бич Роуд',
      'karon': 'Карон Бич Роуд',
      'kata': 'Ката Бич Роуд',
      'phuket-town': 'Пхукет Таун, Центральная'
    };
    
    const district = this.guessDistrict(siteName);
    return `${districts[district as keyof typeof districts] || 'Пхукет'}, ${Math.floor(Math.random() * 500) + 1}`;
  }

  private extractPhone(rates: ExcelExchangeRate[]): string {
    for (const rate of rates) {
      if (rate.contact && rate.contact.includes('+')) {
        return rate.contact;
      }
    }
    return `+66 89 ${Math.floor(Math.random() * 900) + 100} ${Math.floor(Math.random() * 9000) + 1000}`;
  }

  // Валидация Excel файла
  public validateExcelFile(file: File): { valid: boolean; error?: string } {
    // Проверка типа файла (добавляем поддержку CSV)
    const validExtensions = ['.xlsx', '.xls', '.csv'];
    const hasValidExtension = validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));

    if (!hasValidExtension) {
      return { valid: false, error: 'Файл должен быть в формате Excel (.xlsx, .xls) или CSV (.csv)' };
    }

    // Проверка размера файла (максимум 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return { valid: false, error: 'Размер файла не должен превышать 10MB' };
    }

    // Проверка на пустой файл
    if (file.size === 0) {
      return { valid: false, error: 'Файл пуст' };
    }

    return { valid: true };
  }

  // Экспорт данных в Excel
  public exportToExcel(exchangers: any[], filename: string = 'exchange_rates_export.xlsx'): void {
    try {
      const exportData: any[] = [];

      exchangers.forEach(exchanger => {
        exchanger.rates?.forEach((rate: any) => {
          exportData.push({
            'Сайт': exchanger.name,
            'Метод поиска': 'manual_entry',
            'Валюта от': rate.currency.split('/')[0] || rate.currency,
            'Валюта к': rate.currency.split('/')[1] || 'THB',
            'Курс покупки': rate.buy,
            'Курс продажи': rate.sell,
            'Мин. сумма': '',
            'Макс. сумма': '',
            'Резерв': '',
            'Контакт': exchanger.phone,
            'Время парсинга': new Date().toISOString(),
          });
        });
      });

      const worksheet = XLSX.utils.json_to_sheet(exportData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Курсы валют');

      // Автоматическая ширина колонок
      const colWidths = Object.keys(exportData[0] || {}).map(key => ({
        wch: Math.max(key.length, 15)
      }));
      worksheet['!cols'] = colWidths;

      XLSX.writeFile(workbook, filename);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      throw new Error('Ошибка экспорта в Excel файл');
    }
  }
}

export const excelParsingService = ExcelParsingService.getInstance();