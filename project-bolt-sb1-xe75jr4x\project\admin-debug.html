<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Диагностика админки</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: white; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; max-height: 300px; }
        .status { padding: 5px 10px; border-radius: 4px; font-weight: bold; margin: 5px 0; }
        .status.ok { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Диагностика админки</h1>
        
        <div class="card info">
            <h2>📋 Статус системы</h2>
            <div id="system-status">
                <div>🔄 Проверка...</div>
            </div>
        </div>

        <div class="card">
            <h2>🧪 Тесты API</h2>
            <div class="grid">
                <button onclick="testAll()">Полная диагностика</button>
                <button onclick="testParsingResults()">Тест результатов парсинга</button>
                <button onclick="testRateTrends()">Тест трендов курсов</button>
                <button onclick="clearResults()">Очистить результаты</button>
            </div>
        </div>

        <div id="results"></div>

        <div class="card">
            <h2>🔗 Быстрые ссылки</h2>
            <div class="grid">
                <a href="http://localhost:8000/health" target="_blank">Health Check</a>
                <a href="http://localhost:8000/api/v1/parsing/results" target="_blank">Parsing Results API</a>
                <a href="http://localhost:8000/api/v1/parsing/historical-rates/trends" target="_blank">Rate Trends API</a>
                <a href="http://localhost:5174/" target="_blank">Админка</a>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="card ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function updateSystemStatus(message, isOk = true) {
            const statusDiv = document.getElementById('system-status');
            const statusClass = isOk ? 'ok' : 'error';
            statusDiv.innerHTML = `<div class="status ${statusClass}">${message}</div>`;
        }

        async function testEndpoint(url, name) {
            try {
                log(`🔄 Тестирование: ${name}`, 'info');
                
                const startTime = Date.now();
                const response = await fetch(url);
                const endTime = Date.now();
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const responseTime = endTime - startTime;
                
                if (data.success) {
                    const dataCount = data.data?.results?.length || data.data?.trends?.length || 0;
                    log(`✅ ${name} - УСПЕХ<br>
                        📊 Записей: ${dataCount}<br>
                        ⏱️ Время: ${responseTime}ms<br>
                        📋 Структура данных корректна`, 'success');
                    return true;
                } else {
                    throw new Error('API вернул success: false');
                }
                
            } catch (error) {
                log(`❌ ${name} - ОШИБКА<br>
                    🚨 Error: ${error.message}<br>
                    💡 Проверьте консоль браузера для деталей`, 'error');
                console.error(`${name} error:`, error);
                return false;
            }
        }

        async function testParsingResults() {
            const url = `${API_BASE_URL}/parsing/results?limit=50&offset=0&hours_back=24`;
            return await testEndpoint(url, 'Результаты парсинга');
        }

        async function testRateTrends() {
            const url = `${API_BASE_URL}/parsing/historical-rates/trends?currency_pair=RUB/THB&days_back=30&interval=daily`;
            return await testEndpoint(url, 'Тренды курсов');
        }

        async function testHealthCheck() {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    const data = await response.json();
                    updateSystemStatus(`🟢 Сервер работает (${data.server})`, true);
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateSystemStatus(`🔴 Сервер недоступен: ${error.message}`, false);
                return false;
            }
        }

        async function testAll() {
            clearResults();
            log('🚀 Запуск полной диагностики...', 'info');
            
            const healthOk = await testHealthCheck();
            if (!healthOk) {
                log('❌ Сервер недоступен. Остальные тесты пропущены.', 'error');
                return;
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
            const parsingOk = await testParsingResults();
            
            await new Promise(resolve => setTimeout(resolve, 500));
            const trendsOk = await testRateTrends();
            
            const allOk = parsingOk && trendsOk;
            
            if (allOk) {
                log('🎉 Все тесты прошли успешно! Админка должна работать.', 'success');
                updateSystemStatus('🟢 Все системы работают', true);
            } else {
                log('⚠️ Некоторые тесты не прошли. Проверьте ошибки выше.', 'error');
                updateSystemStatus('🔴 Обнаружены проблемы', false);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Автоматическая диагностика при загрузке
        window.addEventListener('load', async () => {
            log('🔧 Страница диагностики админки загружена', 'info');
            
            // Автоматически запускаем диагностику через 1 секунду
            setTimeout(async () => {
                await testAll();
            }, 1000);
        });

        // Периодическая проверка статуса сервера
        setInterval(async () => {
            await testHealthCheck();
        }, 10000); // Каждые 10 секунд

        // Обработка ошибок
        window.addEventListener('unhandledrejection', event => {
            log(`🚨 Необработанная ошибка Promise: ${event.reason}`, 'error');
            console.error('Unhandled promise rejection:', event.reason);
        });

        window.addEventListener('error', event => {
            log(`🚨 JavaScript ошибка: ${event.message}`, 'error');
            console.error('JavaScript error:', event.error);
        });
    </script>
</body>
</html>
