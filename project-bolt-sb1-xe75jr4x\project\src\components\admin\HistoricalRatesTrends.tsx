import React, { useState, useMemo } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  Calendar,
  Filter,
  Download,
  Refresh<PERSON>w,
  AlertCircle
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { config } from '../../config/environment';

interface RateTrend {
  time_period: string;
  currency_pair: string;
  avg_buy_rate: number;
  avg_sell_rate: number;
  min_buy_rate: number;
  max_buy_rate: number;
  min_sell_rate: number;
  max_sell_rate: number;
  avg_spread_percentage: number;
  data_points: number;
  unique_exchangers: number;
}

interface TrendsResponse {
  success: boolean;
  data: {
    trends: RateTrend[];
    currency_pair: string;
    interval: string;
    period: {
      days_back: number;
      start_date: string;
      end_date: string;
    };
  };
}

const HistoricalRatesTrends: React.FC = () => {
  const [filters, setFilters] = useState({
    currency_pair: 'RUB/THB',
    days_back: 30,
    interval: 'daily'
  });
  const [chartType, setChartType] = useState<'line' | 'bar'>('line');

  // Fetch rate trends
  const { 
    data: trendsData, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ['rate-trends', filters],
    queryFn: async (): Promise<TrendsResponse> => {
      const params = new URLSearchParams({
        currency_pair: filters.currency_pair,
        days_back: filters.days_back.toString(),
        interval: filters.interval
      });

      const response = await fetch(`${config.API_BASE_URL}/parsing/historical-rates/trends?${params}`);
      if (!response.ok) throw new Error('Failed to fetch rate trends');
      return response.json();
    },
    refetchInterval: 60000, // Refresh every minute
  });

  const trends = trendsData?.data?.trends || [];

  // Process data for charts
  const chartData = useMemo(() => {
    return trends.map(trend => ({
      date: new Date(trend.time_period).toLocaleDateString(),
      timestamp: trend.time_period,
      buyRate: trend.avg_buy_rate,
      sellRate: trend.avg_sell_rate,
      minBuy: trend.min_buy_rate,
      maxBuy: trend.max_buy_rate,
      minSell: trend.min_sell_rate,
      maxSell: trend.max_sell_rate,
      spread: trend.avg_spread_percentage,
      dataPoints: trend.data_points,
      exchangers: trend.unique_exchangers
    })).reverse(); // Reverse to show chronological order
  }, [trends]);

  // Calculate statistics
  const statistics = useMemo(() => {
    if (trends.length === 0) return null;

    const latestTrend = trends[0];
    const oldestTrend = trends[trends.length - 1];
    
    const buyRateChange = latestTrend.avg_buy_rate - oldestTrend.avg_buy_rate;
    const sellRateChange = latestTrend.avg_sell_rate - oldestTrend.avg_sell_rate;
    const buyRateChangePercent = (buyRateChange / oldestTrend.avg_buy_rate) * 100;
    const sellRateChangePercent = (sellRateChange / oldestTrend.avg_sell_rate) * 100;

    return {
      currentBuyRate: latestTrend.avg_buy_rate,
      currentSellRate: latestTrend.avg_sell_rate,
      currentSpread: latestTrend.avg_spread_percentage,
      buyRateChange,
      sellRateChange,
      buyRateChangePercent,
      sellRateChangePercent,
      totalDataPoints: trends.reduce((sum, t) => sum + t.data_points, 0),
      avgExchangers: Math.round(trends.reduce((sum, t) => sum + t.unique_exchangers, 0) / trends.length)
    };
  }, [trends]);

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const exportData = () => {
    const csvContent = [
      ['Date', 'Buy Rate', 'Sell Rate', 'Spread %', 'Data Points', 'Exchangers'].join(','),
      ...chartData.map(item => [
        item.date,
        item.buyRate,
        item.sellRate,
        item.spread,
        item.dataPoints,
        item.exchangers
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rate-trends-${filters.currency_pair}-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center space-x-2 text-red-800">
          <AlertCircle className="w-5 h-5" />
          <span className="font-medium">Error loading rate trends</span>
        </div>
        <p className="text-red-600 mt-2">Failed to fetch rate trends. Please try again.</p>
        <button
          onClick={() => refetch()}
          className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Historical Rate Trends</h2>
          <p className="text-gray-600">Analyze exchange rate trends and patterns over time</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setChartType('line')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                chartType === 'line' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Line Chart
            </button>
            <button
              onClick={() => setChartType('bar')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                chartType === 'bar' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Bar Chart
            </button>
          </div>
          <button
            onClick={exportData}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
          <button
            onClick={() => refetch()}
            disabled={isLoading}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Currency Pair
            </label>
            <select
              value={filters.currency_pair}
              onChange={(e) => handleFilterChange('currency_pair', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="RUB/THB">RUB/THB</option>
              <option value="THB/RUB">THB/RUB</option>
              <option value="USDT/THB">USDT/THB</option>
              <option value="USD/THB">USD/THB</option>
              <option value="EUR/THB">EUR/THB</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Time Period
            </label>
            <select
              value={filters.days_back}
              onChange={(e) => handleFilterChange('days_back', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={7}>Last 7 days</option>
              <option value={14}>Last 2 weeks</option>
              <option value={30}>Last 30 days</option>
              <option value={60}>Last 2 months</option>
              <option value={90}>Last 3 months</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Interval
            </label>
            <select
              value={filters.interval}
              onChange={(e) => handleFilterChange('interval', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="hourly">Hourly</option>
              <option value="daily">Daily</option>
            </select>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Current Buy Rate</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.currentBuyRate.toFixed(4)}</p>
              </div>
              <div className={`flex items-center space-x-1 ${
                statistics.buyRateChangePercent >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {statistics.buyRateChangePercent >= 0 ? (
                  <TrendingUp className="w-4 h-4" />
                ) : (
                  <TrendingDown className="w-4 h-4" />
                )}
                <span className="text-sm font-medium">
                  {statistics.buyRateChangePercent.toFixed(2)}%
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Current Sell Rate</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.currentSellRate.toFixed(4)}</p>
              </div>
              <div className={`flex items-center space-x-1 ${
                statistics.sellRateChangePercent >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {statistics.sellRateChangePercent >= 0 ? (
                  <TrendingUp className="w-4 h-4" />
                ) : (
                  <TrendingDown className="w-4 h-4" />
                )}
                <span className="text-sm font-medium">
                  {statistics.sellRateChangePercent.toFixed(2)}%
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div>
              <p className="text-sm text-gray-600">Average Spread</p>
              <p className="text-2xl font-bold text-gray-900">{statistics.currentSpread.toFixed(2)}%</p>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div>
              <p className="text-sm text-gray-600">Data Points</p>
              <p className="text-2xl font-bold text-gray-900">{statistics.totalDataPoints}</p>
              <p className="text-xs text-gray-500">Avg {statistics.avgExchangers} exchangers</p>
            </div>
          </div>
        </div>
      )}

      {/* Chart */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading rate trends...</span>
          </div>
        ) : chartData.length === 0 ? (
          <div className="text-center py-12">
            <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No trend data available</h3>
            <p className="text-gray-600">No historical rate data found for the selected filters.</p>
          </div>
        ) : (
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'line' ? (
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tick={{ fontSize: 12 }}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip 
                    labelFormatter={(label) => `Date: ${label}`}
                    formatter={(value: number, name: string) => [
                      value.toFixed(4),
                      name === 'buyRate' ? 'Buy Rate' : 
                      name === 'sellRate' ? 'Sell Rate' : 
                      name === 'spread' ? 'Spread %' : name
                    ]}
                  />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="buyRate" 
                    stroke="#10b981" 
                    strokeWidth={2}
                    name="Buy Rate"
                    dot={{ r: 3 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="sellRate" 
                    stroke="#ef4444" 
                    strokeWidth={2}
                    name="Sell Rate"
                    dot={{ r: 3 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="spread" 
                    stroke="#f59e0b" 
                    strokeWidth={2}
                    name="Spread %"
                    dot={{ r: 3 }}
                    yAxisId="right"
                  />
                </LineChart>
              ) : (
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tick={{ fontSize: 12 }}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip 
                    labelFormatter={(label) => `Date: ${label}`}
                    formatter={(value: number, name: string) => [
                      value.toFixed(4),
                      name === 'buyRate' ? 'Buy Rate' : 
                      name === 'sellRate' ? 'Sell Rate' : name
                    ]}
                  />
                  <Legend />
                  <Bar dataKey="buyRate" fill="#10b981" name="Buy Rate" />
                  <Bar dataKey="sellRate" fill="#ef4444" name="Sell Rate" />
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>
        )}
      </div>
    </div>
  );
};

export default HistoricalRatesTrends;
