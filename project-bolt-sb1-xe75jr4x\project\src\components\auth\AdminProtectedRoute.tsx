import React, { useEffect, useState } from 'react';
import { Shield, AlertCircle, LogIn, ArrowLeft } from 'lucide-react';
import { adminAuthService } from '../../services/adminAuthService';
import AdminLoginForm from './AdminLoginForm';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
}

const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    try {
      const isAuth = adminAuthService.isAuthenticated();
      const isValidSession = adminAuthService.isSessionValid();
      
      if (isAuth && isValidSession) {
        setIsAuthenticated(true);
      } else {
        // Clear invalid session
        if (isAuth && !isValidSession) {
          await adminAuthService.logout();
        }
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Authentication check failed:', error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoginSuccess = () => {
    setIsAuthenticated(true);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white rounded-xl shadow-lg p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Проверка доступа к административной панели...</p>
        </div>
      </div>
    );
  }

  // Not authenticated - show login form
  if (!isAuthenticated) {
    return <AdminLoginForm onSuccess={handleLoginSuccess} />;
  }

  // Authenticated - show admin content
  return <>{children}</>;
};

export default AdminProtectedRoute;