#!/usr/bin/env python3
"""
Финальный сервер для админки - гарантированно работающий
"""

import json
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
from datetime import datetime, timedelta
import random
import sys
import threading
import time

PORT = 8000

class AdminAPIHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        try:
            # Логируем запрос с принудительным выводом
            log_message = f"[{datetime.now().strftime('%H:%M:%S')}] GET {self.path}"
            print(log_message)
            sys.stdout.flush()  # Принудительный вывод

            # CORS заголовки
            self.send_response(200)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
            self.end_headers()
            
            parsed = urlparse(self.path)
            path = parsed.path
            
            # Роутинг
            if path == '/health':
                response = self.get_health()
            elif path == '/api/v1/parsing/results':
                response = self.get_parsing_results()
            elif path == '/api/v1/parsing/historical-rates/trends':
                response = self.get_rate_trends()
            elif path == '/api/v1/parsing/historical-rates':
                response = self.get_historical_rates()
            else:
                response = {"error": "Not found", "path": path}
            
            # Отправляем ответ
            json_response = json.dumps(response, ensure_ascii=False, indent=2)
            self.wfile.write(json_response.encode('utf-8'))
            log_message = f"[{datetime.now().strftime('%H:%M:%S')}] Response sent: {len(json_response)} bytes"
            print(log_message)
            sys.stdout.flush()  # Принудительный вывод
            
        except Exception as e:
            error_message = f"[{datetime.now().strftime('%H:%M:%S')}] ERROR: {e}"
            print(error_message)
            sys.stdout.flush()  # Принудительный вывод
            self.send_error(500, str(e))
    
    def do_OPTIONS(self):
        # Preflight запросы
        log_message = f"[{datetime.now().strftime('%H:%M:%S')}] OPTIONS {self.path}"
        print(log_message)
        sys.stdout.flush()  # Принудительный вывод
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def get_health(self):
        return {
            "status": "ok",
            "timestamp": datetime.now().isoformat(),
            "server": "final_server.py",
            "uptime": time.time() - start_time
        }
    
    def get_parsing_results(self):
        # Генерируем результаты парсинга
        results = []
        statuses = ['success', 'failed', 'error', 'partial']
        exchangers = ['SuperRich Thailand', 'Vasu Exchange', 'Happy Rich Exchange', 'Grand SuperRich', 'Siam Exchange']
        
        for i in range(20):
            status = random.choice(statuses)
            exchanger = random.choice(exchangers)
            timestamp = datetime.now() - timedelta(hours=random.randint(1, 48))
            
            results.append({
                "id": i + 1,
                "exchanger_id": exchanger.lower().replace(' ', '_'),
                "exchanger_name": exchanger,
                "parsing_timestamp": timestamp.isoformat(),
                "status": status,
                "execution_time_ms": random.randint(500, 5000),
                "source_url": f"https://{exchanger.lower().replace(' ', '')}.com/rates",
                "parsed_pairs_count": random.randint(1, 5) if status == 'success' else 0,
                "total_pairs_expected": 5,
                "error_message": "Connection timeout" if status in ['failed', 'error'] else None,
                "error_details": {"timeout": True, "retry_count": random.randint(0, 3)} if status in ['failed', 'error'] else None,
                "raw_data": {"rates": {"RUB/THB": {"buy": round(2.45 + random.uniform(-0.1, 0.1), 4), "sell": round(2.55 + random.uniform(-0.1, 0.1), 4)}}} if status == 'success' else None,
                "metadata": {"parser_version": "1.0", "user_agent": "Mozilla/5.0"},
                "created_at": timestamp.isoformat()
            })
        
        return {
            "success": True,
            "data": {
                "results": results,
                "pagination": {
                    "total": len(results),
                    "limit": 50,
                    "offset": 0,
                    "has_more": False
                },
                "filters": {
                    "exchanger_id": None,
                    "status": None,
                    "hours_back": 24
                }
            }
        }
    
    def get_rate_trends(self):
        # Генерируем тренды курсов
        trends = []
        
        for i in range(30):
            date = datetime.now() - timedelta(days=i)
            base_buy_rate = 2.45
            base_sell_rate = 2.55
            
            # Добавляем реалистичную волатильность
            daily_volatility = random.uniform(-0.08, 0.08)
            
            trends.append({
                "time_period": date.date().isoformat(),
                "currency_pair": "RUB/THB",
                "avg_buy_rate": round(base_buy_rate + daily_volatility, 4),
                "avg_sell_rate": round(base_sell_rate + daily_volatility, 4),
                "min_buy_rate": round(base_buy_rate + daily_volatility - 0.03, 4),
                "max_buy_rate": round(base_buy_rate + daily_volatility + 0.03, 4),
                "min_sell_rate": round(base_sell_rate + daily_volatility - 0.03, 4),
                "max_sell_rate": round(base_sell_rate + daily_volatility + 0.03, 4),
                "avg_spread_percentage": round(((base_sell_rate - base_buy_rate) / base_buy_rate) * 100, 2),
                "data_points": random.randint(15, 60),
                "unique_exchangers": random.randint(3, 5)
            })
        
        return {
            "success": True,
            "data": {
                "trends": trends,
                "currency_pair": "RUB/THB",
                "interval": "daily",
                "period": {
                    "days_back": 30,
                    "start_date": (datetime.now() - timedelta(days=30)).date().isoformat(),
                    "end_date": datetime.now().date().isoformat()
                }
            }
        }
    
    def get_historical_rates(self):
        # Генерируем исторические курсы
        rates = []
        exchangers = ['SuperRich Thailand', 'Vasu Exchange', 'Happy Rich Exchange']
        
        for i in range(15):
            exchanger = random.choice(exchangers)
            timestamp = datetime.now() - timedelta(hours=random.randint(1, 24))
            
            rates.append({
                "id": i + 1,
                "exchanger_id": exchanger.lower().replace(' ', '_'),
                "exchanger_name": exchanger,
                "currency_pair": "RUB/THB",
                "buy_rate": round(2.45 + random.uniform(-0.1, 0.1), 4),
                "sell_rate": round(2.55 + random.uniform(-0.1, 0.1), 4),
                "parsed_at": timestamp.isoformat()
            })
        
        return {
            "success": True,
            "data": {
                "rates": rates,
                "total_count": len(rates),
                "available_filters": {
                    "currency_pairs": [
                        {"currency_pair": "RUB/THB", "records_count": 150},
                        {"currency_pair": "THB/RUB", "records_count": 120},
                        {"currency_pair": "USD/THB", "records_count": 200}
                    ],
                    "exchangers": [
                        {"exchanger_id": "superrich_thailand", "exchanger_name": "SuperRich Thailand", "records_count": 80},
                        {"exchanger_id": "vasu_exchange", "exchanger_name": "Vasu Exchange", "records_count": 65},
                        {"exchanger_id": "happy_rich_exchange", "exchanger_name": "Happy Rich Exchange", "records_count": 55}
                    ]
                }
            }
        }
    
    def log_message(self, format, *args):
        # Отключаем стандартное логирование HTTP сервера
        pass

# Глобальная переменная для времени запуска
start_time = time.time()

def main():
    print("🚀 Запуск финального сервера админки...")
    print(f"📍 Порт: {PORT}")
    print(f"🔗 Health check: http://localhost:{PORT}/health")
    print(f"📊 Parsing results: http://localhost:{PORT}/api/v1/parsing/results")
    print(f"📈 Rate trends: http://localhost:{PORT}/api/v1/parsing/historical-rates/trends")
    print(f"📋 Historical rates: http://localhost:{PORT}/api/v1/parsing/historical-rates")
    print("=" * 60)
    print("Нажмите Ctrl+C для остановки")
    print("=" * 60)
    
    try:
        with socketserver.TCPServer(("", PORT), AdminAPIHandler) as httpd:
            print(f"✅ Сервер запущен на порту {PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Сервер остановлен пользователем")
    except OSError as e:
        if e.errno == 10048:  # Address already in use
            print(f"❌ Порт {PORT} уже используется")
            print("💡 Остановите другой сервер или используйте другой порт")
        else:
            print(f"❌ Ошибка сервера: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
