import React, { useState, useEffect } from 'react';
import { Users, Search, Filter, Plus, Edit, Trash2, Shield, Ban, CheckCircle, XCircle, Eye, Mail, Calendar, Clock, Database, Globe, Server } from 'lucide-react';
import { User } from '../../types/admin';
import { adminAPI } from '../../data/adminData';
import { adminUserService, AdminUser, UserFilters } from '../../services/adminUserService';

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<'all' | 'admin' | 'moderator' | 'user'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null);
  const [notification, setNotification] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  const [newUser, setNewUser] = useState({
    username: '',
    email: '',
    role: 'user' as 'admin' | 'moderator' | 'user',
    status: 'active' as 'active' | 'inactive' | 'banned',
    permissions: [] as string[]
  });

  useEffect(() => {
    loadUsers();
  }, [currentPage, searchQuery, roleFilter, statusFilter]);

  const loadUsers = async () => {
    setLoading(true);
    try {
      const filters: UserFilters = {
        page: currentPage,
        limit: 10,
        search: searchQuery || undefined,
        role: roleFilter !== 'all' ? roleFilter : undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined
      };

      const response = await adminUserService.getUsers(filters);
      setUsers(response.users);
      setTotalPages(response.totalPages);
      setTotalCount(response.totalCount);
    } catch (error) {
      console.error('Error loading users:', error);
      showNotification('error', 'Ошибка загрузки пользователей');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await adminAPI.createUser(newUser);
      setShowCreateModal(false);
      setNewUser({ username: '', email: '', role: 'user', status: 'active', permissions: [] });
      showNotification('success', 'Пользователь успешно создан');
      loadUsers();
    } catch (error) {
      showNotification('error', 'Ошибка создания пользователя');
    }
  };

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedUser) return;

    try {
      const success = await adminUserService.updateUser(selectedUser.id, selectedUser);
      if (success) {
        setShowEditModal(false);
        setSelectedUser(null);
        showNotification('success', 'Пользователь успешно обновлен');
        loadUsers();
      } else {
        showNotification('error', 'Не удалось обновить пользователя');
      }
    } catch (error) {
      console.error('Error updating user:', error);
      showNotification('error', 'Ошибка обновления пользователя');
    }
  };

  const handleDeleteUser = async (id: string) => {
    if (!confirm('Вы уверены, что хотите удалить этого пользователя?')) return;

    try {
      const success = await adminUserService.deleteUser(id);
      if (success) {
        showNotification('success', 'Пользователь успешно удален');
        loadUsers();
      } else {
        showNotification('error', 'Не удалось удалить пользователя');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      showNotification('error', 'Ошибка удаления пользователя');
    }
  };

  const handleStatusChange = async (user: AdminUser, newStatus: boolean) => {
    try {
      const success = await adminUserService.updateUser(user.id, { isActive: newStatus });
      if (success) {
        showNotification('success', `Статус пользователя изменен на ${newStatus ? 'активный' : 'неактивный'}`);
        loadUsers();
      } else {
        showNotification('error', 'Не удалось изменить статус пользователя');
      }
    } catch (error) {
      console.error('Error changing user status:', error);
      showNotification('error', 'Ошибка изменения статуса');
    }
  };

  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  const getRegistrationSourceIcon = (source: string) => {
    switch (source) {
      case 'supabase': return <Database className="w-3 h-3" />;
      case 'backend': return <Server className="w-3 h-3" />;
      case 'fallback': return <Globe className="w-3 h-3" />;
      default: return <Globe className="w-3 h-3" />;
    }
  };

  const getRegistrationSourceText = (source: string) => {
    switch (source) {
      case 'supabase': return 'Supabase';
      case 'backend': return 'Backend API';
      case 'fallback': return 'Локальное хранилище';
      default: return 'Неизвестно';
    }
  };
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'moderator': return 'bg-blue-100 text-blue-800';
      case 'user': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-yellow-100 text-yellow-800';
      case 'banned': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'inactive': return <Clock className="w-4 h-4" />;
      case 'banned': return <Ban className="w-4 h-4" />;
      default: return <XCircle className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Users className="w-6 h-6 text-blue-600" />
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Управление пользователями</h2>
            <p className="text-sm text-gray-600">
              Всего пользователей: {totalCount} | Страница {currentPage} из {totalPages}
            </p>
          </div>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Добавить пользователя</span>
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Поиск пользователей..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value as any)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">Все роли</option>
            <option value="admin">Администраторы</option>
            <option value="moderator">Модераторы</option>
            <option value="user">Пользователи</option>
          </select>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as any)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">Все статусы</option>
            <option value="active">Активные</option>
            <option value="inactive">Неактивные</option>
          </select>
          
          <button
            onClick={loadUsers}
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center justify-center space-x-2 transition-colors"
          >
            <Filter className="w-4 h-4" />
            <span>Применить</span>
          </button>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Загрузка пользователей...</p>
          </div>
        ) : (
          <div className="overflow-x-auto" role="region" aria-label="Таблица пользователей системы">
            <table className="w-full" role="table" aria-label="Список пользователей с возможностью управления">
              <caption className="sr-only">
                Таблица пользователей системы. Показано {users.length} пользователей. 
                Используйте клавиши Tab для навигации между элементами управления.
              </caption>
              <thead className="bg-gray-50" role="rowgroup">
                <tr>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    scope="col"
                  >
                    Пользователь
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    scope="col"
                  >
                    Роль
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    scope="col"
                  >
                    Статус
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    scope="col"
                  >
                    Последний вход
                  </th>
                  <th 
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    scope="col"
                  >
                    Действия
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200" role="rowgroup">
                {users.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50 focus-within:bg-gray-50" role="row">
                    <td className="px-6 py-4 whitespace-nowrap" role="cell">
                      <div className="flex items-center">
                        <div 
                          className="bg-blue-500 w-10 h-10 rounded-full flex items-center justify-center"
                          aria-hidden="true"
                        >
                          <span className="text-white font-semibold">
                            {user.username.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900" id={`user-${user.id}`}>
                            {user.username || `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email.split('@')[0]}
                          </div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Mail className="w-3 h-3 mr-1" aria-hidden="true" />
                            <a
                              href={`mailto:${user.email}`}
                              className="hover:text-blue-600 focus:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
                              aria-label={`Отправить email пользователю ${user.username || user.email}`}
                            >
                              {user.email}
                            </a>
                          </div>
                          <div className="text-xs text-gray-400 mt-1 flex items-center">
                            {getRegistrationSourceIcon(user.registrationSource)}
                            <span className="ml-1">{getRegistrationSourceText(user.registrationSource)}</span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap" role="cell">
                      <span 
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}
                        aria-label={`Роль пользователя: ${user.role}`}
                      >
                        <Shield className="w-3 h-3 mr-1" aria-hidden="true" />
                        {user.role}
                      </span>
                      <div className="text-xs text-gray-500 mt-1">
                        Создан: {new Date(user.createdAt).toLocaleDateString('ru-RU')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap" role="cell">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                        aria-label={`Статус пользователя: ${user.isActive ? 'активный' : 'неактивный'}`}
                      >
                        {user.isActive ? <CheckCircle className="w-3 h-3 mr-1" /> : <XCircle className="w-3 h-3 mr-1" />}
                        <span className="ml-1">{user.isActive ? 'Активный' : 'Неактивный'}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500" role="cell">
                      {user.lastLogin ? (
                        <div className="flex items-center" aria-label={`Последний вход: ${new Date(user.lastLogin).toLocaleDateString('ru-RU')}`}>
                          <Calendar className="w-3 h-3 mr-1" aria-hidden="true" />
                          <time dateTime={user.lastLogin}>
                            {new Date(user.lastLogin).toLocaleDateString('ru-RU')}
                          </time>
                          <div className="text-xs text-gray-400 block">
                            {new Date(user.lastLogin).toLocaleTimeString('ru-RU')}
                          </div>
                        </div>
                      ) : (
                        <span aria-label="Пользователь никогда не входил в систему">Никогда</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" role="cell">
                      <div className="flex items-center justify-end space-x-2" role="group" aria-label={`Действия для пользователя ${user.username || user.email}`}>
                        <button
                          onClick={() => {
                            setSelectedUser(user);
                            setShowEditModal(true);
                          }}
                          className="text-blue-600 hover:text-blue-900 focus:text-blue-900 p-1 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                          aria-label={`Редактировать пользователя ${user.username || user.email}`}
                        >
                          <Edit className="w-4 h-4" aria-hidden="true" />
                        </button>

                        {user.isActive ? (
                          <button
                            onClick={() => handleStatusChange(user, false)}
                            className="text-red-600 hover:text-red-900 focus:text-red-900 p-1 rounded focus:outline-none focus:ring-2 focus:ring-red-500"
                            aria-label={`Деактивировать пользователя ${user.username || user.email}`}
                          >
                            <Ban className="w-4 h-4" aria-hidden="true" />
                          </button>
                        ) : (
                          <button
                            onClick={() => handleStatusChange(user, true)}
                            className="text-green-600 hover:text-green-900 focus:text-green-900 p-1 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
                            aria-label={`Активировать пользователя ${user.username || user.email}`}
                          >
                            <CheckCircle className="w-4 h-4" aria-hidden="true" />
                          </button>
                        )}
                        
                        <button
                          onClick={() => handleDeleteUser(user.id)}
                          className="text-red-600 hover:text-red-900 focus:text-red-900 p-1 rounded focus:outline-none focus:ring-2 focus:ring-red-500"
                          aria-label={`Удалить пользователя ${user.username || user.email}`}
                        >
                          <Trash2 className="w-4 h-4" aria-hidden="true" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Страница {currentPage} из {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Назад
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Вперед
            </button>
          </div>
        </div>
      )}

      {/* Create User Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center p-4 z-50 overflow-y-auto">
          <div className="bg-white rounded-lg w-full max-w-lg mt-8 mb-8 p-6">
            <h3 className="text-lg font-semibold mb-4">Создать пользователя</h3>
            <form onSubmit={handleCreateUser} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Имя пользователя</label>
                <input
                  type="text"
                  value={newUser.username}
                  onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Роль</label>
                <select
                  value={newUser.role}
                  onChange={(e) => setNewUser({ ...newUser, role: e.target.value as any })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500"
                >
                  <option value="user">Пользователь</option>
                  <option value="moderator">Модератор</option>
                  <option value="admin">Администратор</option>
                </select>
              </div>
              <div className="flex space-x-3">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg transition-colors"
                >
                  Создать
                </button>
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 rounded-lg transition-colors"
                >
                  Отмена
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {showEditModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center p-4 z-50 overflow-y-auto">
          <div className="bg-white rounded-lg w-full max-w-lg mt-8 mb-8 p-6">
            <h3 className="text-lg font-semibold mb-4">Редактировать пользователя</h3>
            <form onSubmit={handleUpdateUser} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Имя пользователя</label>
                <input
                  type="text"
                  value={selectedUser.username}
                  onChange={(e) => setSelectedUser({ ...selectedUser, username: e.target.value })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  value={selectedUser.email}
                  onChange={(e) => setSelectedUser({ ...selectedUser, email: e.target.value })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Роль</label>
                <select
                  value={selectedUser.role}
                  onChange={(e) => setSelectedUser({ ...selectedUser, role: e.target.value as any })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500"
                >
                  <option value="user">Пользователь</option>
                  <option value="moderator">Модератор</option>
                  <option value="admin">Администратор</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Статус</label>
                <select
                  value={selectedUser.status}
                  onChange={(e) => setSelectedUser({ ...selectedUser, status: e.target.value as any })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500"
                >
                  <option value="active">Активный</option>
                  <option value="inactive">Неактивный</option>
                  <option value="banned">Заблокированный</option>
                </select>
              </div>
              <div className="flex space-x-3">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg transition-colors"
                >
                  Сохранить
                </button>
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 rounded-lg transition-colors"
                >
                  Отмена
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Notification */}
      {notification && (
        <div className="fixed top-4 right-4 z-50">
          <div className={`rounded-lg shadow-lg p-4 max-w-sm ${
            notification.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
          }`}>
            <p className="font-medium text-sm">{notification.message}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;