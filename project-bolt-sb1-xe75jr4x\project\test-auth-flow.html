<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication Flow</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Authentication Flow Test</h1>
    
    <div class="test-section info">
        <h2>Test Controls</h2>
        <button onclick="clearStorage()">Clear LocalStorage</button>
        <button onclick="testRegistration()">Test Registration</button>
        <button onclick="testLogin()">Test Login</button>
        <button onclick="showStorageData()">Show Storage Data</button>
    </div>

    <div id="results"></div>

    <script>
        const testEmail = '<EMAIL>';
        const testPassword = 'test123456';
        const testName = 'Test User';

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="test-section ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearStorage() {
            localStorage.clear();
            log('LocalStorage cleared', 'info');
            showStorageData();
        }

        function showStorageData() {
            const registeredUsers = localStorage.getItem('registered_users');
            const fallbackUsers = localStorage.getItem('fallback_users');
            const authData = localStorage.getItem('auth_data');
            
            log(`
                <h3>Current Storage Data:</h3>
                <strong>Registered Users:</strong>
                <pre>${registeredUsers || 'None'}</pre>
                <strong>Fallback Users:</strong>
                <pre>${fallbackUsers || 'None'}</pre>
                <strong>Auth Data:</strong>
                <pre>${authData || 'None'}</pre>
            `, 'info');
        }

        async function testRegistration() {
            try {
                log('Starting registration test...', 'info');
                
                // Simulate userRegistrationService.registerUser
                const registrationData = {
                    email: testEmail,
                    password: testPassword,
                    firstName: 'Test',
                    lastName: 'User',
                    phone: '+1234567890'
                };

                // Check if user already exists
                const existingUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
                const userExists = existingUsers.some(user => user.email === testEmail);

                if (userExists) {
                    log('User already exists, removing first...', 'info');
                    const filteredUsers = existingUsers.filter(user => user.email !== testEmail);
                    localStorage.setItem('registered_users', JSON.stringify(filteredUsers));
                }

                // Create new user (simulating userRegistrationService fallback)
                const newUser = {
                    id: `user-${Date.now()}`,
                    email: registrationData.email,
                    password: registrationData.password,
                    firstName: registrationData.firstName,
                    lastName: registrationData.lastName,
                    phone: registrationData.phone,
                    role: 'user',
                    isActive: true,
                    createdAt: new Date().toISOString(),
                };

                const updatedUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
                updatedUsers.push(newUser);
                localStorage.setItem('registered_users', JSON.stringify(updatedUsers));

                log(`Registration successful! User created: ${newUser.email}`, 'success');
                showStorageData();
                
            } catch (error) {
                log(`Registration failed: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            try {
                log('Starting login test...', 'info');
                
                // Simulate authService.fallbackLogin
                const credentials = {
                    email: testEmail,
                    password: testPassword
                };

                // Check registered users
                const registeredUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
                log(`Found ${registeredUsers.length} registered users`, 'info');
                
                const registeredUser = registeredUsers.find(user => 
                    user.email === credentials.email && user.password === credentials.password
                );

                if (registeredUser) {
                    log(`Login successful! Found user: ${registeredUser.email}`, 'success');
                    
                    // Simulate storing auth tokens
                    const authResponse = {
                        user: {
                            id: registeredUser.id,
                            email: registeredUser.email,
                            role: registeredUser.role || 'user',
                            permissions: ['read', 'write'],
                            isActive: true,
                            twoFactorEnabled: false,
                            lastLogin: new Date().toISOString(),
                        },
                        accessToken: `fallback-token-${Date.now()}`,
                        refreshToken: `fallback-refresh-${Date.now()}`,
                        expiresIn: 3600,
                        expiresAt: Date.now() + (3600 * 1000)
                    };

                    localStorage.setItem('auth_data', JSON.stringify(authResponse));
                    log('Auth tokens stored successfully', 'success');
                    showStorageData();
                } else {
                    log('Login failed: Invalid email or password', 'error');
                    log(`Searched for: ${credentials.email} / ${credentials.password}`, 'info');
                    log(`Available users: ${JSON.stringify(registeredUsers.map(u => ({email: u.email, hasPassword: !!u.password})), null, 2)}`, 'info');
                }
                
            } catch (error) {
                log(`Login failed: ${error.message}`, 'error');
            }
        }

        // Show initial storage data
        window.addEventListener('load', () => {
            log('Authentication Flow Test Loaded', 'info');
            showStorageData();
        });
    </script>
</body>
</html>
