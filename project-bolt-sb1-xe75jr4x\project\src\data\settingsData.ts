/**
 * System Settings Data Management
 * Handles all system configuration settings with persistence and validation
 */

import { SystemSettings } from '../types/admin';

// Default system settings
const defaultSettings: SystemSettings[] = [
  // General Settings
  {
    id: 'app_name',
    category: 'general',
    key: 'app_name',
    value: 'Thailand Exchange Platform',
    type: 'string',
    description: 'Название приложения',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'app_version',
    category: 'general',
    key: 'app_version',
    value: '1.0.0',
    type: 'string',
    description: 'Версия приложения',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'maintenance_mode',
    category: 'general',
    key: 'maintenance_mode',
    value: 'false',
    type: 'boolean',
    description: 'Режим технического обслуживания',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'debug_mode',
    category: 'general',
    key: 'debug_mode',
    value: 'false',
    type: 'boolean',
    description: 'Режим отладки',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },

  // Exchange Rate Settings
  {
    id: 'rates_update_interval',
    category: 'rates',
    key: 'rates_update_interval',
    value: '15',
    type: 'number',
    description: 'Интервал обновления курсов (минуты)',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'auto_sync_networks',
    category: 'rates',
    key: 'auto_sync_networks',
    value: 'true',
    type: 'boolean',
    description: 'Автоматическая синхронизация курсов в сети',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'network_sync_cooldown',
    category: 'rates',
    key: 'network_sync_cooldown',
    value: '5',
    type: 'number',
    description: 'Задержка синхронизации сети (минуты)',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'rate_validation_strict',
    category: 'rates',
    key: 'rate_validation_strict',
    value: 'true',
    type: 'boolean',
    description: 'Строгая валидация курсов валют',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'google_sheets_url',
    category: 'rates',
    key: 'google_sheets_url',
    value: '',
    type: 'string',
    description: 'URL Google Таблицы с курсами',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'google_sheets_range',
    category: 'rates',
    key: 'google_sheets_range',
    value: 'A1:Z1000',
    type: 'string',
    description: 'Диапазон ячеек Google Таблицы',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'parsing_timeout',
    category: 'rates',
    key: 'parsing_timeout',
    value: '30',
    type: 'number',
    description: 'Таймаут парсинга (секунды)',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'max_retry_attempts',
    category: 'rates',
    key: 'max_retry_attempts',
    value: '3',
    type: 'number',
    description: 'Максимальное количество попыток парсинга',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'mandatory_currency_pairs',
    category: 'rates',
    key: 'mandatory_currency_pairs',
    value: '["THB/RUB", "RUB/THB", "USDT/THB"]',
    type: 'json',
    description: 'Обязательные валютные пары',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },

  // Email Settings
  {
    id: 'smtp_host',
    category: 'email',
    key: 'smtp_host',
    value: '',
    type: 'string',
    description: 'SMTP сервер',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'smtp_port',
    category: 'email',
    key: 'smtp_port',
    value: '587',
    type: 'number',
    description: 'SMTP порт',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'smtp_username',
    category: 'email',
    key: 'smtp_username',
    value: '',
    type: 'string',
    description: 'SMTP пользователь',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'smtp_password',
    category: 'email',
    key: 'smtp_password',
    value: '',
    type: 'string',
    description: 'SMTP пароль',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'email_notifications_enabled',
    category: 'email',
    key: 'email_notifications_enabled',
    value: 'false',
    type: 'boolean',
    description: 'Включить email уведомления',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },

  // Security Settings
  {
    id: 'session_timeout',
    category: 'security',
    key: 'session_timeout',
    value: '30',
    type: 'number',
    description: 'Время жизни сессии (минуты)',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'max_login_attempts',
    category: 'security',
    key: 'max_login_attempts',
    value: '5',
    type: 'number',
    description: 'Максимальное количество попыток входа',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'require_2fa',
    category: 'security',
    key: 'require_2fa',
    value: 'false',
    type: 'boolean',
    description: 'Требовать двухфакторную аутентификацию',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  },
  {
    id: 'allowed_origins',
    category: 'security',
    key: 'allowed_origins',
    value: '["http://localhost:3000", "http://localhost:5173"]',
    type: 'json',
    description: 'Разрешенные домены для CORS',
    updatedAt: new Date().toISOString(),
    updatedBy: 'system'
  }
];

// Settings storage key
const SETTINGS_STORAGE_KEY = 'systemSettings';
const SETTINGS_BACKUP_KEY = 'systemSettings_backup';

// Initialize settings from localStorage or defaults
let systemSettings: SystemSettings[] = [...defaultSettings];

// Load saved settings
const loadSavedSettings = () => {
  try {
    const savedSettings = localStorage.getItem(SETTINGS_STORAGE_KEY);
    if (savedSettings) {
      const parsedSettings = JSON.parse(savedSettings);
      if (Array.isArray(parsedSettings) && parsedSettings.length > 0) {
        systemSettings = parsedSettings;
        console.log('Loaded saved settings:', {
          count: parsedSettings.length,
          categories: [...new Set(parsedSettings.map(s => s.category))],
          timestamp: new Date().toISOString()
        });
        return;
      }
    }

    // Try backup if main settings failed
    const backupSettings = localStorage.getItem(SETTINGS_BACKUP_KEY);
    if (backupSettings) {
      const backup = JSON.parse(backupSettings);
      if (backup.data && Array.isArray(backup.data)) {
        systemSettings = backup.data;
        console.log('Loaded backup settings:', {
          count: backup.data.length,
          backupTimestamp: backup.timestamp
        });
        saveSettings(); // Save backup as main settings
        return;
      }
    }

    console.log('No saved settings found, using defaults');
  } catch (error) {
    console.error('Error loading saved settings:', error);
    // Use defaults on error
    systemSettings = [...defaultSettings];
  }
};

// Save settings to localStorage
const saveSettings = () => {
  try {
    const dataToSave = JSON.stringify(systemSettings);
    localStorage.setItem(SETTINGS_STORAGE_KEY, dataToSave);
    
    // Create backup
    localStorage.setItem(SETTINGS_BACKUP_KEY, JSON.stringify({
      data: systemSettings,
      timestamp: new Date().toISOString(),
      count: systemSettings.length
    }));

    console.log('Settings saved:', {
      count: systemSettings.length,
      categories: [...new Set(systemSettings.map(s => s.category))],
      dataSize: dataToSave.length,
      timestamp: new Date().toISOString()
    });

    // Trigger settings update event
    window.dispatchEvent(new CustomEvent('settingsUpdated', {
      detail: {
        timestamp: Date.now(),
        settingsCount: systemSettings.length
      }
    }));

  } catch (error) {
    console.error('Error saving settings:', error);
    throw new Error('Ошибка сохранения настроек');
  }
};

// Validation functions
const validateSettingValue = (setting: SystemSettings, value: string): { isValid: boolean; error?: string } => {
  try {
    switch (setting.type) {
      case 'number':
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
          return { isValid: false, error: 'Значение должно быть числом' };
        }
        if (setting.key === 'rates_update_interval' && numValue < 1) {
          return { isValid: false, error: 'Интервал должен быть больше 0' };
        }
        if (setting.key === 'session_timeout' && (numValue < 5 || numValue > 1440)) {
          return { isValid: false, error: 'Время сессии должно быть от 5 до 1440 минут' };
        }
        break;

      case 'boolean':
        if (value !== 'true' && value !== 'false') {
          return { isValid: false, error: 'Значение должно быть true или false' };
        }
        break;

      case 'json':
        try {
          JSON.parse(value);
        } catch {
          return { isValid: false, error: 'Некорректный JSON формат' };
        }
        break;

      case 'string':
        if (setting.key === 'google_sheets_url' && value && !value.includes('docs.google.com')) {
          return { isValid: false, error: 'URL должен быть ссылкой на Google Таблицу' };
        }
        break;
    }

    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'Ошибка валидации' };
  }
};

// Initialize settings
loadSavedSettings();

// Export settings management functions
export const settingsAPI = {
  getSettings: async (category?: string): Promise<SystemSettings[]> => {
    try {
      console.log('getSettings called:', { category, totalSettings: systemSettings.length });
      
      if (category && category !== 'all') {
        const filtered = systemSettings.filter(s => s.category === category);
        console.log(`Filtered settings for category ${category}:`, filtered.length);
        return filtered;
      }
      
      return [...systemSettings];
    } catch (error) {
      console.error('Error getting settings:', error);
      throw new Error('Ошибка загрузки настроек');
    }
  },

  updateSetting: async (key: string, value: string): Promise<SystemSettings> => {
    try {
      console.log('updateSetting called:', { key, value });
      
      const settingIndex = systemSettings.findIndex(s => s.key === key);
      if (settingIndex === -1) {
        throw new Error(`Настройка ${key} не найдена`);
      }

      const setting = systemSettings[settingIndex];
      
      // Validate new value
      const validation = validateSettingValue(setting, value);
      if (!validation.isValid) {
        throw new Error(validation.error || 'Некорректное значение');
      }

      // Update setting
      systemSettings[settingIndex] = {
        ...setting,
        value,
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin' // TODO: Get actual user
      };

      saveSettings();
      
      console.log(`Setting ${key} updated successfully:`, systemSettings[settingIndex]);
      return systemSettings[settingIndex];
    } catch (error) {
      console.error('Error updating setting:', error);
      throw error;
    }
  },

  createSetting: async (settingData: Omit<SystemSettings, 'id' | 'updatedAt' | 'updatedBy'>): Promise<SystemSettings> => {
    try {
      const newSetting: SystemSettings = {
        ...settingData,
        id: `custom_${Date.now()}`,
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
      };

      // Validate value
      const validation = validateSettingValue(newSetting, newSetting.value);
      if (!validation.isValid) {
        throw new Error(validation.error || 'Некорректное значение');
      }

      systemSettings.push(newSetting);
      saveSettings();
      
      console.log('Setting created:', newSetting);
      return newSetting;
    } catch (error) {
      console.error('Error creating setting:', error);
      throw error;
    }
  },

  deleteSetting: async (key: string): Promise<{ success: boolean }> => {
    try {
      const settingIndex = systemSettings.findIndex(s => s.key === key);
      if (settingIndex === -1) {
        throw new Error(`Настройка ${key} не найдена`);
      }

      // Prevent deletion of critical settings
      const criticalSettings = ['app_name', 'app_version', 'rates_update_interval'];
      if (criticalSettings.includes(key)) {
        throw new Error('Нельзя удалить критическую настройку');
      }

      systemSettings.splice(settingIndex, 1);
      saveSettings();
      
      console.log(`Setting ${key} deleted`);
      return { success: true };
    } catch (error) {
      console.error('Error deleting setting:', error);
      throw error;
    }
  },

  getSettingValue: (key: string): string | null => {
    const setting = systemSettings.find(s => s.key === key);
    return setting ? setting.value : null;
  },

  getSettingValueTyped: <T>(key: string, defaultValue: T): T => {
    const setting = systemSettings.find(s => s.key === key);
    if (!setting) return defaultValue;

    try {
      switch (setting.type) {
        case 'number':
          return parseFloat(setting.value) as T;
        case 'boolean':
          return (setting.value === 'true') as T;
        case 'json':
          return JSON.parse(setting.value) as T;
        default:
          return setting.value as T;
      }
    } catch {
      return defaultValue;
    }
  },

  resetToDefaults: async (): Promise<{ success: boolean }> => {
    try {
      systemSettings = [...defaultSettings];
      saveSettings();
      console.log('Settings reset to defaults');
      return { success: true };
    } catch (error) {
      console.error('Error resetting settings:', error);
      throw new Error('Ошибка сброса настроек');
    }
  },

  exportSettings: (): any => {
    return {
      settings: systemSettings,
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };
  },

  importSettings: async (settingsData: any): Promise<{ success: boolean; imported: number }> => {
    try {
      if (!settingsData.settings || !Array.isArray(settingsData.settings)) {
        throw new Error('Некорректный формат данных');
      }

      let importedCount = 0;
      for (const setting of settingsData.settings) {
        try {
          const validation = validateSettingValue(setting, setting.value);
          if (validation.isValid) {
            const existingIndex = systemSettings.findIndex(s => s.key === setting.key);
            if (existingIndex !== -1) {
              systemSettings[existingIndex] = {
                ...setting,
                updatedAt: new Date().toISOString(),
                updatedBy: 'import'
              };
            } else {
              systemSettings.push({
                ...setting,
                id: setting.id || `imported_${Date.now()}_${importedCount}`,
                updatedAt: new Date().toISOString(),
                updatedBy: 'import'
              });
            }
            importedCount++;
          }
        } catch (error) {
          console.warn(`Failed to import setting ${setting.key}:`, error);
        }
      }

      saveSettings();
      console.log(`Imported ${importedCount} settings`);
      return { success: true, imported: importedCount };
    } catch (error) {
      console.error('Error importing settings:', error);
      throw error;
    }
  }
};

// Global settings access for console debugging
(window as any).systemSettings = {
  get: (key: string) => settingsAPI.getSettingValue(key),
  getTyped: (key: string, defaultValue: any) => settingsAPI.getSettingValueTyped(key, defaultValue),
  export: () => settingsAPI.exportSettings(),
  reset: () => settingsAPI.resetToDefaults()
};

export default settingsAPI;
