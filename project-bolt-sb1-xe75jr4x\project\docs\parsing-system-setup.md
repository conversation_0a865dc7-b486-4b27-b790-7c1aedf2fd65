# Система автоматического парсинга курсов валют

## Обзор системы

Система автоматического парсинга предназначена для регулярного обновления курсов валют с сайтов обменников. Поддерживает парсинг валютных пар THB/RUB и USDT/BAHT с настраиваемыми CSS селекторами.

## Архитектура

### Компоненты системы:
1. **RateParserService** - основной сервис парсинга
2. **SchedulerService** - планировщик задач
3. **DatabaseService** - работа с базой данных
4. **Parsing API** - REST API для управления парсингом

### Технологии:
- **Backend**: Python 3.11+ с FastAPI
- **Парсинг**: aiohttp + BeautifulSoup4 + lxml
- **База данных**: PostgreSQL с asyncpg
- **Планировщик**: schedule + threading
- **Логирование**: Python logging

## Структура базы данных

### Таблица `exchangers`
```sql
- id (uuid) - уникальный идентификатор
- name (text) - название обменника
- website_url (text) - URL сайта для парсинга
- parsing_enabled (boolean) - включен ли парсинг
- parsing_config (jsonb) - конфигурация парсинга
- last_parsed_at (timestamp) - время последнего парсинга
```

### Таблица `exchange_rates`
```sql
- id (uuid) - уникальный идентификатор
- exchanger_id (uuid) - ссылка на обменник
- currency_pair (text) - валютная пара (THB/RUB, USDT/BAHT)
- buy_rate (decimal) - курс покупки
- sell_rate (decimal) - курс продажи
- source_url (text) - URL источника
- parsed_at (timestamp) - время парсинга
- is_active (boolean) - активность курса
```

### Таблица `parsing_logs`
```sql
- id (uuid) - уникальный идентификатор
- exchanger_id (uuid) - ссылка на обменник
- status (text) - статус (success, error, warning, skipped)
- message (text) - сообщение
- error_details (jsonb) - детали ошибки
- parsed_rates_count (integer) - количество обновленных курсов
- execution_time_ms (integer) - время выполнения
```

## Конфигурация парсинга

### Формат parsing_config:
```json
{
  "enabled": true,
  "selectors": {
    "thbRubBuy": ".rate-thb-rub .buy-rate",
    "thbRubSell": ".rate-thb-rub .sell-rate",
    "usdtBahtBuy": ".rate-usdt-baht .buy-rate",
    "usdtBahtSell": ".rate-usdt-baht .sell-rate"
  },
  "updateInterval": 60,
  "retryAttempts": 3,
  "timeout": 30
}
```

### Параметры:
- **selectors** - CSS селекторы для извлечения курсов
- **updateInterval** - интервал обновления в минутах (минимум 15)
- **retryAttempts** - количество попыток при ошибке (1-10)
- **timeout** - таймаут запроса в секундах (10-120)

## API Endpoints

### Парсинг курсов
- `POST /api/v1/parsing/parse/{exchanger_id}` - парсинг конкретного обменника
- `POST /api/v1/parsing/parse/all` - парсинг всех обменников
- `POST /api/v1/parsing/test-config` - тестирование конфигурации

### Получение данных
- `GET /api/v1/parsing/rates?exchanger_id={id}` - получение курсов
- `GET /api/v1/parsing/logs?exchanger_id={id}&limit={n}` - получение логов

### Управление планировщиком
- `GET /api/v1/parsing/scheduler/status` - статус планировщика
- `POST /api/v1/parsing/scheduler/start` - запуск планировщика
- `POST /api/v1/parsing/scheduler/stop` - остановка планировщика

## Расписание задач

### Автоматические задачи:
1. **Парсинг курсов** - каждые 30 минут
2. **Очистка логов** - ежедневно в 02:00
3. **Проверка состояния** - каждые 5 минут

### Настройка интервалов:
Интервалы можно настроить для каждого обменника индивидуально через поле `updateInterval` в конфигурации.

## Обработка ошибок

### Типы ошибок:
1. **Сетевые ошибки** - недоступность сайта, таймауты
2. **Ошибки парсинга** - изменение структуры сайта, неверные селекторы
3. **Ошибки данных** - некорректные значения курсов
4. **Ошибки базы данных** - проблемы с сохранением

### Механизм повторных попыток:
- Автоматические повторы при сетевых ошибках
- Экспоненциальная задержка между попытками
- Логирование всех попыток и ошибок

## Мониторинг и уведомления

### Логирование:
- Все операции парсинга логируются в таблицу `parsing_logs`
- Детальные логи в файловой системе через Python logging
- Метрики производительности (время выполнения, количество курсов)

### Уведомления об ошибках:
- Email уведомления при критических ошибках
- Telegram уведомления для оперативного реагирования
- Dashboard с визуализацией статистики парсинга

## Безопасность

### Меры безопасности:
1. **Rate limiting** - ограничение частоты запросов к сайтам
2. **User-Agent rotation** - ротация пользовательских агентов
3. **IP rotation** - использование прокси при необходимости
4. **Respect robots.txt** - соблюдение правил сайтов

### Защита от блокировок:
- Случайные задержки между запросами
- Мониторинг HTTP статусов ответов
- Автоматическое отключение при многократных ошибках

## Развертывание

### Требования:
- Python 3.11+
- PostgreSQL 14+
- Redis (для кэширования)
- Минимум 1GB RAM
- Стабильное интернет-соединение

### Переменные окружения:
```env
DATABASE_URL=postgresql://user:pass@localhost:5432/db
REDIS_URL=redis://localhost:6379
PARSING_ENABLED=true
PARSING_INTERVAL_MINUTES=30
MAX_CONCURRENT_PARSERS=5
```

### Команды запуска:
```bash
# Установка зависимостей
pip install -r requirements.txt

# Применение миграций
alembic upgrade head

# Запуск сервера
uvicorn main:app --host 0.0.0.0 --port 8000
```

## Мониторинг производительности

### Ключевые метрики:
- Время выполнения парсинга
- Процент успешных обновлений
- Количество ошибок по типам
- Частота обновления курсов

### Алерты:
- Более 50% ошибок парсинга за час
- Отсутствие обновлений более 2 часов
- Критические ошибки базы данных
- Превышение времени выполнения

## Масштабирование

### Горизонтальное масштабирование:
- Распределение обменников между инстансами
- Использование очередей задач (Celery/RQ)
- Кэширование результатов в Redis

### Оптимизация:
- Параллельный парсинг нескольких сайтов
- Кэширование HTML страниц
- Оптимизация SQL запросов
- Использование индексов базы данных