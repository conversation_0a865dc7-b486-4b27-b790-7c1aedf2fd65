# Руководство по тестированию стабильности веб-приложения

## 🧪 ПРОТОКОЛ ТЕСТИРОВАНИЯ СТАБИЛЬНОСТИ

### 1. **Базовые тесты стабильности (15 минут)**

#### Тест 1.1: Длительная работа приложения
```bash
# Цель: Проверить отсутствие memory leaks и деградации производительности
# Время: 10 минут непрерывной работы

1. Откройте приложение
2. Откройте DevTools → Performance → Memory
3. Начните запись
4. Выполняйте обычные действия пользователя:
   - Переключение между вкладками
   - Поиск обменников
   - Просмотр отзывов
   - Работа с административной панелью
5. Через 10 минут остановите запись
6. Проверьте график памяти на наличие утечек
```

**Критерии успеха:**
- ✅ Память не растет постоянно
- ✅ Нет резких скачков использования CPU
- ✅ Приложение отзывчиво через 10 минут работы

#### Тест 1.2: Стресс-тест форм
```bash
# Цель: Проверить обработку форм на предмет перезагрузок

1. Найдите все формы в приложении:
   - Форма поиска в Header
   - Форма добавления отзыва
   - Формы в административной панели
   
2. Для каждой формы:
   - Заполните данные
   - Нажмите Enter вместо кнопки отправки
   - Нажмите кнопку отправки несколько раз подряд
   - Отправьте пустую форму
   - Отправьте форму с некорректными данными

3. Проверьте консоль на ошибки
4. Убедитесь, что страница не перезагружается
```

**Критерии успеха:**
- ✅ Нет перезагрузок при отправке форм
- ✅ Корректная обработка ошибок валидации
- ✅ Нет JavaScript ошибок в консоли

#### Тест 1.3: Тест навигации
```bash
# Цель: Проверить роутинг на циклические редиректы

1. Протестируйте все маршруты:
   - / (главная)
   - /auth (авторизация)
   - /register (регистрация)
   - /admin (админка)
   - /nonexistent (несуществующий маршрут)

2. Для каждого маршрута:
   - Перейдите по прямой ссылке
   - Используйте кнопки навигации
   - Используйте кнопку "Назад" браузера
   - Обновите страницу (F5)

3. Проверьте Network вкладку на лишние запросы
```

**Критерии успеха:**
- ✅ Все маршруты загружаются корректно
- ✅ Нет бесконечных редиректов
- ✅ Кнопка "Назад" работает правильно

### 2. **Продвинутые тесты (30 минут)**

#### Тест 2.1: Тест React компонентов
```javascript
// Добавьте в консоль браузера для мониторинга ре-рендеров
let renderCount = 0;
const originalRender = React.createElement;

React.createElement = function(...args) {
  renderCount++;
  if (renderCount % 100 === 0) {
    console.log(`Render count: ${renderCount}`);
  }
  return originalRender.apply(this, args);
};

// Через 5 минут проверьте счетчик
setTimeout(() => {
  console.log(`Total renders in 5 minutes: ${renderCount}`);
  if (renderCount > 10000) {
    console.warn('Excessive renders detected!');
  }
}, 5 * 60 * 1000);
```

#### Тест 2.2: Тест API интеграций
```bash
# Цель: Проверить обработку ошибок API

1. Откройте DevTools → Network
2. Включите "Offline" режим
3. Попробуйте выполнить действия, требующие API:
   - Загрузка обменников
   - Отправка отзыва
   - Вход в админку
4. Включите сеть обратно
5. Проверьте восстановление функциональности
```

#### Тест 2.3: Тест производительности
```bash
# Цель: Выявить узкие места производительности

1. DevTools → Performance
2. Запустите запись
3. Выполните типичный пользовательский сценарий:
   - Загрузка главной страницы
   - Поиск обменника
   - Просмотр деталей
   - Переход в админку
4. Остановите запись
5. Анализируйте:
   - Время загрузки компонентов
   - Длительные задачи (>50ms)
   - Блокирующие операции
```

### 3. **Автоматизированные тесты**

#### Cypress E2E тесты
```javascript
// cypress/e2e/stability.cy.js
describe('Page Stability Tests', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('should not reload during normal usage', () => {
    // Мониторинг перезагрузок
    let reloadCount = 0;
    
    cy.window().then((win) => {
      win.addEventListener('beforeunload', () => {
        reloadCount++;
      });
    });

    // Выполнение действий пользователя
    cy.get('[data-testid="search-input"]').type('обменник');
    cy.get('[data-testid="district-filter"]').select('patong');
    cy.get('[data-testid="currency-filter"]').select('THB/RUB');
    
    // Ожидание и проверка
    cy.wait(5000);
    cy.then(() => {
      expect(reloadCount).to.equal(0);
    });
  });

  it('should handle form submissions without reload', () => {
    cy.get('[data-testid="review-form-trigger"]').click();
    cy.get('[data-testid="review-author"]').type('Тестовый пользователь');
    cy.get('[data-testid="review-comment"]').type('Тестовый отзыв');
    
    // Отправка формы
    cy.get('[data-testid="review-submit"]').click();
    
    // Проверка, что страница не перезагрузилась
    cy.url().should('include', '/');
    cy.get('[data-testid="review-form"]').should('not.exist');
  });

  it('should handle admin panel navigation', () => {
    cy.visit('/admin');
    
    // Переключение между вкладками админки
    const tabs = ['dashboard', 'reviews', 'exchangers', 'users'];
    
    tabs.forEach(tab => {
      cy.get(`[data-testid="admin-tab-${tab}"]`).click();
      cy.wait(1000);
      cy.url().should('include', '/admin');
    });
  });
});
```

#### Jest Unit тесты
```javascript
// src/components/__tests__/AdminPanel.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AdminPanel from '../AdminPanel';

describe('AdminPanel Stability', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
  });

  it('should not cause infinite re-renders', async () => {
    const renderSpy = jest.fn();
    
    const TestComponent = () => {
      renderSpy();
      return <AdminPanel onLogout={() => {}} />;
    };

    render(
      <QueryClientProvider client={queryClient}>
        <TestComponent />
      </QueryClientProvider>
    );

    // Ожидание стабилизации
    await waitFor(() => {
      expect(renderSpy).toHaveBeenCalledTimes(1);
    }, { timeout: 5000 });

    // Дополнительная проверка через 2 секунды
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Количество рендеров не должно значительно увеличиться
    expect(renderSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle data loading errors gracefully', async () => {
    // Мокаем API для возврата ошибки
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <QueryClientProvider client={queryClient}>
        <AdminPanel onLogout={() => {}} />
      </QueryClientProvider>
    );

    // Компонент должен отобразиться даже при ошибках API
    await waitFor(() => {
      expect(screen.getByText(/Административная панель/)).toBeInTheDocument();
    });
  });
});
```

---

## 🔍 ДИАГНОСТИЧЕСКИЕ СКРИПТЫ

### Скрипт 1: Автоматическая диагностика
```javascript
// Вставьте в консоль браузера для быстрой диагностики
(function() {
  console.log('🔍 Starting automatic diagnostics...');
  
  // Проверка ошибок
  let errorCount = 0;
  const originalError = console.error;
  console.error = function(...args) {
    errorCount++;
    originalError.apply(console, args);
  };
  
  // Проверка перезагрузок
  let reloadDetected = false;
  window.addEventListener('beforeunload', () => {
    reloadDetected = true;
    console.warn('Page reload detected during diagnostics!');
  });
  
  // Проверка производительности
  const startTime = performance.now();
  
  setTimeout(() => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log('📊 Diagnostic Results:');
    console.log(`- Errors detected: ${errorCount}`);
    console.log(`- Page reloads: ${reloadDetected ? 'YES' : 'NO'}`);
    console.log(`- Test duration: ${duration.toFixed(2)}ms`);
    console.log(`- Memory usage: ${(performance as any).memory?.usedJSHeapSize || 'N/A'}`);
    
    if (errorCount > 0) {
      console.warn('⚠️ Errors detected - check console for details');
    }
    
    if (reloadDetected) {
      console.error('🚨 Page reload detected - investigate cause');
    }
    
    if (errorCount === 0 && !reloadDetected) {
      console.log('✅ No stability issues detected');
    }
  }, 10000); // 10 секунд диагностики
})();
```

### Скрипт 2: Мониторинг React ре-рендеров
```javascript
// Мониторинг чрезмерных ре-рендеров
(function() {
  if (typeof React === 'undefined') {
    console.warn('React not found - skipping render monitoring');
    return;
  }
  
  let componentRenders = new Map();
  
  const originalUseState = React.useState;
  React.useState = function(initialState) {
    const result = originalUseState(initialState);
    
    // Отслеживание вызовов setState
    const [state, setState] = result;
    const wrappedSetState = function(newState) {
      const componentName = new Error().stack?.split('\n')[2]?.trim() || 'Unknown';
      const count = componentRenders.get(componentName) || 0;
      componentRenders.set(componentName, count + 1);
      
      if (count > 100) {
        console.warn(`Excessive renders in component: ${componentName} (${count} renders)`);
      }
      
      return setState(newState);
    };
    
    return [state, wrappedSetState];
  };
  
  // Отчет через 30 секунд
  setTimeout(() => {
    console.log('📈 Render Report:');
    componentRenders.forEach((count, component) => {
      if (count > 50) {
        console.warn(`High render count: ${component} - ${count} renders`);
      }
    });
  }, 30000);
})();
```

---

## 📋 ЧЕКЛИСТ ФИНАЛЬНОЙ ПРОВЕРКИ

### Перед деплоем:
- [ ] Все формы имеют `e.preventDefault()`
- [ ] useEffect имеют правильные зависимости
- [ ] Нет бесконечных циклов в React Query
- [ ] Error Boundaries установлены на критических компонентах
- [ ] Event listeners правильно очищаются
- [ ] Нет глобальных переменных, которые могут конфликтовать
- [ ] Все async операции имеют обработку ошибок
- [ ] React Strict Mode не показывает предупреждений

### После деплоя:
- [ ] Мониторинг ошибок настроен (Sentry/LogRocket)
- [ ] Алерты на критические проблемы
- [ ] Регулярные проверки производительности
- [ ] Backup план для быстрого отката

---

## 🚨 ЭКСТРЕННЫЕ ДЕЙСТВИЯ

### При обнаружении проблемы в продакшне:

1. **Немедленно (0-5 минут):**
   ```bash
   # Откат к стабильной версии
   git checkout main
   git reset --hard <last-stable-commit>
   npm run build
   npm run deploy
   ```

2. **Краткосрочно (5-30 минут):**
   - Изолировать проблемный компонент
   - Добавить Error Boundary
   - Отключить автоматические обновления

3. **Среднесрочно (30 минут - 2 часа):**
   - Исправить корневую причину
   - Добавить тесты для предотвращения регрессии
   - Обновить мониторинг

4. **Долгосрочно (2+ часа):**
   - Провести полный аудит кода
   - Улучшить процессы тестирования
   - Обновить документацию

---

*Этот протокол обеспечивает комплексный подход к тестированию и поддержанию стабильности веб-приложения.*