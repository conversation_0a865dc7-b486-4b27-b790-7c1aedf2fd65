import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Filter, Grid, List } from 'lucide-react';
import { districts } from '../data/mockData';
import { useExchangers } from '../hooks/useExchangeData';
import { Exchanger } from '../types';
import ExchangerList from './ExchangerList';
import ExchangerTable from './ExchangerTable';
import CitySelector from './CitySelector';
import TrustBadge from './TrustBadge';
import CurrencyConverter from './CurrencyConverter';

interface ExchangeRatesProps {
  searchQuery: string;
  onViewReviews?: (exchangerId: number) => void;
  onViewReviews?: (exchangerId: number) => void;
}

const ExchangeRates: React.FC<ExchangeRatesProps> = ({ searchQuery, onViewReviews }) => {
  const [selectedCurrency, setSelectedCurrency] = useState('RUB');
  const [selectedDistrict, setSelectedDistrict] = useState('all');
  const [selectedCity, setSelectedCity] = useState('phuket');
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const [lastUpdate] = useState(new Date());
  const [showBestRates, setShowBestRates] = useState(false);
  const [realTimeCount, setRealTimeCount] = useState<number | null>(null);

  // Fetch exchangers from API
  const { 
    data: exchangersData, 
    isLoading, 
    error,
    refetch,
    dataUpdatedAt
  } = useExchangers({
    district: selectedDistrict === 'all' ? undefined : selectedDistrict,
    search: searchQuery || undefined,
    limit: 50,
    forceRefresh: false
  });

  // Get real-time count of exchangers
  useEffect(() => {
    const updateRealTimeCount = async () => {
      try {
        const { getActiveExchangersCount } = await import('../data/adminData');
        const count = getActiveExchangersCount();
        setRealTimeCount(count);
      } catch (error) {
        console.error('Error getting real-time count:', error);
        setRealTimeCount(null);
      }
    };
    
    updateRealTimeCount();
    
    // Update count when data changes
    const handleDataUpdate = () => {
      updateRealTimeCount();
    };
    
    window.addEventListener('exchangerDataUpdated', handleDataUpdate);
    
    return () => {
      window.removeEventListener('exchangerDataUpdated', handleDataUpdate);
    };
  }, []);
  // Force refresh data when component mounts or when coming from admin
  useEffect(() => {
    const shouldRefresh = sessionStorage.getItem('refreshExchangers');
    if (shouldRefresh) {
      sessionStorage.removeItem('refreshExchangers');
      refetch();
    }

    // Listen for real-time updates from admin panel
    const handleDataUpdate = (event: CustomEvent) => {
      console.log('Main page received data update event:', event.detail);
      
      // Show user-friendly notification about the update
      if (event.detail.source === 'manual_edit') {
        // Could show a toast notification here
        console.log('Курсы валют обновлены администратором');
      } else if (event.detail.source === 'excel_upload') {
        console.log('Курсы валют обновлены из Excel файла');
      } else if (event.detail.source === 'ai_parsing') {
        console.log('Курсы валют обновлены через ИИ парсинг');
      }
      
      refetch();
    };

    window.addEventListener('exchangerDataUpdated', handleDataUpdate as EventListener);
    
    return () => {
      window.removeEventListener('exchangerDataUpdated', handleDataUpdate as EventListener);
    };
  }, [refetch]);

  // Auto-refresh every 30 seconds to catch any updates
  useEffect(() => {
    const interval = setInterval(() => {
      const shouldRefresh = sessionStorage.getItem('refreshExchangers');
      if (shouldRefresh) {
        sessionStorage.removeItem('refreshExchangers');
        refetch();
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [refetch]);

  const exchangers = exchangersData?.exchangers || [];

  const currencies = ['RUB', 'USD', 'USDT', 'THB/RUB', 'THB/USDT', 'USDT/BAHT'];

  const filteredExchangers = exchangers.filter(exchanger => {
    const matchesSearch = exchanger.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         exchanger.address.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesDistrict = selectedDistrict === 'all' || exchanger.district === selectedDistrict;
    return matchesSearch && matchesDistrict;
  });

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <CitySelector selectedCity={selectedCity} onCityChange={setSelectedCity} />
        <div className="text-center py-12 bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div className="text-gray-600 text-lg">Загрузка актуальных курсов валют...</div>
          <p className="text-gray-400 mt-2">Получаем данные от обменников</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-6">
        <CitySelector selectedCity={selectedCity} onCityChange={setSelectedCity} />
        <div className="text-center py-12 bg-white rounded-xl shadow-lg border border-red-100">
          <div className="text-red-600 text-lg mb-4">Ошибка загрузки данных</div>
          <p className="text-gray-600 mb-4">Не удалось получить актуальные курсы валют</p>
          <button
            onClick={() => refetch()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Попробовать снова
          </button>
        </div>
      </div>
    );
  }

  // Найти лучшие курсы для каждой валюты
  const getBestRates = (currency: string) => {
    const rates = filteredExchangers
      .map(e => e.rates?.find(r => r.currency === currency))
      .filter(Boolean);
    
    if (rates.length === 0) {
      return { bestBuy: 0, bestSell: 0 };
    }
    
    const bestBuy = Math.max(...rates.map(r => r!.buy));
    const bestSell = Math.min(...rates.map(r => r!.sell));
    return { bestBuy, bestSell };
  };

  const getRateForCurrency = (exchanger: Exchanger, currency: string) => {
    return exchanger.rates?.find(rate => rate.currency === currency);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ru-RU', { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getCurrencyPairInfo = (currency: string) => {
    switch (currency) {
      case 'THB/RUB':
        return { 
          name: 'Тайский бат → Рубли', 
          description: '1 THB = ? RUB',
          icon: '🇹🇭→🇷🇺',
          popular: true
        };
      case 'USDT/RUB':
        return { 
          name: 'USDT → Тайские баты', 
          description: '1 USDT = ? THB',
          icon: '₮→🇹🇭',
          popular: true
        };
      case 'USDT/BAHT':
        return { 
          name: 'USDT → Тайские баты', 
          description: '1 USDT = ? THB',
          icon: '₮→🇹🇭',
          popular: true
        };
      case 'RUB':
        return { 
          name: 'Российский рубль', 
          description: 'Основная валюта',
          icon: '🇷🇺',
          popular: false
        };
      case 'USD':
        return { 
          name: 'Доллар США', 
          description: 'Американская валюта',
          icon: '🇺🇸',
          popular: false
        };
      case 'USDT':
        return { 
          name: 'Tether USDT', 
          description: 'Стейблкоин',
          icon: '₮',
          popular: false
        };
      default:
        return { 
          name: currency, 
          description: '',
          icon: '💱',
          popular: false
        };
    }
  };

  return (
    <div className="space-y-6">
      {/* City Selector */}
      <CitySelector selectedCity={selectedCity} onCityChange={setSelectedCity} />
      
      {/* Trust Badge */}
      <TrustBadge />
      
      {/* Quick Rate Overview - Новый блок */}
      <div className="bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 rounded-2xl border-2 border-emerald-200 overflow-hidden">
        <div className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold mb-2">🔥 Лучшие курсы сегодня</h3>
              <p className="text-emerald-100">Самые выгодные предложения от проверенных обменников</p>
            </div>
            <div className="text-right">
              <div className="text-sm text-emerald-200">Обновлено</div>
              <div className="text-lg font-bold">{formatTime(lastUpdate)}</div>
            </div>
          </div>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* THB/RUB */}
            <div className="bg-white rounded-xl p-4 border-2 border-teal-200 shadow-lg hover:shadow-xl hover:border-teal-300 transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="text-3xl">🇹🇭→🇷🇺</div>
                  <div>
                    <h4 className="text-xl font-bold text-slate-800">THB → RUB</h4>
                    <p className="text-sm text-slate-600">Тайский бат за рубли</p>
                  </div>
                </div>
                <div className="bg-gradient-to-r from-orange-400 to-red-400 text-white px-3 py-1 rounded-full text-xs font-bold">
                  ХИТ
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center bg-emerald-50 rounded-lg p-3 border border-emerald-100">
                  <div className="text-xs text-emerald-700 font-medium mb-1">Лучшая покупка</div>
                  <div className="text-2xl font-bold text-emerald-800">{getBestRates('THB/RUB').bestBuy.toFixed(3)}</div>
                </div>
                <div className="text-center bg-rose-50 rounded-lg p-3 border border-rose-100">
                  <div className="text-xs text-rose-700 font-medium mb-1">Лучшая продажа</div>
                  <div className="text-2xl font-bold text-rose-800">{getBestRates('THB/RUB').bestSell.toFixed(3)}</div>
                </div>
              </div>
              
              <button 
                onClick={() => setSelectedCurrency('THB/RUB')}
                className="w-full bg-gradient-to-r from-teal-500 to-cyan-500 hover:from-teal-600 hover:to-cyan-600 text-white py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl"
                title="Показать все обменники с курсом THB/RUB"
              >
                Найти лучший курс THB
              </button>
            </div>
            
            {/* USDT/RUB */}
            {/* USDT/BAHT */}
            <div className="bg-white rounded-xl p-4 border-2 border-indigo-200 shadow-lg hover:shadow-xl hover:border-indigo-300 transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="text-3xl">₮→🇹🇭</div>
                  <div>
                    <h4 className="text-xl font-bold text-slate-800">USDT → BAHT</h4>
                    <p className="text-sm text-slate-600">Tether за тайские баты</p>
                  </div>
                </div>
                <div className="bg-gradient-to-r from-indigo-400 to-purple-400 text-white px-3 py-1 rounded-full text-xs font-bold">
                  ПОПУЛЯРНО
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center bg-emerald-50 rounded-lg p-3 border border-emerald-100">
                  <div className="text-xs text-emerald-700 font-medium mb-1">Лучшая покупка</div>
                  <div className="text-2xl font-bold text-emerald-800">{getBestRates('USDT/BAHT').bestBuy.toFixed(2)}</div>
                </div>
                <div className="text-center bg-rose-50 rounded-lg p-3 border border-rose-100">
                  <div className="text-xs text-rose-700 font-medium mb-1">Лучшая продажа</div>
                  <div className="text-2xl font-bold text-rose-800">{getBestRates('USDT/BAHT').bestSell.toFixed(2)}</div>
                </div>
              </div>
              
              <button 
                onClick={() => setSelectedCurrency('USDT/BAHT')}
                className="w-full bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl"
                title="Показать все обменники с курсом USDT/BAHT"
              >
                Найти лучший курс USDT
              </button>
            </div>
          </div>
          
          {/* Дополнительная информация */}
          <div className="mt-6 bg-gradient-to-r from-slate-50 to-teal-50 rounded-xl p-4 border border-slate-200">
            <div className="flex items-center justify-center space-x-8 text-sm text-slate-700">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                <span>Лучшие курсы покупки</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-rose-500 rounded-full"></div>
                <span>Лучшие курсы продажи</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-teal-500 rounded-full animate-pulse"></div>
                <span>Обновляется каждые 5 минут</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Currency Converter Calculator */}
      <CurrencyConverter exchangers={filteredExchangers} />
      
      {/* Header */}
      <div className="bg-gradient-to-r from-slate-50 to-blue-50 p-6 rounded-xl border border-slate-200">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div>
            <h2 className="text-2xl font-bold text-slate-800 mb-2">Актуальные курсы валют</h2>
            <p className="text-slate-600">
              Последнее обновление: {formatTime(lastUpdate)}
              <span className="ml-4 text-sm bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full">
                ✓ {realTimeCount !== null ? `${realTimeCount} обменников` : 'Все курсы проверены'}
              </span>
              {dataUpdatedAt && (
                <span className="ml-2 text-xs text-slate-500">
                  Данные: {new Date(dataUpdatedAt).toLocaleTimeString('ru-RU')}
                </span>
              )}
            </p>
            <p className="text-sm text-slate-500 mt-1">
              Сравните курсы {filteredExchangers.length} из {realTimeCount || 'всех'} проверенных обменников и выберите лучший
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => refetch()}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg text-sm flex items-center space-x-2 transition-colors"
            >
              <div className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`}>
                🔄
              </div>
              <span>{isLoading ? 'Обновление...' : 'Обновить'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-xl shadow-lg border border-slate-200">
        <div className="flex items-center space-x-4 mb-4">
          <Filter className="w-5 h-5 text-slate-500" />
          <h3 className="text-lg font-semibold text-slate-800">Фильтры и отображение</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Валюта</label>
            <select
              value={selectedCurrency}
              onChange={(e) => setSelectedCurrency(e.target.value)}
              className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors bg-white text-slate-700"
            >
              {currencies.map(currency => (
                <option key={currency} value={currency}>
                  {getCurrencyPairInfo(currency).popular && '🔥 '}
                  {getCurrencyPairInfo(currency).name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Район</label>
            <select
              value={selectedDistrict}
              onChange={(e) => setSelectedDistrict(e.target.value)}
              className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors bg-white text-slate-700"
            >
              <option value="all" className="text-slate-700">Все районы</option>
              {districts.map(district => (
                <option key={district.id} value={district.id}>{district.name}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Показать</label>
            <select
              value={showBestRates ? 'best' : 'all'}
              onChange={(e) => setShowBestRates(e.target.value === 'best')}
              className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors bg-white text-slate-700"
            >
              <option value="all" className="text-slate-700">Все обменники</option>
              <option value="best">🏆 Только лучшие курсы</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Вид</label>
            <select
              value={viewMode}
              onChange={(e) => setViewMode(e.target.value as 'table' | 'cards')}
              className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors bg-white text-slate-700"
            >
              <option value="table" className="text-slate-700">Таблица</option>
              <option value="cards">Карточки</option>
            </select>
          </div>
        </div>
      </div>

      {/* Content based on view mode */}
      {viewMode === 'table' ? (
        <ExchangerTable
          exchangers={filteredExchangers}
          selectedCurrency={selectedCurrency}
          onViewReviews={onViewReviews}
        />
      ) : (
        <ExchangerList
          exchangers={filteredExchangers}
          selectedCurrency={selectedCurrency}
          onViewReviews={onViewReviews}
        />
      )}

      {filteredExchangers.length === 0 && (
        <div className="text-center py-12 bg-white rounded-xl shadow-lg border border-slate-200">
          <div className="text-slate-500 text-lg">
            Обменники не найдены
          </div>
          <p className="text-slate-400 mt-2">
            Попробуйте изменить параметры поиска или фильтры
          </p>
        </div>
      )}
    </div>
  );
};

export default ExchangeRates;