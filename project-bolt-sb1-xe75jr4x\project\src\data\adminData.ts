import { allExchangers } from './mockData';
import { ExchangerFormData } from '../types/exchanger';
import { processIntelligentRates } from '../utils/rateProcessor';
import { networkSynchronizer } from '../utils/networkSynchronization';
import DataPersistenceDebugger from '../utils/dataPersistenceDebugger';
import settingsAPI from './settingsData';
import settingsIntegration from '../services/settingsIntegration';

// Convert exchanger data to admin format
const convertToAdminFormat = (exchanger: any) => ({
  id: exchanger?.id?.toString() || '',
  name: exchanger?.name || '',
  address: exchanger?.address || '',
  district: exchanger?.district || '',
  phone: exchanger?.phone || '',
  hours: exchanger?.hours || '',
  websiteUrl: exchanger?.website || '',
  parsingEnabled: false,
  parsingConfig: {
    enabled: false,
    selectors: {},
    updateInterval: 60,
    retryAttempts: 3,
    timeout: 30
  },
  coordinates: exchanger?.coordinates,
  status: 'active' as const,
  additionalOffices: [],
  lastParsedAt: undefined,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
});

// Initialize admin data from mock exchangers
let adminExchangers = (allExchangers || []).map(convertToAdminFormat);

// Load saved data from localStorage if available with enhanced recovery
const loadSavedData = () => {
  try {
    const savedData = localStorage.getItem('exchangerManagement');
    if (savedData) {
      const parsedData = JSON.parse(savedData);
      if (Array.isArray(parsedData) && parsedData.length > 0) {
        adminExchangers = parsedData;
        console.log('Loaded saved exchanger data:', {
          count: parsedData.length,
          activeCount: parsedData.filter(e => e && e.status === 'active' && !e.isDeleted).length,
          timestamp: new Date().toISOString()
        });
        return;
      }
    }

    // Try backup if main data failed
    const backupData = localStorage.getItem('exchangerManagement_backup');
    if (backupData) {
      const backup = JSON.parse(backupData);
      if (backup.data && Array.isArray(backup.data) && backup.data.length > 0) {
        adminExchangers = backup.data;
        console.log('Loaded backup exchanger data:', {
          count: backup.data.length,
          backupTimestamp: backup.timestamp,
          loadedAt: new Date().toISOString()
        });
        // Save the backup as main data
        saveData();
        return;
      }
    }

    console.log('No saved data found, using initial mock data');
  } catch (error) {
    console.error('Error loading saved exchanger data:', error);
    // Try to recover from backup
    try {
      const backupData = localStorage.getItem('exchangerManagement_backup');
      if (backupData) {
        const backup = JSON.parse(backupData);
        if (backup.data && Array.isArray(backup.data)) {
          adminExchangers = backup.data;
          console.log('Recovered from backup after error');
        }
      }
    } catch (backupError) {
      console.error('Backup recovery also failed:', backupError);
    }
  }
};

// Save data to localStorage with enhanced debugging
const saveData = () => {
  try {
    const dataToSave = JSON.stringify(adminExchangers);
    localStorage.setItem('exchangerManagement', dataToSave);

    // Also save a backup with timestamp
    localStorage.setItem('exchangerManagement_backup', JSON.stringify({
      data: adminExchangers,
      timestamp: new Date().toISOString(),
      count: adminExchangers.length
    }));

    console.log('Data saved to localStorage:', {
      exchangersCount: adminExchangers.length,
      activeCount: adminExchangers.filter(e => e && e.status === 'active' && !e.isDeleted).length,
      timestamp: new Date().toISOString(),
      dataSize: dataToSave.length
    });

    // Run integrity check after save
    const integrity = DataPersistenceDebugger.verifyDataIntegrity();
    if (!integrity.isValid) {
      console.error('Data integrity issues detected after save:', integrity.errors);
    }

    // Trigger update event for main page with more details
    window.dispatchEvent(new CustomEvent('exchangerDataUpdated', {
      detail: {
        timestamp: Date.now(),
        source: 'admin_save',
        exchangersCount: adminExchangers.length,
        activeCount: adminExchangers.filter(e => e && e.status === 'active' && !e.isDeleted).length
      }
    }));

    // Force refresh flag for main page
    sessionStorage.setItem('forceExchangerRefresh', 'true');

  } catch (error) {
    console.error('Error saving exchanger data:', error);
  }
};

// Export function to get current exchangers count for main page
export const getActiveExchangersCount = () => {
  return (adminExchangers || []).filter(e => e && e.status === 'active' && !e.isDeleted).length;
};

// Force reload data from localStorage (for debugging persistence issues)
export const forceReloadData = () => {
  console.log('Force reloading data from localStorage...');
  loadSavedData();
  return adminExchangers;
};

// Export function to sync main page data with admin data
export const getActiveExchangersForMainPage = () => {
  // Check if we need to force refresh
  const forceRefresh = sessionStorage.getItem('forceExchangerRefresh');
  if (forceRefresh) {
    console.log('Force refresh detected, reloading data...');
    loadSavedData();
    sessionStorage.removeItem('forceExchangerRefresh');
  }

  console.log('getActiveExchangersForMainPage called:', {
    totalAdminExchangers: (adminExchangers || []).length,
    activeCount: (adminExchangers || []).filter(e => e && e.status === 'active' && !e.isDeleted).length,
    timestamp: new Date().toISOString(),
    forceRefreshTriggered: !!forceRefresh
  });
  
  return (adminExchangers || [])
    .filter(e => e && e.status === 'active' && !e.isDeleted)
    .map(e => {
      // Find corresponding exchanger from allExchangers to get rates
      const originalExchanger = (allExchangers || []).find(orig => orig && orig.id && orig.id.toString() === e.id);
      if (originalExchanger) {
        // Process rates to ensure consistent format
        let finalRates = e.rates && e.rates.length > 0 ? e.rates : originalExchanger.rates || [];
        
        // Apply intelligent processing to ensure all rates are in correct format
        if (finalRates.length > 0) {
          finalRates = processIntelligentRates(finalRates);
        }
        
        const result = {
          ...originalExchanger,
          // Override with admin data if different
          name: e.name,
          address: e.address,
          district: e.district,
          city: e.city,
          phone: e.phone,
          hours: e.hours,
          website: e.websiteUrl,
          coordinates: e.coordinates,
          // Use processed rates with intelligent conversion
          rates: (finalRates || []).map((rate: any) => ({
            currency: rate.currency,
            buy: parseFloat(rate.buy) || 0,
            sell: parseFloat(rate.sell) || 0,
            change: parseFloat(rate.change) || 0,
            converted: rate.converted || false,
            lastUpdated: rate.lastUpdated || new Date().toISOString()
          })),
          rating: originalExchanger.rating || 4.5,
          reviewCount: originalExchanger.reviewCount || 0,
          lastUpdated: e.updatedAt,
          syncedAt: new Date().toISOString(),
          hasProcessedRates: (finalRates || []).some((r: any) => r.converted)
        };
        
        console.log(`Mapped exchanger ${e.name}:`, {
          id: result.id,
          rating: result.rating,
          reviewCount: result.reviewCount,
          ratesCount: (result.rates || []).length,
          hasProcessedRates: result.hasProcessedRates
        });
        
        return result;
      }
      console.warn(`No original exchanger found for admin exchanger ${e.id}: ${e.name}`);
      return null;
    })
    .filter(Boolean); // Remove any null/undefined entries
};

// Initialize data
loadSavedData();

// Mock admin API for development/fallback
export const adminAPI = {
  // Dashboard statistics
  getDashboardStats: async () => {
    try {
      // Get real data from current state
      const safeAdminExchangers = adminExchangers || [];
      const activeExchangers = safeAdminExchangers.filter(e => e && e.status === 'active' && !e.isDeleted);
      const inactiveExchangers = safeAdminExchangers.filter(e => e && e.status === 'inactive' && !e.isDeleted);
      const pendingExchangers = safeAdminExchangers.filter(e => e && e.status === 'pending' && !e.isDeleted);
      
      // Get real reviews data
      const pendingReviews = JSON.parse(localStorage.getItem('pendingReviews') || '[]');
      const approvedReviews = JSON.parse(localStorage.getItem('approvedReviews') || '[]');
      const rejectedReviews = JSON.parse(localStorage.getItem('rejectedReviews') || '[]');
      const totalReviews = pendingReviews.length + approvedReviews.length + rejectedReviews.length;
      
      // Get real users data
      const registeredUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
      const fallbackUsers = JSON.parse(localStorage.getItem('fallback_users') || '[]');
      const totalUsers = registeredUsers.length + fallbackUsers.length;
      
      // Calculate active users (users who logged in within last 30 days)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const activeUsers = [...registeredUsers, ...fallbackUsers].filter(user => {
        const lastLogin = user.lastLogin || user.createdAt;
        return lastLogin && new Date(lastLogin) > thirtyDaysAgo;
      }).length;
      
      // Calculate today's visits (realistic calculation based on exchangers)
      const todayVisits = Math.floor(activeExchangers.length * 15) + Math.floor(Math.random() * 100);
      
      // Calculate monthly growth (based on recent activity)
      const monthlyGrowth = Math.max(5, Math.floor(activeExchangers.length / 10));
      
      // Get top exchangers by rating with proper sorting
      const topExchangers = activeExchangers
        .map(e => {
          if (!e || !e.id) return null;
          // Find corresponding exchanger from allExchangers to get rating
          const safeAllExchangers = allExchangers || [];
          const originalExchanger = safeAllExchangers.find(orig => orig && orig.id && orig.id.toString() === e.id);
          return {
            id: parseInt(e.id),
            name: e.name,
            rating: originalExchanger?.rating || 4.0,
            reviewCount: originalExchanger?.reviewCount || Math.floor(Math.random() * 50) + 10
          };
        })
        .filter(Boolean) // Remove null entries
        .sort((a, b) => {
          // Sort by rating first, then by review count
          const ratingDiff = (b?.rating || 0) - (a?.rating || 0);
          if (ratingDiff !== 0) return ratingDiff;
          return (b?.reviewCount || 0) - (a?.reviewCount || 0);
        })
        .slice(0, 5);
      
      const recentActivity = await adminAPI.getActivityLogs(1, 5);
      
      const stats = {
        totalExchangers: safeAdminExchangers.filter(e => e && !e.isDeleted).length,
        activeExchangers: activeExchangers.length,
        inactiveExchangers: inactiveExchangers.length,
        pendingExchangers: pendingExchangers.length,
        totalUsers: totalUsers,
        activeUsers: activeUsers,
        totalReviews: totalReviews,
        pendingReviews: pendingReviews.length,
        approvedReviews: approvedReviews.length,
        rejectedReviews: rejectedReviews.length,
        todayVisits: todayVisits,
        monthlyGrowth: monthlyGrowth,
        totalRates: activeExchangers.reduce((sum, e) => {
          const rates = (e && e.rates) || [];
          return sum + rates.length;
        }, 0),
        topExchangers: topExchangers,
        lastUpdate: new Date().toISOString(),
        recentActivity: recentActivity?.logs || [],
        dataSource: 'real_time',
        calculatedAt: new Date().toISOString()
      };
      
      console.log('Dashboard stats calculated:', stats);
      return stats;
      
    } catch (error) {
      console.error('Error calculating dashboard stats:', error);
      
      // Emergency fallback
      const safeAdminExchangers = adminExchangers || [];
      return {
        totalExchangers: safeAdminExchangers.length,
        activeExchangers: safeAdminExchangers.length,
        inactiveExchangers: 0,
        pendingExchangers: 0,
        totalUsers: 0,
        activeUsers: 0,
        totalReviews: 0,
        pendingReviews: 0,
        approvedReviews: 0,
        rejectedReviews: 0,
        todayVisits: 0,
        monthlyGrowth: 0,
        totalRates: 0,
        topExchangers: [],
        lastUpdate: new Date().toISOString(),
        recentActivity: [],
        dataSource: 'emergency_fallback',
        error: 'Failed to calculate real stats'
      };
    }
  },
    

  // User management - now uses real data from adminUserService
  getUsers: async (page = 1, limit = 10, searchQuery = '', roleFilter = 'all', statusFilter = 'all') => {
    try {
      // Import adminUserService dynamically to avoid circular dependencies
      const { adminUserService } = await import('../services/adminUserService');

      const filters = {
        page,
        limit,
        search: searchQuery || undefined,
        role: roleFilter !== 'all' ? roleFilter : undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined
      };

      const response = await adminUserService.getUsers(filters);

      // Convert AdminUser format to legacy User format for compatibility
      const convertedUsers = response.users.map(user => ({
        id: user.id,
        username: user.username || `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email.split('@')[0],
        email: user.email,
        role: user.role,
        status: user.isActive ? 'active' : 'inactive',
        permissions: user.role === 'admin' ? ['*'] : user.role === 'moderator' ? ['reviews.moderate', 'users.view'] : ['basic'],
        createdAt: user.createdAt,
        lastLogin: user.lastLogin || null
      }));

      return {
        users: convertedUsers,
        totalPages: response.totalPages,
        totalCount: response.totalCount
      };
    } catch (error) {
      console.error('Error getting users from adminUserService:', error);

      // Fallback to mock data if service fails
      const mockUsers = [
        {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          permissions: ['*'],
          createdAt: new Date().toISOString(),
          lastLogin: new Date().toISOString()
        },
      {
        id: 3,
        username: 'user1',
        email: '<EMAIL>',
        role: 'user',
        status: 'active',
        permissions: ['reviews.create'],
        createdAt: new Date().toISOString(),
        lastLogin: new Date(Date.now() - 172800000).toISOString()
      },
      {
        id: 4,
        username: 'user2',
        email: '<EMAIL>',
        role: 'user',
        status: 'inactive',
        permissions: ['reviews.create'],
        createdAt: new Date().toISOString(),
        lastLogin: null
      }
    ];
    
    // Apply filters
    let filteredUsers = mockUsers.filter(user => {
      const matchesSearch = searchQuery === '' || 
        user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesRole = roleFilter === 'all' || user.role === roleFilter;
      const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
      
      return matchesSearch && matchesRole && matchesStatus;
    });
    
    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    
    return {
      users: paginatedUsers,
      totalPages: Math.ceil(filteredUsers.length / limit),
      totalCount: filteredUsers.length
    };
    }
  },
  createUser: async (userData: any) => ({ id: crypto.randomUUID(), ...userData }),
  updateUser: async (id: string, userData: any) => ({ id, ...userData }),
  deleteUser: async (id: string) => ({ success: true }),

  // System settings - Fixed implementation
  getSettings: async (category?: string) => {
    try {
      console.log('adminAPI.getSettings called:', { category });
      return await settingsAPI.getSettings(category);
    } catch (error) {
      console.error('Error in adminAPI.getSettings:', error);
      throw error;
    }
  },

  updateSetting: async (key: string, value: string) => {
    try {
      console.log('adminAPI.updateSetting called:', { key, value });

      // Use integration service for real-time application
      await settingsIntegration.updateSetting(key, value);

      return await settingsAPI.getSettings().then(settings =>
        settings.find(s => s.key === key)
      );
    } catch (error) {
      console.error('Error in adminAPI.updateSetting:', error);
      throw error;
    }
  },

  createSetting: async (settingData: any) => {
    try {
      console.log('adminAPI.createSetting called:', settingData);
      return await settingsAPI.createSetting(settingData);
    } catch (error) {
      console.error('Error in adminAPI.createSetting:', error);
      throw error;
    }
  },

  deleteSetting: async (key: string) => {
    try {
      console.log('adminAPI.deleteSetting called:', { key });
      return await settingsAPI.deleteSetting(key);
    } catch (error) {
      console.error('Error in adminAPI.deleteSetting:', error);
      throw error;
    }
  },

  getSystemSettings: async () => {
    // Legacy function - redirect to new implementation
    return await settingsAPI.getSettings();
  },

  updateSystemSettings: async (settings: any) => {
    // Legacy function - redirect to new implementation
    if (settings.key && settings.value !== undefined) {
      return await settingsAPI.updateSetting(settings.key, settings.value);
    }
    return settings;
  },

  // Exchanger management
  getExchangers: async (page = 1, limit = 20, statusFilter = 'all') => {
    // Filter exchangers
    let filteredExchangers = (adminExchangers || []).filter(e => e && !e.isDeleted);
    
    if (statusFilter !== 'all') {
      filteredExchangers = filteredExchangers.filter(e => e && e.status === statusFilter);
    }
    
    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedExchangers = filteredExchangers.slice(startIndex, endIndex);
    
    return {
      exchangers: paginatedExchangers,
      totalPages: Math.ceil(filteredExchangers.length / limit),
      totalCount: filteredExchangers.length
    };
  },
  
  createExchanger: async (exchangerData: any) => {
    const newId = Math.max(...(adminExchangers || []).map(e => parseInt(e?.id) || 0), 0) + 1;
    const newExchanger = {
      id: newId.toString(),
      ...exchangerData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isDeleted: false
    };
    
    adminExchangers.push(newExchanger);
    saveData();
    // Force refresh main page data
    sessionStorage.setItem('refreshExchangers', 'true');
    
    return newExchanger;
  },
   
  updateExchanger: async (id: string, exchangerData: any) => {
    console.log('updateExchanger called:', { id, exchangerData });

    const index = (adminExchangers || []).findIndex(e => e && e.id === id.toString());
    if (index === -1) {
      console.error(`Exchanger with id ${id} not found`);
      throw new Error('Обменник не найден');
    }

    const originalExchanger = { ...adminExchangers[index] };

    // Process and validate rates before updating
    const existingRates = adminExchangers[index].rates;
    let processedRates = exchangerData.rates || existingRates;

    // Apply intelligent rate processing if rates are provided
    if (exchangerData.rates && Array.isArray(exchangerData.rates)) {
      console.log(`Processing ${exchangerData.rates.length} rates for ${adminExchangers[index].name}`);
      processedRates = processIntelligentRates(exchangerData.rates);
      console.log(`Processed to ${processedRates.length} rates`);
    }

      // Update the exchanger
      const updatedExchanger = {
        ...adminExchangers[index],
        ...exchangerData,
        rates: processedRates,
        updatedAt: new Date().toISOString()
      };

      adminExchangers[index] = updatedExchanger;

      console.log('Exchanger updated:', {
        id: id,
        name: updatedExchanger.name,
        ratesCount: processedRates?.length || 0,
        hasRates: !!(processedRates && processedRates.length > 0),
        syncedFrom: exchangerData.syncedFrom || null
      });

      // Network synchronization - only if rates were updated and not already synced
      const shouldSync = processedRates &&
                        processedRates.length > 0 &&
                        !exchangerData.syncedFrom; // Don't sync if this update came from sync

      if (shouldSync) {
        try {
          const syncResult = await networkSynchronizer.synchronizeNetworkRates(
            adminExchangers[index],
            processedRates,
            adminExchangers || [],
            async (targetId: string, syncData: any) => {
              // Recursive call but with syncedFrom flag to prevent infinite loop
              return adminAPI.updateExchanger(targetId, { ...syncData, syncedFrom: id });
            }
          );

          if (syncResult.success && syncResult.syncedCount > 0) {
            console.log(`Network sync completed: ${syncResult.syncedCount} exchangers updated`);
          }

          if (syncResult.errors.length > 0) {
            console.warn('Network sync errors:', syncResult.errors);
          }
        } catch (syncError) {
          console.error('Network synchronization failed:', syncError);
          // Don't throw - the main update should still succeed
        }
      }

      // Save data with enhanced persistence
      try {
        saveData();
        console.log('Data saved successfully after exchanger update');
      } catch (saveError) {
        console.error('Error saving data after exchanger update:', saveError);
        // Restore original data if save failed
        adminExchangers[index] = originalExchanger;
        throw new Error('Ошибка сохранения данных');
      }

      // Force refresh main page data
      sessionStorage.setItem('refreshExchangers', 'true');

      // Enhanced logging with rate update details
      if (processedRates && processedRates.length > 0) {
        console.log(`Rates updated for ${adminExchangers[index].name}:`, {
          exchangerId: id,
          ratesCount: processedRates.length,
          timestamp: new Date().toISOString(),
          rates: processedRates.map(r => ({ currency: r.currency, buy: r.buy, sell: r.sell })),
          hasConvertedRates: processedRates.some((r: any) => r.converted),
          syncedFrom: exchangerData.syncedFrom || null,
          updateSource: 'manual_admin_edit'
        });
      }

      console.log('updateExchanger completed successfully for:', adminExchangers[index].name);
      return adminExchangers[index];
  },
  
  softDeleteExchanger: async (id: number) => {
    const index = (adminExchangers || []).findIndex(e => e && e.id === id.toString());
    if (index !== -1) {
      adminExchangers[index].isDeleted = true;
      adminExchangers[index].deletedAt = new Date().toISOString();
      saveData();
      return { success: true };
    }
    throw new Error('Обменник не найден');
  },
  
  deleteExchanger: async (id: string) => {
    const index = (adminExchangers || []).findIndex(e => e && e.id === id.toString());
    if (index !== -1) {
      adminExchangers.splice(index, 1);
      saveData();
      return { success: true };
    }
    throw new Error('Обменник не найден');
  },

  // Activity logs
  getActivityLogs: async (page = 1, limit = 10) => {
    // Generate realistic activity logs based on actual data
    const logs = [];
    const now = Date.now();
    const logKey = `activity_logs_${Math.floor(now / 60000)}`; // Group by 1-minute intervals (more frequent updates)

    // Check if we've already generated logs for this time period (but allow refresh)
    const cachedLogs = sessionStorage.getItem(logKey);
    if (cachedLogs && Math.random() > 0.3) { // 30% chance to refresh even with cache
      const parsed = JSON.parse(cachedLogs);
      // Add timestamp to show when logs were generated
      parsed.generatedAt = new Date().toISOString();
      return parsed;
    }
    
    // Add recent exchanger updates
    const recentExchangers = (adminExchangers || [])
      .filter(e => e.updatedAt)
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, 3);
    
    recentExchangers.forEach((exchanger, index) => {
      logs.push({
        id: `exchanger-${exchanger.id}-${index}`,
        action: 'exchanger_update',
        description: `Обменник "${exchanger.name}" обновлен`,
        user: 'admin',
        timestamp: exchanger.updatedAt,
        details: { 
          exchangerId: exchanger.id, 
          name: exchanger.name,
          hasRates: !!(exchanger.rates && exchanger.rates.length > 0)
        }
      });
    });
    
    // Add recent review activities
    const pendingReviews = JSON.parse(localStorage.getItem('pendingReviews') || '[]');
    const approvedReviews = JSON.parse(localStorage.getItem('approvedReviews') || '[]');
    
    if (pendingReviews.length > 0) {
      logs.push({
        id: `reviews-pending-${Date.now()}`,
        action: 'reviews_pending',
        description: `${pendingReviews.length} новых отзывов ожидают модерации`,
        user: 'system',
        timestamp: new Date(Date.now() - Math.random() * 3600000).toISOString(),
        details: { pendingCount: pendingReviews.length }
      });
    }
    
    if (approvedReviews.length > 0) {
      const latestApproved = approvedReviews[approvedReviews.length - 1];
      logs.push({
        id: `review-approved-${latestApproved.id}`,
        action: 'review_approved',
        description: `Отзыв от ${latestApproved.author} одобрен`,
        user: 'admin',
        timestamp: new Date(Date.now() - Math.random() * 7200000).toISOString(),
        details: { reviewId: latestApproved.id, author: latestApproved.author }
      });
    }
    
    // Add admin session activities
    const adminSessions = JSON.parse(localStorage.getItem('admin_sessions') || '[]');
    if (adminSessions.length > 0) {
      const recentSession = adminSessions[adminSessions.length - 1];
      logs.push({
        id: `admin-session-${Date.now()}`,
        action: 'admin_login',
        description: `Администратор ${recentSession.login || 'admin'} вошел в систему`,
        user: recentSession.login || 'admin',
        timestamp: recentSession.loginTime || new Date(Date.now() - 1800000).toISOString(),
        details: { sessionId: recentSession.id }
      });
    }

    // Add system activities
    logs.push(
      {
        id: `system-backup-${Date.now()}`,
        action: 'system_backup',
        description: 'Автоматическое резервное копирование данных',
        user: 'system',
        timestamp: new Date(Date.now() - 2 * 3600000).toISOString(),
        details: {
          backupSize: `${(adminExchangers.length * 0.1).toFixed(1)}MB`,
          recordsCount: adminExchangers.length
        }
      },
      {
        id: `data-sync-${Date.now()}`,
        action: 'data_sync',
        description: 'Синхронизация данных между интерфейсами',
        user: 'admin',
        timestamp: new Date(Date.now() - 900000).toISOString(),
        details: { syncedExchangers: activeExchangers.length }
      }
    );
    
    // Sort by timestamp (newest first)
    const sortedLogs = logs.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
    
    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedLogs = sortedLogs.slice(startIndex, endIndex);

    const result = {
      logs: paginatedLogs,
      totalPages: Math.ceil(sortedLogs.length / limit),
      totalCount: sortedLogs.length
    };

    // Cache the result to prevent duplicate generation
    sessionStorage.setItem(logKey, JSON.stringify(result));

    return result;
  },

  // Reviews management
  getAllReviews: async (page = 1, limit = 20, statusFilter = 'all') => {
    try {
      const pendingReviews = JSON.parse(localStorage.getItem('pendingReviews') || '[]')
        .map((r: any) => ({ ...r, status: 'pending' }));
      const approvedReviews = JSON.parse(localStorage.getItem('approvedReviews') || '[]')
        .map((r: any) => ({ ...r, status: 'approved' }));
      const rejectedReviews = JSON.parse(localStorage.getItem('rejectedReviews') || '[]')
        .map((r: any) => ({ ...r, status: 'rejected' }));
      
      let allReviews = [...pendingReviews, ...approvedReviews, ...rejectedReviews];
      
      // Apply status filter
      if (statusFilter !== 'all') {
        allReviews = allReviews.filter(review => review.status === statusFilter);
      }
      
      // Sort by date (newest first)
      allReviews.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      
      // Apply pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedReviews = allReviews.slice(startIndex, endIndex);
      
      const result = {
        reviews: paginatedReviews,
        totalPages: Math.ceil(allReviews.length / limit),
        totalCount: allReviews.length,
        statusCounts: {
          pending: pendingReviews.length,
          approved: approvedReviews.length,
          rejected: rejectedReviews.length,
          total: allReviews.length
        },
        loadedAt: new Date().toISOString()
      };
      
      console.log('Reviews loaded:', result);
      return result;
      
    } catch (error) {
      console.error('Error loading reviews:', error);
      return {
        reviews: [],
        totalPages: 0,
        totalCount: 0,
        statusCounts: {
          pending: 0,
          approved: 0,
          rejected: 0,
          total: 0
        },
        error: 'Failed to load reviews'
      };
    }
  },

  deleteReview: async (reviewId: number) => {
    try {
      // Remove from all review lists
      const reviewLists = ['pendingReviews', 'approvedReviews', 'rejectedReviews'];
      
      reviewLists.forEach(listName => {
        const reviews = JSON.parse(localStorage.getItem(listName) || '[]');
        const filteredReviews = reviews.filter((r: any) => r.id !== reviewId);
        localStorage.setItem(listName, JSON.stringify(filteredReviews));
      });
      
      return { success: true };
    } catch (error) {
      throw new Error('Ошибка удаления отзыва');
    }
  },

  moderateReview: async (reviewId: number, action: 'approve' | 'reject') => {
    try {
      // Find review in pending list
      const pendingReviews = JSON.parse(localStorage.getItem('pendingReviews') || '[]');
      const reviewIndex = pendingReviews.findIndex((r: any) => r.id === reviewId);
      
      if (reviewIndex === -1) {
        throw new Error('Отзыв не найден в списке ожидающих модерации');
      }
      
      const review = pendingReviews[reviewIndex];
      
      // Remove from pending
      pendingReviews.splice(reviewIndex, 1);
      localStorage.setItem('pendingReviews', JSON.stringify(pendingReviews));
      
      // Add to appropriate list
      const targetList = action === 'approve' ? 'approvedReviews' : 'rejectedReviews';
      const targetReviews = JSON.parse(localStorage.getItem(targetList) || '[]');
      
      const updatedReview = {
        ...review,
        status: action === 'approve' ? 'approved' : 'rejected',
        moderatedAt: new Date().toISOString(),
        moderatedBy: 'admin'
      };
      
      targetReviews.push(updatedReview);
      localStorage.setItem(targetList, JSON.stringify(targetReviews));
      
      return { success: true, review: updatedReview };
    } catch (error) {
      throw new Error(`Ошибка ${action === 'approve' ? 'одобрения' : 'отклонения'} отзыва`);
    }
  },

  // Parsing logs
  getParsingLogs: async () => []
};

// Mock exchanger management data - now properly initialized
export const mockExchangerManagement = adminExchangers;

// Initialize settings integration
settingsIntegration.initialize();

// Listen for settings changes that affect data operations
settingsIntegration.onSettingChange('auto_sync_networks', (event) => {
  console.log('Network sync setting changed:', event.newValue);
});

settingsIntegration.onSettingChange('network_sync_cooldown', (event) => {
  console.log('Network sync cooldown changed:', event.newValue);
});

settingsIntegration.onSettingChange('rate_validation_strict', (event) => {
  console.log('Rate validation setting changed:', event.newValue);
});

settingsIntegration.onSettingChange('mandatory_currency_pairs', (event) => {
  console.log('Mandatory currency pairs changed:', event.newValue);
});

// Global access for debugging
(window as any).debugPersistence = DataPersistenceDebugger;

export default adminAPI;