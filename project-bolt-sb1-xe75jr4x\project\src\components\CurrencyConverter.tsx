import React, { useState, useEffect } from 'react';
import { Calculator, ArrowUpDown, TrendingUp, Banknote, RefreshCw } from 'lucide-react';
import { Exchanger } from '../types';

interface CurrencyConverterProps {
  exchangers: Exchanger[];
}

const CurrencyConverter: React.FC<CurrencyConverterProps> = ({ exchangers }) => {
  const [fromCurrency, setFromCurrency] = useState('THB');
  const [toCurrency, setToCurrency] = useState('RUB');
  const [amount, setAmount] = useState('1000');
  const [result, setResult] = useState<number | null>(null);
  const [bestRate, setBestRate] = useState<{ rate: number; exchangerName: string } | null>(null);
  const [isConverting, setIsConverting] = useState(false);

  // Available currency pairs based on the data
  const availablePairs = [
    { from: 'THB', to: 'RUB', label: 'Тайский бат → Российский рубль', flag: '🇹🇭→🇷🇺' },
    { from: 'RUB', to: 'THB', label: 'Российский рубль → Тайский бат', flag: '🇷🇺→🇹🇭' },
    { from: 'USDT', to: 'THB', label: 'USDT → Тайский бат', flag: '₮→🇹🇭' },
    { from: 'THB', to: 'USDT', label: 'Тайский бат → USDT', flag: '🇹🇭→₮' },
    { from: 'USD', to: 'THB', label: 'Доллар США → Тайский бат', flag: '🇺🇸→🇹🇭' },
    { from: 'THB', to: 'USD', label: 'Тайский бат → Доллар США', flag: '🇹🇭→🇺🇸' },
  ];

  // Get best rate for current currency pair
  const getBestRateForPair = (from: string, to: string) => {
    let bestRate = 0;
    let bestExchanger = '';
    let currencyKey = '';

    // Determine the currency key based on the pair
    if (from === 'THB' && to === 'RUB') {
      currencyKey = 'THB/RUB';
    } else if (from === 'RUB' && to === 'THB') {
      currencyKey = 'THB/RUB';
    } else if (from === 'USDT' && to === 'THB') {
      currencyKey = 'USDT/BAHT';
    } else if (from === 'THB' && to === 'USDT') {
      currencyKey = 'THB/USDT';
    } else if (from === 'USD' && to === 'THB') {
      currencyKey = 'USD';
    } else if (from === 'THB' && to === 'USD') {
      currencyKey = 'USD';
    }

    if (!currencyKey) return null;

    // Find the best rate among all exchangers
    exchangers.forEach(exchanger => {
      const rate = exchanger.rates.find(r => r.currency === currencyKey);
      if (rate) {
        let currentRate = 0;
        
        // Determine which rate to use based on conversion direction
        if (from === 'THB' && to === 'RUB') {
          currentRate = rate.buy; // THB to RUB conversion uses buy rate
        } else if (from === 'RUB' && to === 'THB') {
          currentRate = 1 / rate.sell; // RUB to THB conversion uses inverse of sell rate
        } else if (from === 'USDT' && to === 'THB') {
          currentRate = rate.buy; // USDT to THB conversion uses buy rate
        } else if (from === 'THB' && to === 'USDT') {
          currentRate = rate.sell; // THB to USDT conversion uses sell rate
        } else if (from === 'USD' && to === 'THB') {
          currentRate = rate.buy; // USD to THB conversion uses buy rate
        } else if (from === 'THB' && to === 'USD') {
          currentRate = 1 / rate.sell; // THB to USD conversion uses inverse of sell rate
        }

        // For most conversions, we want the highest rate (best for customer)
        // For inverse conversions, we want the lowest rate
        const shouldMaximize = from === 'THB' || from === 'USDT' || from === 'USD';
        
        if (shouldMaximize ? currentRate > bestRate : (bestRate === 0 || currentRate < bestRate)) {
          bestRate = currentRate;
          bestExchanger = exchanger.name;
        }
      }
    });

    return bestRate > 0 ? { rate: bestRate, exchangerName: bestExchanger } : null;
  };

  // Calculate conversion
  const calculateConversion = () => {
    setIsConverting(true);
    
    // Simulate calculation delay for better UX
    setTimeout(() => {
      const inputAmount = parseFloat(amount);
      if (isNaN(inputAmount) || inputAmount <= 0) {
        setResult(null);
        setBestRate(null);
        setIsConverting(false);
        return;
      }

      const rateData = getBestRateForPair(fromCurrency, toCurrency);
      if (rateData) {
        const convertedAmount = inputAmount * rateData.rate;
        setResult(convertedAmount);
        setBestRate(rateData);
      } else {
        setResult(null);
        setBestRate(null);
      }
      setIsConverting(false);
    }, 300);
  };

  // Recalculate when inputs change
  useEffect(() => {
    if (amount && fromCurrency && toCurrency) {
      calculateConversion();
    }
  }, [amount, fromCurrency, toCurrency, exchangers]);

  // Swap currencies
  const swapCurrencies = () => {
    setFromCurrency(toCurrency);
    setToCurrency(fromCurrency);
  };

  // Format currency display
  const formatCurrency = (value: number, currency: string) => {
    const formatOptions: Intl.NumberFormatOptions = {
      minimumFractionDigits: 2,
      maximumFractionDigits: currency === 'USDT' ? 4 : 2,
    };

    return new Intl.NumberFormat('ru-RU', formatOptions).format(value);
  };

  // Get currency symbol
  const getCurrencySymbol = (currency: string) => {
    switch (currency) {
      case 'THB': return '฿';
      case 'RUB': return '₽';
      case 'USD': return '$';
      case 'USDT': return '₮';
      default: return '';
    }
  };

  // Get currency flag
  const getCurrencyFlag = (currency: string) => {
    switch (currency) {
      case 'THB': return '🇹🇭';
      case 'RUB': return '🇷🇺';
      case 'USD': return '🇺🇸';
      case 'USDT': return '₮';
      default: return '💱';
    }
  };

  return (
    <div className="bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 rounded-2xl border-2 border-indigo-200 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-white bg-opacity-20 p-2 rounded-lg">
              <Calculator className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-2xl font-bold mb-1">💱 Калькулятор валют</h3>
              <p className="text-indigo-100">Конвертация по лучшим курсам обменников</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-indigo-200">Используются</div>
            <div className="text-lg font-bold">Лучшие курсы</div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Currency Selection */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end mb-6">
          {/* From Currency */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Из валюты
            </label>
            <select
              value={fromCurrency}
              onChange={(e) => setFromCurrency(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors bg-white text-gray-700"
            >
              {['THB', 'RUB', 'USD', 'USDT'].map(currency => (
                <option key={currency} value={currency}>
                  {getCurrencyFlag(currency)} {currency} - {
                    currency === 'THB' ? 'Тайский бат' :
                    currency === 'RUB' ? 'Российский рубль' :
                    currency === 'USD' ? 'Доллар США' :
                    'Tether USDT'
                  }
                </option>
              ))}
            </select>
          </div>

          {/* Swap Button */}
          <div className="flex justify-center">
            <button
              onClick={swapCurrencies}
              className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white p-3 rounded-full transition-all duration-200 transform hover:scale-110 active:scale-95 shadow-lg hover:shadow-xl"
              title="Поменять валюты местами"
            >
              <ArrowUpDown className="w-5 h-5" />
            </button>
          </div>

          {/* To Currency */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              В валюту
            </label>
            <select
              value={toCurrency}
              onChange={(e) => setToCurrency(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors bg-white text-gray-700"
            >
              {['THB', 'RUB', 'USD', 'USDT'].map(currency => (
                <option key={currency} value={currency}>
                  {getCurrencyFlag(currency)} {currency} - {
                    currency === 'THB' ? 'Тайский бат' :
                    currency === 'RUB' ? 'Российский рубль' :
                    currency === 'USD' ? 'Доллар США' :
                    'Tether USDT'
                  }
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Amount Input and Result */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Input Amount */}
          <div className="bg-white rounded-xl p-4 border-2 border-indigo-200 shadow-sm">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Сумма для конвертации
            </label>
            <div className="relative">
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-lg font-semibold text-gray-800 pr-16"
                placeholder="Введите сумму"
                min="0"
                step="0.01"
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-lg font-bold text-indigo-600">
                {getCurrencySymbol(fromCurrency)}
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-600 flex items-center">
              <span className="text-2xl mr-2">{getCurrencyFlag(fromCurrency)}</span>
              <span>
                {fromCurrency === 'THB' ? 'Тайский бат' :
                 fromCurrency === 'RUB' ? 'Российский рубль' :
                 fromCurrency === 'USD' ? 'Доллар США' :
                 'Tether USDT'}
              </span>
            </div>
          </div>

          {/* Result */}
          <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-xl p-4 border-2 border-emerald-200 shadow-sm">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Результат конвертации
            </label>
            <div className="relative">
              <div className="w-full p-4 bg-white border border-emerald-300 rounded-lg text-lg font-bold text-emerald-800 min-h-[56px] flex items-center">
                {isConverting ? (
                  <div className="flex items-center space-x-2 text-gray-600">
                    <RefreshCw className="w-5 h-5 animate-spin" />
                    <span>Расчет...</span>
                  </div>
                ) : result !== null ? (
                  <span>{formatCurrency(result, toCurrency)} {getCurrencySymbol(toCurrency)}</span>
                ) : (
                  <span className="text-gray-400">Введите сумму</span>
                )}
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-600 flex items-center">
              <span className="text-2xl mr-2">{getCurrencyFlag(toCurrency)}</span>
              <span>
                {toCurrency === 'THB' ? 'Тайский бат' :
                 toCurrency === 'RUB' ? 'Российский рубль' :
                 toCurrency === 'USD' ? 'Доллар США' :
                 'Tether USDT'}
              </span>
            </div>
          </div>
        </div>

        {/* Best Rate Information */}
        {bestRate && result !== null && (
          <div className="bg-white rounded-xl p-4 border-2 border-green-200 shadow-sm">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <div className="bg-green-500 p-2 rounded-full">
                  <TrendingUp className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">🏆 Лучший курс</h4>
                  <p className="text-sm text-gray-600">Используется курс от лучшего обменника</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-500">Курс</div>
                <div className="text-lg font-bold text-green-600">
                  {bestRate.rate.toFixed(fromCurrency === 'THB' && toCurrency === 'RUB' ? 3 : 
                                        fromCurrency === 'THB' && toCurrency === 'USDT' ? 4 : 2)}
                </div>
              </div>
            </div>
            
            <div className="bg-green-50 rounded-lg p-3 border border-green-200">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-green-800">
                    Обменник: {bestRate.exchangerName}
                  </div>
                  <div className="text-xs text-green-700 mt-1">
                    1 {fromCurrency} = {bestRate.rate.toFixed(fromCurrency === 'THB' && toCurrency === 'RUB' ? 3 : 
                                                              fromCurrency === 'THB' && toCurrency === 'USDT' ? 4 : 2)} {toCurrency}
                  </div>
                </div>
                <div className="flex items-center space-x-1 text-green-600">
                  <Banknote className="w-4 h-4" />
                  <span className="text-sm font-medium">Лучший курс</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Quick Amount Buttons */}
        <div className="mt-6">
          <div className="text-sm font-medium text-gray-700 mb-3">Быстрый выбор суммы:</div>
          <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
            {[
              { amount: '1000', label: '1K' },
              { amount: '5000', label: '5K' },
              { amount: '10000', label: '10K' },
              { amount: '25000', label: '25K' },
              { amount: '50000', label: '50K' },
              { amount: '100000', label: '100K' }
            ].map(({ amount: quickAmount, label }) => (
              <button
                key={quickAmount}
                onClick={() => setAmount(quickAmount)}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  amount === quickAmount
                    ? 'bg-indigo-600 text-white shadow-md'
                    : 'bg-white text-indigo-600 border border-indigo-200 hover:bg-indigo-50'
                }`}
              >
                {label}
              </button>
            ))}
          </div>
        </div>

        {/* Popular Pairs Quick Access */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="text-sm font-medium text-gray-700 mb-3">Популярные пары:</div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {[
              { from: 'THB', to: 'RUB', label: 'THB → RUB', desc: 'Баты в рубли' },
              { from: 'RUB', to: 'THB', label: 'RUB → THB', desc: 'Рубли в баты' },
              { from: 'USDT', to: 'THB', label: 'USDT → THB', desc: 'USDT в баты' },
              { from: 'THB', to: 'USDT', label: 'THB → USDT', desc: 'Баты в USDT' }
            ].map(({ from, to, label, desc }) => (
              <button
                key={`${from}-${to}`}
                onClick={() => {
                  setFromCurrency(from);
                  setToCurrency(to);
                }}
                className={`p-3 rounded-lg text-left transition-all duration-200 border ${
                  fromCurrency === from && toCurrency === to
                    ? 'bg-indigo-600 text-white border-indigo-600 shadow-md'
                    : 'bg-white text-gray-700 border-gray-200 hover:bg-indigo-50 hover:border-indigo-300'
                }`}
              >
                <div className="font-medium text-sm">{label}</div>
                <div className={`text-xs mt-1 ${
                  fromCurrency === from && toCurrency === to ? 'text-indigo-100' : 'text-gray-500'
                }`}>
                  {desc}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Disclaimer */}
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start space-x-2">
            <div className="text-yellow-600 mt-0.5">⚠️</div>
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-1">Важная информация:</p>
              <ul className="space-y-1 text-xs">
                <li>• Курсы обновляются каждые 5 минут</li>
                <li>• Расчеты основаны на лучших курсах среди всех обменников</li>
                <li>• Фактические курсы могут отличаться - уточняйте в обменном пункте</li>
                <li>• Калькулятор носит информационный характер</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CurrencyConverter;