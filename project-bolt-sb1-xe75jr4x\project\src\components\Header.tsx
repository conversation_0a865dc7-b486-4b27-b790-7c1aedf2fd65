import React, { useState } from 'react';
import { Search, Menu, X, TrendingUp, Globe, Phone, Clock, User, LogOut, BarChart3 } from 'lucide-react';
import { useAuthContext } from './auth/AuthProvider';

interface HeaderProps {
  currentSection: string;
  onSectionChange: (section: string) => void;
  onSearch: (query: string) => void;
}

const Header: React.FC<HeaderProps> = ({ currentSection, onSectionChange, onSearch }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showUserMenu, setShowUserMenu] = useState(false);

  // Get auth context
  const { user, isAuthenticated, logout } = useAuthContext();

  // Fallback: check localStorage directly if auth context shows no user
  const [localAuthUser, setLocalAuthUser] = useState<any>(null);

  const checkLocalAuth = React.useCallback(() => {
    if (!isAuthenticated) {
      // Check localStorage directly as fallback
      const authData = localStorage.getItem('auth');
      if (authData) {
        try {
          const auth = JSON.parse(authData);
          if (auth.user && Date.now() < auth.expiresAt) {
            setLocalAuthUser(auth.user);
          } else {
            setLocalAuthUser(null);
          }
        } catch (e) {
          setLocalAuthUser(null);
        }
      } else {
        setLocalAuthUser(null);
      }
    } else {
      setLocalAuthUser(null);
    }
  }, [isAuthenticated]);

  React.useEffect(() => {
    checkLocalAuth();
  }, [checkLocalAuth]);

  // Listen for auth state changes
  React.useEffect(() => {
    const handleAuthChange = () => {
      checkLocalAuth();
    };

    window.addEventListener('authStateChanged', handleAuthChange);
    return () => window.removeEventListener('authStateChanged', handleAuthChange);
  }, [checkLocalAuth]);

  // Use auth context user or fallback to localStorage user
  const effectiveUser = user || localAuthUser;
  const effectiveIsAuthenticated = isAuthenticated || !!localAuthUser;

  // Debug auth state (only in development)
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🏠 Header auth state:', {
        contextUser: user?.email,
        contextAuth: isAuthenticated,
        localUser: localAuthUser?.email,
        effectiveAuth: effectiveIsAuthenticated
      });
    }
  }, [user, isAuthenticated, localAuthUser, effectiveIsAuthenticated]);

  // Close user menu when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showUserMenu && !(event.target as Element).closest('.user-menu-container')) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showUserMenu]);

  const menuItems = [
    { id: 'rates', label: 'Курсы валют', description: 'Актуальные курсы обменников' },
    { id: 'top', label: 'Лучшие', description: 'Рейтинг обменников' },
    { id: 'reviews', label: 'Отзывы', description: 'Отзывы клиентов' },
    { id: 'map', label: 'Карта', description: 'Расположение на карте' }
  ];

  // Add historical rates for authenticated users
  const authenticatedMenuItems = effectiveIsAuthenticated ? [
    ...menuItems,
    { id: 'historical', label: 'История курсов', description: 'Исторические данные курсов' }
  ] : menuItems;

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Prevent default form submission that could cause reload
    if (e.target) {
      e.stopPropagation();
    }
    
    onSearch(searchQuery);
  };

  return (
    <>
      {/* Top Info Bar */}
      <div className="bg-gradient-to-r from-blue-800 to-blue-900 text-white py-2">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between text-sm">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4" />
                <span>Обновление курсов: 3 раза в день</span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4" />
                <span>+66 89 123 4567</span>
              </div>
            </div>
            <div className="flex items-center space-x-4 mt-2 md:mt-0">
              <div className="flex items-center space-x-2">
                <Globe className="w-4 h-4" />
                <span>Пхукет, Бангкок, Паттайя</span>
              </div>
              {/* User Registration Link */}

              {/* Data sync indicator */}
              <div className="text-xs text-gray-500">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Данные синхронизированы</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <header className="bg-white shadow-xl sticky top-0 z-40 border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo Section */}
            <div className="flex items-center space-x-4">
              <button 
                onClick={() => onSectionChange('rates')}
                className="bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 p-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer"
                title="Перейти на главную страницу"
              >
                <TrendingUp className="w-8 h-8 text-white" />
              </button>
              <div>
                <button 
                  onClick={() => onSectionChange('rates')}
                  className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:opacity-80 transition-opacity cursor-pointer"
                >
                  Обменники Таиланда
                </button>
                <p className="text-gray-600 text-sm font-medium">
                  Лучшие курсы валют • Проверенные обменники
                </p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1">
              {authenticatedMenuItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => {
                    if (item.id === 'historical') {
                      window.location.href = '/historical-rates';
                    } else {
                      onSectionChange(item.id);
                    }
                  }}
                  className={`group px-4 py-3 rounded-xl font-medium transition-all duration-200 relative ${
                    currentSection === item.id
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'
                  }`}
                >
                  <div className="text-center">
                    <div className="font-semibold flex items-center justify-center space-x-1">
                      {item.id === 'historical' && <BarChart3 className="w-4 h-4" />}
                      <span>{item.label}</span>
                    </div>
                    <div className={`text-xs mt-1 ${
                      currentSection === item.id ? 'text-blue-100' : 'text-gray-500 group-hover:text-blue-500'
                    }`}>
                      {item.description}
                    </div>
                  </div>
                </button>
              ))}
            </nav>

            {/* Search Bar */}
            <form onSubmit={handleSearch} className="hidden md:flex items-center">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Найти обменник..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="bg-gray-50 border-2 border-gray-200 text-gray-800 placeholder-gray-500 pl-12 pr-4 py-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 w-80"
                />
              </div>
            </form>

            {/* User Authentication Controls */}
            <div className="hidden lg:flex items-center space-x-4">
              {effectiveIsAuthenticated ? (
                <div className="relative user-menu-container">
                  <button
                    onClick={() => setShowUserMenu(!showUserMenu)}
                    className="flex items-center space-x-3 px-4 py-2 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-xl hover:from-green-600 hover:to-blue-600 transition-all duration-200 shadow-lg"
                  >
                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4" />
                    </div>
                    <div className="text-left">
                      <div className="font-medium text-sm">{effectiveUser?.email?.split('@')[0] || 'Пользователь'}</div>
                      <div className="text-xs text-green-100">Авторизован</div>
                    </div>
                  </button>

                  {showUserMenu && (
                    <div className="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-xl border border-gray-200 py-2 z-50">
                      <div className="px-4 py-3 border-b border-gray-100">
                        <p className="text-sm font-medium text-gray-900">{effectiveUser?.email}</p>
                        <p className="text-xs text-gray-500">Роль: {effectiveUser?.role || 'user'}</p>
                        <div className="flex items-center mt-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span className="text-xs text-green-600 font-medium">Онлайн</span>
                        </div>
                      </div>
                      <button
                        onClick={() => {
                          window.location.href = '/historical-rates';
                          setShowUserMenu(false);
                        }}
                        className="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-3 transition-colors"
                      >
                        <BarChart3 className="w-4 h-4 text-blue-500" />
                        <div>
                          <div className="font-medium">История курсов</div>
                          <div className="text-xs text-gray-500">Исторические данные</div>
                        </div>
                      </button>
                      <div className="border-t border-gray-100 mt-1 pt-1">
                        <button
                          onClick={async () => {
                            await logout();
                            setShowUserMenu(false);
                            window.location.href = '/';
                          }}
                          className="w-full text-left px-4 py-3 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-3 transition-colors"
                        >
                          <LogOut className="w-4 h-4" />
                          <div>
                            <div className="font-medium">Выйти</div>
                            <div className="text-xs text-red-400">Завершить сеанс</div>
                          </div>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <a
                    href="/login"
                    className="px-4 py-2 text-gray-700 hover:text-blue-600 font-medium transition-colors"
                  >
                    Войти
                  </a>
                  <a
                    href="/register"
                    className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl hover:from-blue-600 hover:to-purple-600 font-medium transition-all duration-200"
                  >
                    Регистрация
                  </a>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 rounded-xl hover:bg-gray-100 transition-colors"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="lg:hidden mt-6 pt-6 border-t border-gray-200">
              <div className="space-y-2">
                {authenticatedMenuItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => {
                      if (item.id === 'historical') {
                        window.location.href = '/historical-rates';
                      } else {
                        onSectionChange(item.id);
                      }
                      setIsMenuOpen(false);
                    }}
                    className={`w-full text-left px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                      currentSection === item.id
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <div className="font-semibold">{item.label}</div>
                    <div className={`text-sm mt-1 ${
                      currentSection === item.id ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {item.description}
                    </div>
                  </button>
                ))}
              </div>
              
              {/* Mobile Search */}
              <form onSubmit={handleSearch} className="mt-4">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Найти обменник..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full bg-gray-50 border-2 border-gray-200 text-gray-800 placeholder-gray-500 pl-12 pr-4 py-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  />
                </div>
              </form>

              {/* Mobile Authentication Controls */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                {effectiveIsAuthenticated ? (
                  <div className="space-y-2">
                    <div className="px-4 py-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border border-green-200">
                      <div className="flex items-center space-x-2 mb-2">
                        <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                          <User className="w-3 h-3 text-white" />
                        </div>
                        <p className="text-sm font-medium text-gray-900">{effectiveUser?.email}</p>
                      </div>
                      <div className="flex items-center justify-between">
                        <p className="text-xs text-gray-500">Роль: {effectiveUser?.role || 'user'}</p>
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                          <span className="text-xs text-green-600 font-medium">Онлайн</span>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => {
                        window.location.href = '/historical-rates';
                        setIsMenuOpen(false);
                      }}
                      className="w-full text-left px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-xl flex items-center space-x-3 transition-colors"
                    >
                      <BarChart3 className="w-4 h-4 text-blue-500" />
                      <div>
                        <div className="font-medium">История курсов</div>
                        <div className="text-xs text-gray-500">Исторические данные</div>
                      </div>
                    </button>
                    <button
                      onClick={async () => {
                        await logout();
                        setIsMenuOpen(false);
                        window.location.href = '/';
                      }}
                      className="w-full text-left px-4 py-3 text-red-600 hover:bg-red-50 rounded-xl flex items-center space-x-3 transition-colors"
                    >
                      <LogOut className="w-4 h-4" />
                      <div>
                        <div className="font-medium">Выйти</div>
                        <div className="text-xs text-red-400">Завершить сеанс</div>
                      </div>
                    </button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <a
                      href="/login"
                      className="block w-full text-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-xl font-medium"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Войти
                    </a>
                    <a
                      href="/register"
                      className="block w-full text-center px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl hover:from-blue-600 hover:to-purple-600 font-medium"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Регистрация
                    </a>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </header>
    </>
  );
};

export default Header;