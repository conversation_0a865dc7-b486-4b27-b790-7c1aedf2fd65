// Historical exchange rates service
import { config } from '../config/environment';

export interface HistoricalRate {
  currency_pair: string;
  buy_rate: number;
  sell_rate: number;
  exchanger_name: string;
  exchanger_id: string;
  parsed_at: string;
}

export interface HistoricalDataFilters {
  currency_pair?: string;
  exchanger_id?: string;
  days: number;
  date_range: {
    start: string;
    end: string;
  };
}

export interface HistoricalDataResponse {
  success: boolean;
  data: {
    rates: HistoricalRate[];
    total_count: number;
    filters: HistoricalDataFilters;
  };
  message?: string;
}

export interface CurrencyPair {
  currency_pair: string;
  records_count: number;
}

export interface CurrencyPairsResponse {
  success: boolean;
  data: {
    currency_pairs: CurrencyPair[];
    total_pairs: number;
  };
}

export interface HistoricalSummary {
  currency_pair: string;
  period_days: number;
  summary: {
    total_records: number;
    exchanger_count: number;
    buy_rate_stats: {
      average: number | null;
      minimum: number | null;
      maximum: number | null;
    };
    sell_rate_stats: {
      average: number | null;
      minimum: number | null;
      maximum: number | null;
    };
    date_range: {
      first_record: string | null;
      last_record: string | null;
    };
  };
  daily_trends: Array<{
    date: string;
    avg_buy_rate: number | null;
    avg_sell_rate: number | null;
    records_count: number;
  }>;
}

export interface HistoricalSummaryResponse {
  success: boolean;
  data: HistoricalSummary;
  message?: string;
}

class HistoricalRatesService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${config.API_BASE_URL}/rates`;
  }

  // Get current exchange rates
  async getCurrentRates(): Promise<HistoricalDataResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/current`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching current rates:', error);
      throw error;
    }
  }

  // Get historical exchange rates
  async getHistoricalRates(params: {
    currency_pair?: string;
    exchanger_id?: string;
    days?: number;
    limit?: number;
  } = {}): Promise<HistoricalDataResponse> {
    try {
      const searchParams = new URLSearchParams();
      
      if (params.currency_pair) {
        searchParams.append('currency_pair', params.currency_pair);
      }
      if (params.exchanger_id) {
        searchParams.append('exchanger_id', params.exchanger_id);
      }
      if (params.days) {
        searchParams.append('days', params.days.toString());
      }
      if (params.limit) {
        searchParams.append('limit', params.limit.toString());
      }

      const response = await fetch(`${this.baseUrl}/history?${searchParams}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching historical rates:', error);
      throw error;
    }
  }

  // Get historical rates summary with statistics
  async getHistoricalSummary(
    currency_pair: string,
    days: number = 30
  ): Promise<HistoricalSummaryResponse> {
    try {
      const searchParams = new URLSearchParams({
        currency_pair,
        days: days.toString(),
      });

      const response = await fetch(`${this.baseUrl}/history/summary?${searchParams}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching historical summary:', error);
      throw error;
    }
  }

  // Get available currency pairs
  async getCurrencyPairs(): Promise<CurrencyPairsResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/currency-pairs`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching currency pairs:', error);
      throw error;
    }
  }

  // Export historical data to CSV
  async exportToCSV(params: {
    currency_pair?: string;
    exchanger_id?: string;
    days?: number;
  } = {}): Promise<Blob> {
    try {
      const data = await this.getHistoricalRates({ ...params, limit: 10000 });
      
      if (!data.success || !data.data.rates.length) {
        throw new Error('No data available for export');
      }

      const headers = ['Currency Pair', 'Exchanger', 'Buy Rate', 'Sell Rate', 'Date'];
      const csvContent = [
        headers.join(','),
        ...data.data.rates.map(rate => [
          rate.currency_pair,
          `"${rate.exchanger_name}"`, // Quote to handle commas in names
          rate.buy_rate?.toString() || 'N/A',
          rate.sell_rate?.toString() || 'N/A',
          new Date(rate.parsed_at).toISOString()
        ].join(','))
      ].join('\n');

      return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      throw error;
    }
  }

  // Format rate for display
  formatRate(rate: number | null | undefined): string {
    if (rate === null || rate === undefined || isNaN(rate)) {
      return 'N/A';
    }
    return rate.toFixed(4);
  }

  // Format date for display
  formatDate(dateString: string, locale: string = 'ru-RU'): string {
    try {
      return new Date(dateString).toLocaleString(locale, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  }

  // Calculate rate change percentage
  calculateRateChange(oldRate: number, newRate: number): number | null {
    if (!oldRate || !newRate || oldRate === 0) {
      return null;
    }
    return ((newRate - oldRate) / oldRate) * 100;
  }

  // Get rate trend (up, down, stable)
  getRateTrend(oldRate: number, newRate: number, threshold: number = 0.01): 'up' | 'down' | 'stable' {
    const change = this.calculateRateChange(oldRate, newRate);
    
    if (change === null) {
      return 'stable';
    }
    
    if (Math.abs(change) < threshold) {
      return 'stable';
    }
    
    return change > 0 ? 'up' : 'down';
  }

  // Check if service is available
  async checkServiceHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${config.API_BASE_URL}/health`, {
        method: 'GET',
        timeout: 5000,
      } as RequestInit);
      
      return response.ok;
    } catch (error) {
      console.error('Service health check failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const historicalRatesService = new HistoricalRatesService();
export default historicalRatesService;
