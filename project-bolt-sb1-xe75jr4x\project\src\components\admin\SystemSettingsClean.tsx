import React, { useState, useEffect } from 'react';
import { Settings, Save, RefreshCw, Database, Mail, Globe, Shield, AlertCircle, CheckCircle } from 'lucide-react';
import { SystemSettings as SystemSettingsType } from '../../types/admin';
import { adminAPI } from '../../data/adminData';

const SystemSettings: React.FC = () => {
  const [settings, setSettings] = useState<SystemSettingsType[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [notification, setNotification] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [editingValues, setEditingValues] = useState<Record<string, string>>({});

  const categories = [
    { id: 'all', name: 'Все настройки', icon: Settings },
    { id: 'general', name: 'Общие', icon: Globe },
    { id: 'rates', name: 'Курсы валют', icon: Database },
    { id: 'email', name: 'Email', icon: Mail },
    { id: 'security', name: 'Безопасность', icon: Shield }
  ];

  useEffect(() => {
    loadSettings();
  }, [selectedCategory]);

  const loadSettings = async () => {
    setLoading(true);
    try {
      console.log('Loading settings for category:', selectedCategory);
      const data = await adminAPI.getSettings(selectedCategory);
      setSettings(data);
      console.log('Settings loaded successfully:', data.length);
      
      // Initialize editing values
      const initialValues: Record<string, string> = {};
      data.forEach(setting => {
        initialValues[setting.key] = setting.value;
      });
      setEditingValues(initialValues);
      
    } catch (error) {
      console.error('Settings loading error:', error);
      showNotification('error', `Ошибка загрузки настроек: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSetting = async (key: string, value: string) => {
    setSaving(key);
    try {
      console.log('Updating setting:', { key, value });
      await adminAPI.updateSetting(key, value);
      showNotification('success', 'Настройка успешно обновлена');
      
      // Update local editing values
      setEditingValues(prev => ({ ...prev, [key]: value }));
      
      // Reload settings to ensure consistency
      await loadSettings();
    } catch (error) {
      console.error('Setting update error:', error);
      showNotification('error', `Ошибка обновления настройки: ${error}`);
    } finally {
      setSaving(null);
    }
  };

  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleValueChange = (key: string, value: string) => {
    setEditingValues(prev => ({ ...prev, [key]: value }));
  };

  const hasUnsavedChanges = (setting: SystemSettingsType): boolean => {
    return editingValues[setting.key] !== setting.value;
  };

  const getCategoryIcon = (category: string) => {
    const categoryData = categories.find(c => c.id === category);
    const Icon = categoryData?.icon || Settings;
    return <Icon className="w-4 h-4" />;
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      general: 'bg-blue-100 text-blue-800',
      rates: 'bg-green-100 text-green-800',
      email: 'bg-purple-100 text-purple-800',
      security: 'bg-red-100 text-red-800'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const renderSettingInput = (setting: SystemSettingsType) => {
    const currentValue = editingValues[setting.key] || setting.value;
    const hasChanges = hasUnsavedChanges(setting);

    const handleChange = (value: string) => {
      handleValueChange(setting.key, value);
    };

    const handleSave = () => {
      handleUpdateSetting(setting.key, currentValue);
    };

    switch (setting.type) {
      case 'boolean':
        return (
          <div className="flex items-center space-x-3">
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={currentValue === 'true'}
                onChange={(e) => handleChange(e.target.checked.toString())}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
            {hasChanges && (
              <button
                onClick={handleSave}
                disabled={saving === setting.key}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm flex items-center space-x-1"
              >
                {saving === setting.key ? (
                  <RefreshCw className="w-3 h-3 animate-spin" />
                ) : (
                  <Save className="w-3 h-3" />
                )}
                <span>Сохранить</span>
              </button>
            )}
          </div>
        );

      case 'number':
        return (
          <div className="flex items-center space-x-3">
            <input
              type="number"
              value={currentValue}
              onChange={(e) => handleChange(e.target.value)}
              className={`border rounded-lg px-3 py-2 w-32 focus:ring-2 focus:ring-blue-500 ${
                hasChanges ? 'border-yellow-300 bg-yellow-50' : 'border-gray-300'
              }`}
            />
            {hasChanges && (
              <button
                onClick={handleSave}
                disabled={saving === setting.key}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm flex items-center space-x-1"
              >
                {saving === setting.key ? (
                  <RefreshCw className="w-3 h-3 animate-spin" />
                ) : (
                  <Save className="w-3 h-3" />
                )}
                <span>Сохранить</span>
              </button>
            )}
          </div>
        );

      default: // string
        return (
          <div className="flex items-center space-x-3">
            <input
              type="text"
              value={currentValue}
              onChange={(e) => handleChange(e.target.value)}
              className={`flex-1 border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 ${
                hasChanges ? 'border-yellow-300 bg-yellow-50' : 'border-gray-300'
              }`}
            />
            {hasChanges && (
              <button
                onClick={handleSave}
                disabled={saving === setting.key}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm flex items-center space-x-1"
              >
                {saving === setting.key ? (
                  <RefreshCw className="w-3 h-3 animate-spin" />
                ) : (
                  <Save className="w-3 h-3" />
                )}
                <span>Сохранить</span>
              </button>
            )}
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Settings className="w-6 h-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-800">Настройки системы</h2>
        </div>
        <button
          onClick={loadSettings}
          className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Обновить</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow border border-gray-200 p-4">
            <h3 className="font-semibold text-gray-800 mb-4">Категории</h3>
            <nav className="space-y-2">
              {categories.map((category) => {
                const Icon = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm font-medium">{category.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow border border-gray-200">
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-600 mt-2">Загрузка настроек...</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {settings.map((setting) => (
                  <div key={setting.id} className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-lg font-semibold text-gray-800">{setting.key}</h4>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(setting.category)}`}>
                            {getCategoryIcon(setting.category)}
                            <span className="ml-1">{setting.category}</span>
                          </span>
                          {hasUnsavedChanges(setting) && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              Не сохранено
                            </span>
                          )}
                        </div>
                        <p className="text-gray-600 text-sm mb-4">{setting.description}</p>
                        {renderSettingInput(setting)}
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 mt-4">
                      Последнее обновление: {new Date(setting.updatedAt).toLocaleString('ru-RU')} 
                      {setting.updatedBy && ` пользователем ${setting.updatedBy}`}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <div className="fixed top-4 right-4 z-50">
          <div className={`rounded-lg shadow-lg p-4 max-w-sm flex items-center space-x-3 ${
            notification.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
          }`}>
            {notification.type === 'success' ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            <p className="font-medium text-sm">{notification.message}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemSettings;
