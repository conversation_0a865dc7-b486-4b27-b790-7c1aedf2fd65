import React, { useState } from 'react';
import { User, Mail, Lock, Eye, EyeOff, AlertCircle, UserPlus, CheckCircle, Phone, ArrowLeft } from 'lucide-react';
import { userRegistrationService, ValidationError } from '../../services/userRegistrationService';

interface UserRegistrationFormProps {
  onSuccess: (message: string) => void;
  onSwitchToLogin?: () => void;
}

const UserRegistrationForm: React.FC<UserRegistrationFormProps> = ({ 
  onSuccess, 
  onSwitchToLogin 
}) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [emailChecking, setEmailChecking] = useState(false);
  const [emailExists, setEmailExists] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors([]);

    // Check password confirmation
    if (formData.password !== formData.confirmPassword) {
      setErrors([{ field: 'confirmPassword', message: 'Пароли не совпадают' }]);
      return;
    }

    setIsLoading(true);

    try {
      const result = await userRegistrationService.registerUser({
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
      });

      if (result.success) {
        onSuccess(result.message);
        
        // Reset form
        setFormData({
          email: '',
          password: '',
          confirmPassword: '',
          firstName: '',
          lastName: '',
          phone: '',
        });
      } else {
        setErrors([{ field: 'general', message: result.message }]);
      }
    } catch (error: any) {
      console.error('Registration error:', error);

      let errorMessage = 'Произошла ошибка при регистрации';

      if (error.message) {
        if (error.message.includes('Failed to fetch')) {
          errorMessage = 'Ошибка подключения к серверу. Проверьте интернет-соединение и попробуйте еще раз.';
        } else if (error.message.includes('уже существует') || error.message.includes('already exists')) {
          errorMessage = 'Пользователь с таким email уже существует';
        } else {
          errorMessage = error.message;
        }
      }

      setErrors([{
        field: 'general',
        message: errorMessage
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear errors for this field
    setErrors(prev => prev.filter(error => error.field !== field && error.field !== 'general'));

    // Calculate password strength
    if (field === 'password') {
      setPasswordStrength(calculatePasswordStrength(value));
    }

    // Check email availability with debounce
    if (field === 'email' && value.includes('@')) {
      checkEmailAvailability(value);
    }
  };

  const checkEmailAvailability = async (email: string) => {
    if (!email || !email.includes('@')) {
      setEmailExists(false);
      return;
    }

    setEmailChecking(true);
    
    try {
      // Debounce email check
      await new Promise(resolve => setTimeout(resolve, 500));
      const exists = await userRegistrationService.checkEmailExists(email);
      setEmailExists(exists);
      
      if (exists) {
        setErrors(prev => [
          ...prev.filter(e => e.field !== 'email'),
          { field: 'email', message: 'Пользователь с таким email уже существует' }
        ]);
      }
    } catch (error) {
      console.error('Error checking email:', error);
    } finally {
      setEmailChecking(false);
    }
  };

  const calculatePasswordStrength = (password: string): number => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
  };

  const getPasswordStrengthColor = (strength: number): string => {
    switch (strength) {
      case 0:
      case 1: return 'bg-red-500';
      case 2: return 'bg-orange-500';
      case 3: return 'bg-yellow-500';
      case 4: return 'bg-green-500';
      case 5: return 'bg-green-600';
      default: return 'bg-gray-300';
    }
  };

  const getPasswordStrengthText = (strength: number): string => {
    switch (strength) {
      case 0:
      case 1: return 'Очень слабый';
      case 2: return 'Слабый';
      case 3: return 'Средний';
      case 4: return 'Сильный';
      case 5: return 'Очень сильный';
      default: return '';
    }
  };

  const getFieldError = (field: string): string | undefined => {
    return errors.find(error => error.field === field)?.message;
  };

  const hasFieldError = (field: string): boolean => {
    return errors.some(error => error.field === field);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-900 via-teal-900 to-cyan-900 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-lg w-full p-8">
        {/* Back to main site */}
        <div className="mb-6">
          <a
            href="/"
            className="text-gray-400 hover:text-gray-600 transition-colors flex items-center space-x-1 text-sm"
            title="Вернуться на главную страницу"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>На главную</span>
          </a>
        </div>

        {/* Header */}
        <div className="text-center mb-8">
          <div className="bg-gradient-to-br from-emerald-500 to-teal-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <UserPlus className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Регистрация</h1>
          <p className="text-gray-600">Создайте аккаунт для доступа к дополнительным функциям</p>
        </div>

        {/* Registration Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Name Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Имя
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className={`w-full pl-10 pr-4 py-3 border rounded-lg transition-colors ${
                    hasFieldError('firstName') 
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                      : 'border-gray-300 focus:ring-emerald-500 focus:border-emerald-500'
                  }`}
                  placeholder="Иван"
                  disabled={isLoading}
                  autoComplete="given-name"
                />
              </div>
              {hasFieldError('firstName') && (
                <p className="mt-1 text-sm text-red-600">{getFieldError('firstName')}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Фамилия
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className={`w-full pl-10 pr-4 py-3 border rounded-lg transition-colors ${
                    hasFieldError('lastName') 
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                      : 'border-gray-300 focus:ring-emerald-500 focus:border-emerald-500'
                  }`}
                  placeholder="Иванов"
                  disabled={isLoading}
                  autoComplete="family-name"
                />
              </div>
              {hasFieldError('lastName') && (
                <p className="mt-1 text-sm text-red-600">{getFieldError('lastName')}</p>
              )}
            </div>
          </div>

          {/* Email Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email адрес *
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`w-full pl-10 pr-12 py-3 border rounded-lg transition-colors ${
                  hasFieldError('email') 
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                    : emailExists
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                      : 'border-gray-300 focus:ring-emerald-500 focus:border-emerald-500'
                }`}
                placeholder="<EMAIL>"
                required
                disabled={isLoading}
                autoComplete="email"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {emailChecking ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
                ) : emailExists ? (
                  <AlertCircle className="w-4 h-4 text-red-500" />
                ) : formData.email && !hasFieldError('email') ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : null}
              </div>
            </div>
            {hasFieldError('email') && (
              <p className="mt-1 text-sm text-red-600">{getFieldError('email')}</p>
            )}
          </div>

          {/* Phone Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Телефон
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className={`w-full pl-10 pr-4 py-3 border rounded-lg transition-colors ${
                  hasFieldError('phone') 
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                    : 'border-gray-300 focus:ring-emerald-500 focus:border-emerald-500'
                }`}
                placeholder="+66 89 123 4567"
                disabled={isLoading}
                autoComplete="tel"
              />
            </div>
            {hasFieldError('phone') && (
              <p className="mt-1 text-sm text-red-600">{getFieldError('phone')}</p>
            )}
          </div>

          {/* Password Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Пароль *
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className={`w-full pl-10 pr-12 py-3 border rounded-lg transition-colors ${
                  hasFieldError('password') 
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                    : 'border-gray-300 focus:ring-emerald-500 focus:border-emerald-500'
                }`}
                placeholder="Минимум 8 символов"
                required
                disabled={isLoading}
                autoComplete="new-password"
                minLength={8}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                disabled={isLoading}
                tabIndex={-1}
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
            
            {/* Password Strength Indicator */}
            {formData.password && (
              <div className="mt-2">
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor(passwordStrength)}`}
                      style={{ width: `${(passwordStrength / 5) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-xs text-gray-600">
                    {getPasswordStrengthText(passwordStrength)}
                  </span>
                </div>
              </div>
            )}
            
            {hasFieldError('password') && (
              <p className="mt-1 text-sm text-red-600">{getFieldError('password')}</p>
            )}
          </div>

          {/* Confirm Password Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Подтвердите пароль *
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                className={`w-full pl-10 pr-12 py-3 border rounded-lg transition-colors ${
                  hasFieldError('confirmPassword') 
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                    : 'border-gray-300 focus:ring-emerald-500 focus:border-emerald-500'
                }`}
                placeholder="Повторите пароль"
                required
                disabled={isLoading}
                autoComplete="new-password"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                disabled={isLoading}
                tabIndex={-1}
              >
                {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
            
            {/* Password Match Indicator */}
            {formData.confirmPassword && (
              <div className="mt-2 flex items-center space-x-2">
                {formData.password === formData.confirmPassword ? (
                  <>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm text-green-600">Пароли совпадают</span>
                  </>
                ) : (
                  <>
                    <AlertCircle className="w-4 h-4 text-red-500" />
                    <span className="text-sm text-red-600">Пароли не совпадают</span>
                  </>
                )}
              </div>
            )}
            
            {hasFieldError('confirmPassword') && (
              <p className="mt-1 text-sm text-red-600">{getFieldError('confirmPassword')}</p>
            )}
          </div>

          {/* General Error Message */}
          {hasFieldError('general') && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
              <span className="text-red-700 text-sm">{getFieldError('general')}</span>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={
              isLoading || 
              !formData.email || 
              !formData.password || 
              !formData.confirmPassword ||
              emailExists ||
              emailChecking ||
              passwordStrength < 3
            }
            className="w-full bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed text-white py-3 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Регистрация...</span>
              </>
            ) : (
              <>
                <UserPlus className="w-5 h-5" />
                <span>Создать аккаунт</span>
              </>
            )}
          </button>
        </form>

        {/* Login Link */}
        {onSwitchToLogin && (
          <div className="mt-6 text-center">
            <p className="text-gray-600 text-sm">
              Уже есть аккаунт?{' '}
              <button
                onClick={onSwitchToLogin}
                className="text-emerald-600 hover:text-emerald-800 font-medium underline transition-colors"
                disabled={isLoading}
              >
                Войти
              </button>
            </p>
          </div>
        )}

        {/* Terms Notice */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <p className="text-xs text-gray-600 text-center">
            Регистрируясь, вы соглашаетесь с{' '}
            <a href="/terms" className="text-emerald-600 underline">Условиями использования</a>
            {' '}и{' '}
            <a href="/privacy" className="text-emerald-600 underline">Политикой конфиденциальности</a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default UserRegistrationForm;