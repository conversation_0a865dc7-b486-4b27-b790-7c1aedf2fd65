import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import QueryProvider from './providers/QueryProvider';
import AuthProvider from './components/auth/AuthProvider';
import ErrorBoundary from './components/ErrorBoundary';
import ProtectedRoute from './components/auth/ProtectedRoute';
import AdminProtectedRoute from './components/auth/AdminProtectedRoute';
import LoginForm from './components/auth/LoginForm';
import RegisterForm from './components/auth/RegisterForm';
import UserRegistrationForm from './components/auth/UserRegistrationForm';
import UserLoginForm from './components/auth/UserLoginForm';
import HistoricalRates from './components/HistoricalRates';
import SecurityHeaders from './components/security/SecurityHeaders';
import Header from './components/Header';
import ExchangeRates from './components/ExchangeRates';
import TopExchangers from './components/TopExchangers';
import Reviews from './components/Reviews';
import GoogleMap from './components/GoogleMap';
import AdminPanel from './components/AdminPanel';
import Footer from './components/Footer';
import { useAuthContext } from './components/auth/AuthProvider';
import { adminAuthService } from './services/adminAuthService';

function App() {
  const [currentSection, setCurrentSection] = useState('rates');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedExchangerForReviews, setSelectedExchangerForReviews] = useState<number | null>(null);

  // Simulate automatic rate updates
  useEffect(() => {
    const interval = setInterval(() => {
      // In a real app, this would fetch new rates from an API
      console.log('Курсы валют обновлены');
    }, 300000); // Update every 5 minutes for demo (real app would be 1-3 times per day)

    return () => clearInterval(interval);
  }, []);

  const handleViewReviews = (exchangerId: number) => {
    // Устанавливаем выбранный обменник и переключаемся на раздел отзывов
    setSelectedExchangerForReviews(exchangerId);
    setCurrentSection('reviews');
  };

  const renderCurrentSection = () => {
    switch (currentSection) {
      case 'rates':
        return <ExchangeRates searchQuery={searchQuery} onViewReviews={handleViewReviews} />;
      case 'top':
        return <TopExchangers searchQuery={searchQuery} onViewReviews={handleViewReviews} />;
      case 'reviews':
        return <Reviews searchQuery={searchQuery} selectedExchangerId={selectedExchangerForReviews} onClearSelection={() => setSelectedExchangerForReviews(null)} />;
      case 'map':
        return <GoogleMap searchQuery={searchQuery} onViewReviews={handleViewReviews} />;
      default:
        return <ExchangeRates searchQuery={searchQuery} onViewReviews={handleViewReviews} />;
    }
  };

  const MainApp = () => {
    const { isAuthenticated } = useAuthContext();

    return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-emerald-50">
      <Header
        currentSection={currentSection}
        onSectionChange={setCurrentSection}
        onSearch={setSearchQuery}
      />
      
      <main className="container mx-auto px-4 py-8">
        {renderCurrentSection()}
      </main>
      
      <Footer />
    </div>
    );
  };

  const AdminRoute = () => {
    return (
      <AdminProtectedRoute>
        <AdminPanel onLogout={() => adminAuthService.logout()} />
      </AdminProtectedRoute>
    );
  };

  const AuthRoute = () => {
    const [authMode, setAuthMode] = useState<'login' | 'register' | 'user-register'>('login');
    const { isAuthenticated } = useAuthContext();
    
    // Redirect if already authenticated
    if (isAuthenticated) {
      return <Navigate to="/admin" replace />;
    }
    
    switch (authMode) {
      case 'login':
        return (
          <LoginForm 
            onSuccess={() => window.location.href = '/admin'}
            onSwitchToRegister={() => setAuthMode('register')}
          />
        );
      case 'register':
        return (
          <RegisterForm 
            onSuccess={() => window.location.href = '/admin'}
            onSwitchToLogin={() => setAuthMode('login')}
          />
        );
      case 'user-register':
        return (
          <UserRegistrationForm 
            onSuccess={(message) => {
              alert(message);
              setAuthMode('login');
            }}
            onSwitchToLogin={() => setAuthMode('login')}
          />
        );
      default:
        return <Navigate to="/auth" replace />;
    }
  };

  return (
    <>
      <SecurityHeaders />
      <ErrorBoundary
        onError={(error, errorInfo) => {
          console.error('App-level error caught:', error, errorInfo);
        }}
      >
        <QueryProvider>
          <ErrorBoundary
            onError={(error, errorInfo) => {
              console.error('Query provider error:', error, errorInfo);
            }}
          >
            <AuthProvider>
              <ErrorBoundary
                onError={(error, errorInfo) => {
                  console.error('Auth provider error:', error, errorInfo);
                }}
              >
                <Router>
                  <ErrorBoundary
                    onError={(error, errorInfo) => {
                      console.error('Router error:', error, errorInfo);
                    }}
                  >
                    <Routes>
                      <Route path="/" element={
                        <ErrorBoundary>
                          <MainApp />
                        </ErrorBoundary>
                      } />
                      <Route path="/auth" element={
                        <ErrorBoundary>
                          <AuthRoute />
                        </ErrorBoundary>
                      } />
                      <Route path="/register" element={
                        <ErrorBoundary>
                          <UserRegistrationForm
                            onSuccess={(message) => {
                              alert(message);
                              window.location.href = '/login';
                            }}
                            onSwitchToLogin={() => window.location.href = '/login'}
                          />
                        </ErrorBoundary>
                      } />
                      <Route path="/login" element={
                        <ErrorBoundary>
                          <UserLoginForm
                            onSuccess={(user) => {
                              console.log('User logged in:', user);
                              window.location.href = '/';
                            }}
                            onSwitchToRegister={() => window.location.href = '/register'}
                          />
                        </ErrorBoundary>
                      } />
                      <Route path="/historical-rates" element={
                        <ErrorBoundary>
                          <ProtectedRoute requiredRole="user">
                            <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-emerald-50">
                              <Header
                                currentSection="historical"
                                onSectionChange={(section) => {
                                  console.log('Header navigation from historical page:', section);
                                  if (section === 'rates') {
                                    window.location.href = '/';
                                  } else {
                                    window.location.href = `/${section}`;
                                  }
                                }}
                                onSearch={() => {}}
                              />
                              <main className="container mx-auto px-4 py-8">
                                <HistoricalRates
                                  onSectionChange={(section) => {
                                    console.log('HistoricalRates navigation:', section);
                                    if (section === 'rates') {
                                      window.location.href = '/';
                                    } else {
                                      window.location.href = `/${section}`;
                                    }
                                  }}
                                />
                              </main>
                              <Footer />
                            </div>
                          </ProtectedRoute>
                        </ErrorBoundary>
                      } />
                      <Route path="/admin" element={
                        <ErrorBoundary>
                          <AdminRoute />
                        </ErrorBoundary>
                      } />
                      <Route path="/admins" element={
                        <ErrorBoundary>
                          <AdminRoute />
                        </ErrorBoundary>
                      } />
                      <Route path="*" element={<Navigate to="/" replace />} />
                    </Routes>
                  </ErrorBoundary>
                </Router>
              </ErrorBoundary>
            </AuthProvider>
          </ErrorBoundary>
        </QueryProvider>
      </ErrorBoundary>
    </>
  );
}

export default App;