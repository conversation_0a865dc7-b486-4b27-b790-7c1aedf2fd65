/**
 * Settings Integration Service
 * Handles real-time application of settings changes across the system
 */

import settingsAPI from '../data/settingsData';

export interface SettingsChangeEvent {
  key: string;
  oldValue: string;
  newValue: string;
  category: string;
  timestamp: Date;
}

export class SettingsIntegrationService {
  private listeners: Map<string, ((event: SettingsChangeEvent) => void)[]> = new Map();
  private initialized = false;

  /**
   * Initialize the settings integration service
   */
  public initialize(): void {
    if (this.initialized) return;

    // Listen for settings update events
    window.addEventListener('settingsUpdated', this.handleSettingsUpdate.bind(this));
    
    // Apply current settings on initialization
    this.applyAllSettings();
    
    this.initialized = true;
    console.log('Settings Integration Service initialized');
  }

  /**
   * Handle settings update events
   */
  private handleSettingsUpdate(event: CustomEvent): void {
    console.log('Settings updated event received:', event.detail);
    
    // Re-apply all settings to ensure consistency
    this.applyAllSettings();
  }

  /**
   * Apply all current settings to the system
   */
  private async applyAllSettings(): Promise<void> {
    try {
      const allSettings = await settingsAPI.getSettings();
      
      for (const setting of allSettings) {
        this.applySetting(setting.key, setting.value, setting.category);
      }
      
      console.log('All settings applied successfully');
    } catch (error) {
      console.error('Error applying settings:', error);
    }
  }

  /**
   * Apply a specific setting to the system
   */
  private applySetting(key: string, value: string, category: string): void {
    try {
      switch (key) {
        case 'debug_mode':
          this.applyDebugMode(value === 'true');
          break;
          
        case 'maintenance_mode':
          this.applyMaintenanceMode(value === 'true');
          break;
          
        case 'rates_update_interval':
          this.applyRatesUpdateInterval(parseInt(value));
          break;
          
        case 'auto_sync_networks':
          this.applyNetworkSyncSetting(value === 'true');
          break;
          
        case 'network_sync_cooldown':
          this.applyNetworkSyncCooldown(parseInt(value));
          break;
          
        case 'rate_validation_strict':
          this.applyStrictValidation(value === 'true');
          break;
          
        case 'mandatory_currency_pairs':
          this.applyMandatoryCurrencyPairs(JSON.parse(value));
          break;
          
        default:
          // Generic setting application
          this.notifyListeners(key, { key, oldValue: '', newValue: value, category, timestamp: new Date() });
          break;
      }
    } catch (error) {
      console.error(`Error applying setting ${key}:`, error);
    }
  }

  /**
   * Apply debug mode setting
   */
  private applyDebugMode(enabled: boolean): void {
    if (enabled) {
      console.log('🐛 Debug mode enabled');
      (window as any).debugMode = true;
      // Enable additional logging
      localStorage.setItem('debugMode', 'true');
    } else {
      console.log('Debug mode disabled');
      (window as any).debugMode = false;
      localStorage.removeItem('debugMode');
    }
  }

  /**
   * Apply maintenance mode setting
   */
  private applyMaintenanceMode(enabled: boolean): void {
    if (enabled) {
      console.log('🚧 Maintenance mode enabled');
      // Could show maintenance banner
      document.body.classList.add('maintenance-mode');
    } else {
      console.log('Maintenance mode disabled');
      document.body.classList.remove('maintenance-mode');
    }
  }

  /**
   * Apply rates update interval setting
   */
  private applyRatesUpdateInterval(intervalMinutes: number): void {
    console.log(`📊 Rates update interval set to ${intervalMinutes} minutes`);
    
    // Store for use by rate update services
    (window as any).ratesUpdateInterval = intervalMinutes;
    
    // Trigger rate update service reconfiguration
    window.dispatchEvent(new CustomEvent('ratesIntervalChanged', {
      detail: { intervalMinutes }
    }));
  }

  /**
   * Apply network sync setting
   */
  private applyNetworkSyncSetting(enabled: boolean): void {
    console.log(`🔄 Network synchronization ${enabled ? 'enabled' : 'disabled'}`);
    (window as any).networkSyncEnabled = enabled;
  }

  /**
   * Apply network sync cooldown setting
   */
  private applyNetworkSyncCooldown(cooldownMinutes: number): void {
    console.log(`⏱️ Network sync cooldown set to ${cooldownMinutes} minutes`);
    (window as any).networkSyncCooldown = cooldownMinutes;
  }

  /**
   * Apply strict validation setting
   */
  private applyStrictValidation(enabled: boolean): void {
    console.log(`✅ Strict rate validation ${enabled ? 'enabled' : 'disabled'}`);
    (window as any).strictValidation = enabled;
  }

  /**
   * Apply mandatory currency pairs setting
   */
  private applyMandatoryCurrencyPairs(pairs: string[]): void {
    console.log('💱 Mandatory currency pairs updated:', pairs);
    (window as any).mandatoryCurrencyPairs = pairs;
  }

  /**
   * Register a listener for specific setting changes
   */
  public onSettingChange(key: string, callback: (event: SettingsChangeEvent) => void): void {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, []);
    }
    this.listeners.get(key)!.push(callback);
  }

  /**
   * Notify listeners of setting changes
   */
  private notifyListeners(key: string, event: SettingsChangeEvent): void {
    const keyListeners = this.listeners.get(key) || [];
    const allListeners = this.listeners.get('*') || [];
    
    [...keyListeners, ...allListeners].forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error(`Error in settings listener for ${key}:`, error);
      }
    });
  }

  /**
   * Get current setting value with type conversion
   */
  public getSetting<T>(key: string, defaultValue: T): T {
    return settingsAPI.getSettingValueTyped(key, defaultValue);
  }

  /**
   * Update a setting and apply it immediately
   */
  public async updateSetting(key: string, value: string): Promise<void> {
    const oldValue = settingsAPI.getSettingValue(key) || '';
    
    await settingsAPI.updateSetting(key, value);
    
    // Get the updated setting to get category info
    const allSettings = await settingsAPI.getSettings();
    const setting = allSettings.find(s => s.key === key);
    
    if (setting) {
      // Apply the setting immediately
      this.applySetting(key, value, setting.category);
      
      // Notify listeners
      this.notifyListeners(key, {
        key,
        oldValue,
        newValue: value,
        category: setting.category,
        timestamp: new Date()
      });
    }
  }

  /**
   * Validate setting value before applying
   */
  public validateSetting(key: string, value: string, type: string): { isValid: boolean; error?: string } {
    try {
      switch (type) {
        case 'number':
          const numValue = parseFloat(value);
          if (isNaN(numValue)) {
            return { isValid: false, error: 'Значение должно быть числом' };
          }
          
          // Specific validations
          if (key === 'rates_update_interval' && numValue < 1) {
            return { isValid: false, error: 'Интервал обновления должен быть больше 0' };
          }
          if (key === 'session_timeout' && (numValue < 5 || numValue > 1440)) {
            return { isValid: false, error: 'Время сессии должно быть от 5 до 1440 минут' };
          }
          if (key === 'network_sync_cooldown' && (numValue < 1 || numValue > 60)) {
            return { isValid: false, error: 'Задержка синхронизации должна быть от 1 до 60 минут' };
          }
          break;

        case 'boolean':
          if (value !== 'true' && value !== 'false') {
            return { isValid: false, error: 'Значение должно быть true или false' };
          }
          break;

        case 'json':
          try {
            const parsed = JSON.parse(value);
            if (key === 'mandatory_currency_pairs' && !Array.isArray(parsed)) {
              return { isValid: false, error: 'Обязательные валютные пары должны быть массивом' };
            }
          } catch {
            return { isValid: false, error: 'Некорректный JSON формат' };
          }
          break;

        case 'string':
          if (key === 'google_sheets_url' && value && !value.includes('docs.google.com')) {
            return { isValid: false, error: 'URL должен быть ссылкой на Google Таблицу' };
          }
          break;
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, error: 'Ошибка валидации' };
    }
  }

  /**
   * Get all settings for a category
   */
  public async getSettingsForCategory(category: string): Promise<SystemSettings[]> {
    return await settingsAPI.getSettings(category);
  }

  /**
   * Reset all settings to defaults
   */
  public async resetToDefaults(): Promise<void> {
    await settingsAPI.resetToDefaults();
    this.applyAllSettings();
  }
}

// Global instance
export const settingsIntegration = new SettingsIntegrationService();

// Initialize on module load
settingsIntegration.initialize();

// Global access for debugging
(window as any).settingsIntegration = settingsIntegration;

export default settingsIntegration;
