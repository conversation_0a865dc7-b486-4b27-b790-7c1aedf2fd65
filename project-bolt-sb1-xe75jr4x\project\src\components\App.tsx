@@ .. @@
   const renderCurrentSection = () => {
     switch (currentSection) {
       case 'rates':
-        return <ExchangeRates searchQuery={searchQuery} />;
+        return <ExchangeRates searchQuery={searchQuery} onViewReviews={handleViewReviews} />;
       case 'top':
-        return <TopExchangers searchQuery={searchQuery} />;
+        return <TopExchangers searchQuery={searchQuery} onViewReviews={handleViewReviews} />;
       case 'reviews':
         return <Reviews searchQuery={searchQuery} />;
       case 'map':
       default:
-        return <ExchangeRates searchQuery={searchQuery} />;
+        return <ExchangeRates searchQuery={searchQuery} onViewReviews={handleViewReviews} />;
+    }
+  };
+
+  const handleViewReviews = (exchangerId: number) => {
+    console.log('Opening reviews for exchanger:', exchangerId);
+    // Переключаемся на раздел отзывов
+    setCurrentSection('reviews');
    // Устанавливаем выбранный обменник для отзывов
    setSelectedExchangerForReviews(exchangerId);
   };
 
+  const handleViewReviews = (exchangerId: number) => {
+    console.log('Opening reviews for exchanger:', exchangerId);
+    // Переключаемся на раздел отзывов
+    setCurrentSection('reviews');
+    // В реальном приложении здесь можно было бы установить фильтр по конкретному обменнику
+    // setSelectedExchangerForReviews(exchangerId);
+  };
+