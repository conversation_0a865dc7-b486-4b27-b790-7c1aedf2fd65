import React from 'react';
import { Star, MapPin, Phone, Clock, TrendingUp, TrendingDown, ExternalLink, Shield, CheckCircle, Globe, MessageCircle, Banknote, Building, ChevronDown, ChevronUp } from 'lucide-react';
import { Exchanger } from '../types';
import { districts } from '../data/mockData';
import { useExchangers } from '../hooks/useExchangeData';

interface ExchangerListProps {
  exchangers: Exchanger[];
  selectedCurrency: string;
  onViewReviews?: (id: number) => void;
}

const ExchangerList: React.FC<ExchangerListProps> = ({ 
  exchangers, 
  selectedCurrency, 
  onViewReviews 
}) => {
  const [expandedServices, setExpandedServices] = React.useState<Set<string>>(new Set());
  
  // Get services from real data
  const { data: exchangersData } = useExchangers({ limit: 100 });
  const allExchangers = exchangersData?.exchangers || [];
  
  // Group exchangers by service (simplified version)
  const services = React.useMemo(() => {
    const serviceMap = new Map();
    
    allExchangers.forEach(exchanger => {
      const serviceId = exchanger.serviceId || `single-${exchanger.id}`;
      
      if (!serviceMap.has(serviceId)) {
        serviceMap.set(serviceId, {
          serviceId,
          serviceName: exchanger.name.split(' - ')[0],
          totalOffices: 0,
          mainOffice: exchanger,
          branches: [],
          averageRating: exchanger.rating,
          totalReviews: exchanger.reviewCount,
          website: exchanger.website,
          telegramBot: exchanger.telegramBot
        });
      }
      
      const service = serviceMap.get(serviceId);
      service.totalOffices++;
      
      if (exchanger.isMainOffice) {
        service.mainOffice = exchanger;
      } else {
        service.branches.push(exchanger);
      }
    });
    
    return Array.from(serviceMap.values());
  }, [allExchangers]);
  
  // Filter services based on exchangers list
  const filteredServices = services.filter(service => 
    exchangers.some(exchanger => exchanger.serviceId === service.serviceId)
  );

  const toggleServiceExpansion = (serviceId: string) => {
    const newExpanded = new Set(expandedServices);
    if (newExpanded.has(serviceId)) {
      newExpanded.delete(serviceId);
    } else {
      newExpanded.add(serviceId);
    }
    setExpandedServices(newExpanded);
  };

  const getDistrictName = (districtId: string) => {
    return districts.find(d => d.id === districtId)?.name || districtId;
  };

  const getOfficeTypeIcon = (officeType?: string) => {
    switch (officeType) {
      case 'main': return '🏢';
      case 'branch': return '🏪';
      case 'kiosk': return '🏬';
      default: return '📍';
    }
  };

  const getOfficeTypeName = (officeType?: string) => {
    switch (officeType) {
      case 'main': return 'Главный офис';
      case 'branch': return 'Филиал';
      case 'kiosk': return 'Киоск';
      default: return 'Офис';
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-3 h-3 ${
              i < Math.floor(rating) 
                ? 'text-yellow-400 fill-current' 
                : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-xs font-semibold text-gray-700">
          {rating.toFixed(1)}
        </span>
      </div>
    );
  };

  const handleExternalClick = (exchanger: Exchanger) => {
    if (exchanger.website) {
      console.log('Website click:', exchanger.name, exchanger.website);
      window.open(exchanger.website, '_blank', 'noopener,noreferrer');
    } else {
      console.warn('Website URL not available for:', exchanger.name);
    }
  };

  const handleTelegramClick = (exchanger: Exchanger) => {
    if (exchanger.telegramBot) {
      console.log('Telegram click:', exchanger.name, exchanger.telegramBot);
      window.open(exchanger.telegramBot, '_blank', 'noopener,noreferrer');
    } else {
      console.warn('Telegram bot URL not available for:', exchanger.name);
    }
  };

  const formatCurrencyRate = (rate: number, currency: string) => {
    if (currency.includes('THB/RUB')) {
      return rate.toFixed(3);
    } else if (currency.includes('THB/USDT')) {
      return rate.toFixed(4);
    }
    return rate.toFixed(2);
  };

  const getServiceOfficeCount = (serviceId: string) => {
    const services = getExchangeServices();
    const service = services.find(s => s.serviceId === serviceId);
    return service?.totalOffices || 1;
  };

  return (
    <div className="space-y-3">
      {exchangers.map((exchanger, index) => {
        const thbRubRate = exchanger.rates.find(r => r.currency === 'THB/RUB');
        const usdtBahtRate = exchanger.rates.find(r => r.currency === 'USDT/BAHT');
        
        return (
          <div key={exchanger.id} className="bg-white rounded-xl shadow-md border border-slate-200 overflow-hidden hover:shadow-lg hover:border-slate-300 transition-all duration-200">
            <div className="p-4">
              {/* Header Section - Compact */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm shadow-md flex-shrink-0">
                    {index + 1}
                  </div>
                  <div className="min-w-0">
                    <h3 className="text-lg font-bold text-slate-800 leading-tight truncate">{exchanger.name}</h3>
                    <div className="flex items-center space-x-2 mt-1">
                      {renderStars(exchanger.rating)}
                      <span className="text-xs text-slate-500 bg-slate-100 px-2 py-0.5 rounded-full">
                        {exchanger.reviewCount} отзывов
                      </span>
                    </div>
                  </div>
                </div>
                
                {/* Trust Badges - Compact */}
                <div className="flex items-center space-x-1">
                  {exchanger.rating >= 4.5 && (
                    <div className="bg-gradient-to-r from-amber-400 to-orange-400 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center">
                      <Shield className="w-3 h-3 mr-1" />
                      ТОП
                    </div>
                  )}
                  <div className="bg-gradient-to-r from-emerald-400 to-teal-400 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    ✓
                  </div>
                </div>
              </div>

              {/* Main Content - Horizontal Layout */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* Left: Contact Info - Compact */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="bg-teal-100 p-1 rounded">
                      <MapPin className="w-3 h-3 text-teal-600" />
                    </div>
                    <div className="min-w-0">
                      <div className="font-bold text-slate-800 text-lg">{getDistrictName(exchanger.district)}</div>
                      <div className="text-sm text-slate-600 leading-relaxed">{exchanger.address}</div>
                      {exchanger.serviceId && (
                        <div className="text-xs text-teal-600 font-medium mt-1">
                          {getServiceOfficeCount(exchanger.serviceId)} офисов в сети
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-2 text-slate-700">
                      <Phone className="w-4 h-4 text-emerald-600" />
                      <span className="font-semibold">{exchanger.phone}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-slate-700">
                      <Clock className="w-4 h-4 text-orange-600" />
                      <span className="font-semibold">{exchanger.hours}</span>
                    </div>
                  </div>
                </div>

                {/* Center: Currency Rates - Horizontal Layout */}
                <div className="bg-gradient-to-br from-teal-50 to-cyan-50 rounded-lg p-4 border border-teal-200">
                  <div className="flex flex-col items-center mb-2">
                    <div className="flex items-center justify-center space-x-1 text-base font-bold text-slate-800 mb-1">
                      <Banknote className="w-4 h-4 text-teal-600" />
                      <span>Курсы THB</span>
                    </div>
                    <div className="text-sm text-slate-600 text-center">
                      Обновлено: {new Date().toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' })}
                    </div>
                  </div>
                  
                  {/* Horizontal Currency Rates */}
                  <div className="flex flex-col md:flex-row gap-3 justify-center items-center">
                    {/* THB/RUB */}
                    {thbRubRate && (
                      <div className="bg-white rounded-lg p-3 border border-teal-200 shadow-sm w-full md:w-auto min-w-[200px]">
                        <div className="flex justify-center mb-1">
                          <div className="text-sm font-bold text-slate-800">THB/RUB</div>
                        </div>
                        <div className="flex justify-center gap-6 text-sm">
                          <div className="text-center flex-1">
                            <div className="text-sm text-emerald-600 font-semibold">Покупка</div>
                            <div className="text-lg font-bold text-emerald-700">
                              {formatCurrencyRate(thbRubRate.buy, 'THB/RUB')}
                            </div>
                          </div>
                          <div className="text-center flex-1">
                            <div className="text-sm text-rose-600 font-semibold">Продажа</div>
                            <div className="text-lg font-bold text-rose-700">
                              {formatCurrencyRate(thbRubRate.sell, 'THB/RUB')}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* USDT/BAHT */}
                    {usdtBahtRate && (
                      <div className="bg-white rounded-lg p-3 border border-teal-200 shadow-sm w-full md:w-auto min-w-[200px]">
                        <div className="flex justify-center mb-1">
                          <div className="text-sm font-bold text-slate-800">USDT/BAHT</div>
                        </div>
                        <div className="flex justify-center gap-6 text-sm">
                          <div className="text-center flex-1">
                            <div className="text-sm text-emerald-600 font-semibold">Покупка</div>
                            <div className="text-lg font-bold text-emerald-700">
                              {formatCurrencyRate(usdtBahtRate.buy, 'USDT/BAHT')}
                            </div>
                          </div>
                          <div className="text-center flex-1">
                            <div className="text-sm text-rose-600 font-semibold">Продажа</div>
                            <div className="text-lg font-bold text-rose-700">
                              {formatCurrencyRate(usdtBahtRate.sell, 'USDT/BAHT')}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Right: Action Buttons - Compact Grid */}
                <div className="grid grid-cols-2 gap-3">
                  {exchanger.website && (
                    <button
                      onClick={() => handleExternalClick(exchanger)}
                      className="bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white px-4 py-2.5 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-1 text-sm"
                    >
                      <Globe className="w-4 h-4" />
                      <span>Сайт</span>
                    </button>
                  )}
                  {exchanger.telegramBot && (
                    <button
                      onClick={() => handleTelegramClick(exchanger)}
                      className="bg-gradient-to-r from-teal-500 to-cyan-500 hover:from-teal-600 hover:to-cyan-600 text-white px-4 py-2.5 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-1 text-sm"
                    >
                      <MessageCircle className="w-4 h-4" />
                      <span>Telegram</span>
                    </button>
                  )}
                  <button
                    onClick={() => onViewReviews?.(exchanger.id)}
                    className="bg-emerald-100 hover:bg-emerald-200 text-emerald-700 px-4 py-2.5 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-1 text-sm cursor-pointer"
                    title="Посмотреть отзывы об этом обменнике"
                  >
                    <MessageCircle className="w-4 h-4" />
                    <span>Отзывы</span>
                  </button>
                  <button
                    onClick={() => window.open(`tel:${exchanger.phone}`, '_self')}
                    className="bg-emerald-100 hover:bg-emerald-200 text-emerald-700 px-4 py-2.5 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-1 text-sm"
                  >
                    <Phone className="w-4 h-4" />
                    <span>Позвонить</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        );
      })}

      {/* Service Groups - Compact Design */}
      {filteredServices.map((service, serviceIndex) => {
        const allOffices = [service.mainOffice, ...service.branches];
        const serviceOffices = allOffices.filter(office => 
          exchangers.some(exchanger => exchanger.id === office.id)
        );
        const isExpanded = expandedServices.has(service.serviceId);
        const hasMultipleOffices = service.totalOffices > 1;

        return (
          <React.Fragment key={service.serviceId}>
            {/* Service Header - Compact */}
            {hasMultipleOffices && (
              <div className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-xl border-2 border-slate-300 p-3 mb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Building className="w-5 h-5 text-slate-600" />
                    <div>
                      <h3 className="text-lg font-bold text-slate-800">{service.serviceName}</h3>
                      <div className="flex items-center space-x-4 text-sm text-slate-600">
                        <span className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4" />
                          <span>{service.totalOffices} офисов</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Star className="w-4 h-4 text-amber-500" />
                          <span>{service.averageRating.toFixed(1)} ({service.totalReviews} отзывов)</span>
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {service.website && (
                      <button
                        onClick={() => window.open(service.website, '_blank', 'noopener,noreferrer')}
                        className="bg-slate-600 hover:bg-slate-700 text-white px-3 py-2 rounded-lg text-sm flex items-center space-x-1"
                      >
                        <Globe className="w-4 h-4" />
                        <span>Сайт сети</span>
                      </button>
                    )}
                    <button
                      onClick={() => toggleServiceExpansion(service.serviceId)}
                      className="bg-slate-200 hover:bg-slate-300 text-slate-700 px-3 py-2 rounded-lg text-sm flex items-center space-x-1"
                    >
                      {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                      <span>{isExpanded ? 'Свернуть' : 'Развернуть'}</span>
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Show collapsed indicator */}
            {hasMultipleOffices && !isExpanded && service.branches.length > 0 && (
              <div className="ml-6 mb-3">
                <button
                  onClick={() => toggleServiceExpansion(service.serviceId)}
                  className="bg-slate-100 hover:bg-slate-200 text-slate-600 px-4 py-2 rounded-lg text-sm flex items-center space-x-2 border-2 border-dashed border-slate-300"
                >
                  <ChevronDown className="w-4 h-4" />
                  <span>Показать еще {service.branches.length} филиалов</span>
                </button>
              </div>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default ExchangerList;