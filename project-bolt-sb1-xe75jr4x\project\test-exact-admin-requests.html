<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Тест точных запросов админки</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; max-height: 400px; }
        .endpoint { font-family: monospace; background: #e9ecef; padding: 2px 4px; border-radius: 3px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Тест точных запросов админки</h1>
        
        <div class="card info">
            <h2>📋 Цель тестирования</h2>
            <p>Эта страница делает точно такие же запросы, как компоненты админки:</p>
            <ul>
                <li><strong>ParsingResultsTable</strong> - запрос к <span class="endpoint">/parsing/results</span></li>
                <li><strong>HistoricalRatesTrends</strong> - запрос к <span class="endpoint">/parsing/historical-rates/trends</span></li>
            </ul>
            <p>Используется та же конфигурация API_BASE_URL: <span class="endpoint" id="api-base-url"></span></p>
        </div>

        <div class="card">
            <h2>🧪 Тесты запросов</h2>
            <div class="grid">
                <button onclick="testParsingResults()">Тест результатов парсинга</button>
                <button onclick="testRateTrends()">Тест трендов курсов</button>
                <button onclick="testBothWithFilters()">Тест с фильтрами</button>
                <button onclick="clearResults()">Очистить результаты</button>
            </div>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // Используем ту же конфигурацию, что и в приложении
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        document.getElementById('api-base-url').textContent = API_BASE_URL;

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="card ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // Точная копия запроса из ParsingResultsTable.tsx
        async function testParsingResults() {
            try {
                log('🔄 Тестирование ParsingResultsTable запроса...', 'info');
                
                // Создаем параметры точно как в компоненте
                const filters = {
                    exchanger_id: '',
                    status: '',
                    hours_back: 24,
                    limit: 50,
                    offset: 0
                };

                const params = new URLSearchParams();
                if (filters.exchanger_id) params.append('exchanger_id', filters.exchanger_id);
                if (filters.status) params.append('status', filters.status);
                if (filters.hours_back) params.append('hours_back', filters.hours_back.toString());
                params.append('limit', filters.limit.toString());
                params.append('offset', filters.offset.toString());

                const url = `${API_BASE_URL}/parsing/results?${params}`;
                log(`📡 URL: <span class="endpoint">${url}</span>`, 'info');

                const startTime = Date.now();
                const response = await fetch(url);
                const endTime = Date.now();

                log(`📊 Ответ получен за ${endTime - startTime}ms`, 'info');
                log(`📈 Status: ${response.status} ${response.statusText}`, 'info');
                log(`📄 Content-Type: ${response.headers.get('content-type')}`, 'info');

                if (!response.ok) {
                    throw new Error(`Failed to fetch parsing results: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data && data.data.results) {
                    const count = data.data.results.length;
                    log(`✅ ParsingResultsTable - УСПЕХ<br>
                        📊 Получено результатов: ${count}<br>
                        📋 Структура данных корректна<br>
                        <details><summary>Первые 2 записи</summary><pre>${JSON.stringify(data.data.results.slice(0, 2), null, 2)}</pre></details>`, 'success');
                } else {
                    throw new Error('Неверная структура ответа');
                }

            } catch (error) {
                log(`❌ ParsingResultsTable - ОШИБКА<br>
                    🚨 Error: ${error.message}<br>
                    💡 Это та же ошибка, что видит админка!`, 'error');
            }
        }

        // Точная копия запроса из HistoricalRatesTrends.tsx
        async function testRateTrends() {
            try {
                log('🔄 Тестирование HistoricalRatesTrends запроса...', 'info');
                
                // Создаем параметры точно как в компоненте
                const filters = {
                    currency_pair: 'RUB/THB',
                    days_back: 30,
                    interval: 'daily'
                };

                const params = new URLSearchParams({
                    currency_pair: filters.currency_pair,
                    days_back: filters.days_back.toString(),
                    interval: filters.interval
                });

                const url = `${API_BASE_URL}/parsing/historical-rates/trends?${params}`;
                log(`📡 URL: <span class="endpoint">${url}</span>`, 'info');

                const startTime = Date.now();
                const response = await fetch(url);
                const endTime = Date.now();

                log(`📊 Ответ получен за ${endTime - startTime}ms`, 'info');
                log(`📈 Status: ${response.status} ${response.statusText}`, 'info');
                log(`📄 Content-Type: ${response.headers.get('content-type')}`, 'info');

                if (!response.ok) {
                    throw new Error(`Failed to fetch rate trends: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data && data.data.trends) {
                    const count = data.data.trends.length;
                    log(`✅ HistoricalRatesTrends - УСПЕХ<br>
                        📊 Получено трендов: ${count}<br>
                        📋 Структура данных корректна<br>
                        <details><summary>Первые 2 записи</summary><pre>${JSON.stringify(data.data.trends.slice(0, 2), null, 2)}</pre></details>`, 'success');
                } else {
                    throw new Error('Неверная структура ответа');
                }

            } catch (error) {
                log(`❌ HistoricalRatesTrends - ОШИБКА<br>
                    🚨 Error: ${error.message}<br>
                    💡 Это та же ошибка, что видит админка!`, 'error');
            }
        }

        async function testBothWithFilters() {
            clearResults();
            log('🚀 Запуск комплексного тестирования с фильтрами...', 'info');
            
            await testParsingResults();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testRateTrends();
            
            log('🎉 Комплексное тестирование завершено!', 'success');
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Автоматический тест при загрузке
        window.addEventListener('load', () => {
            log('🔧 Страница тестирования точных запросов админки загружена', 'info');
            log(`🔗 API Base URL: ${API_BASE_URL}`, 'info');
            
            // Автоматически запускаем тест через 2 секунды
            setTimeout(async () => {
                log('🤖 Автоматический запуск тестирования...', 'info');
                await testBothWithFilters();
            }, 2000);
        });

        // Мониторинг сетевых ошибок
        window.addEventListener('unhandledrejection', event => {
            log(`🚨 Необработанная ошибка Promise: ${event.reason}`, 'error');
        });

        window.addEventListener('error', event => {
            log(`🚨 JavaScript ошибка: ${event.message}`, 'error');
        });
    </script>
</body>
</html>
