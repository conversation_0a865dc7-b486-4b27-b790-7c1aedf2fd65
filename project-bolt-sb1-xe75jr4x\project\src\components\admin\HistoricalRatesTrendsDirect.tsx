import React, { useState, useEffect } from 'react';
import { RefreshCw, AlertCircle } from 'lucide-react';

interface RateTrend {
  time_period: string;
  currency_pair: string;
  avg_buy_rate: number;
  avg_sell_rate: number;
  data_points: number;
}

const HistoricalRatesTrendsDirect: React.FC = () => {
  const [trends, setTrends] = useState<RateTrend[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastFetch, setLastFetch] = useState<Date | null>(null);

  // Прямой URL без конфигурации (порт 8001 для тестирования)
  const API_URL = 'http://localhost:8001/api/v1/parsing/historical-rates/trends?currency_pair=RUB/THB&days_back=30&interval=daily';

  const fetchTrends = async () => {
    console.log('🔄 HistoricalRatesTrendsDirect: Starting fetch...');
    console.log('📡 HistoricalRatesTrendsDirect: Direct URL =', API_URL);
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('📡 HistoricalRatesTrendsDirect: Making fetch request...');
      
      const response = await fetch(API_URL);
      console.log('📊 HistoricalRatesTrendsDirect: Response status:', response.status);
      console.log('📊 HistoricalRatesTrendsDirect: Response headers:', Object.fromEntries(response.headers.entries()));
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('✅ HistoricalRatesTrendsDirect: Data received:', data);
      
      if (data.success && data.data?.trends) {
        setTrends(data.data.trends);
        setLastFetch(new Date());
        console.log(`📋 HistoricalRatesTrendsDirect: Loaded ${data.data.trends.length} trends`);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('❌ HistoricalRatesTrendsDirect: Error:', errorMessage);
      console.error('❌ HistoricalRatesTrendsDirect: Full error:', err);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Загружаем данные при монтировании компонента
  useEffect(() => {
    console.log('🚀 HistoricalRatesTrendsDirect: Component mounted, fetching data...');
    fetchTrends();
    
    // Автоматическое обновление каждые 60 секунд
    const interval = setInterval(() => {
      console.log('⏰ HistoricalRatesTrendsDirect: Auto-refresh triggered');
      fetchTrends();
    }, 60000);
    
    return () => {
      console.log('🛑 HistoricalRatesTrendsDirect: Component unmounted, clearing interval');
      clearInterval(interval);
    };
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatRate = (rate: number) => {
    return rate.toFixed(4);
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Historical Rate Trends (Direct)</h2>
            <p className="text-gray-600">Direct fetch version for debugging</p>
            <p className="text-sm text-blue-600">Direct URL: {API_URL}</p>
          </div>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center space-x-2 text-red-800">
            <AlertCircle className="w-5 h-5" />
            <span className="font-medium">Error loading rate trends</span>
          </div>
          <p className="text-red-600 mt-2">{error}</p>
          <button
            onClick={fetchTrends}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Historical Rate Trends (Direct)</h2>
          <p className="text-gray-600">Direct fetch version for debugging</p>
          <p className="text-sm text-blue-600">Direct URL: {API_URL}</p>
          {lastFetch && (
            <p className="text-sm text-gray-500">Last updated: {lastFetch.toLocaleTimeString()}</p>
          )}
        </div>
        <button
          onClick={fetchTrends}
          disabled={isLoading}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          <span>{isLoading ? 'Loading...' : 'Refresh'}</span>
        </button>
      </div>

      {/* Results Table */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading rate trends...</span>
          </div>
        ) : trends.length === 0 ? (
          <div className="text-center py-12">
            <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No trends found</h3>
            <p className="text-gray-600">No rate trends available.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Currency Pair
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Avg Buy Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Avg Sell Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Data Points
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {trends.slice(0, 15).map((trend, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(trend.time_period)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {trend.currency_pair}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatRate(trend.avg_buy_rate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatRate(trend.avg_sell_rate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {trend.data_points}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      <div className="text-sm text-gray-500">
        Total trends: {trends.length} | Component: HistoricalRatesTrendsDirect
      </div>
    </div>
  );
};

export default HistoricalRatesTrendsDirect;
