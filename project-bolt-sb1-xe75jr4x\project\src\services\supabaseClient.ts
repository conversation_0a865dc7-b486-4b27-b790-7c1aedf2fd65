// Supabase client configuration
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { config } from '../config/environment';

// Database types (generated from Supabase)
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          role: 'admin' | 'moderator' | 'user';
          is_active: boolean;
          last_login: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          role?: 'admin' | 'moderator' | 'user';
          is_active?: boolean;
          last_login?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          role?: 'admin' | 'moderator' | 'user';
          is_active?: boolean;
          last_login?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      profiles: {
        Row: {
          id: string;
          user_id: string;
          first_name: string | null;
          last_name: string | null;
          avatar_url: string | null;
          phone: string | null;
          bio: string | null;
          preferences: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          first_name?: string | null;
          last_name?: string | null;
          avatar_url?: string | null;
          phone?: string | null;
          bio?: string | null;
          preferences?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          first_name?: string | null;
          last_name?: string | null;
          avatar_url?: string | null;
          phone?: string | null;
          bio?: string | null;
          preferences?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}

class SupabaseService {
  private static instance: SupabaseService;
  public client: SupabaseClient<Database> | null = null;
  public isConnected = false;

  private constructor() {
    this.initializeClient();
  }

  public static getInstance(): SupabaseService {
    if (!SupabaseService.instance) {
      SupabaseService.instance = new SupabaseService();
    }
    return SupabaseService.instance;
  }

  private initializeClient(): void {
    try {
      // Check if Supabase is properly configured
      const hasValidUrl = config.SUPABASE_URL && 
        config.SUPABASE_URL !== 'https://placeholder.supabase.co' &&
        !config.SUPABASE_URL.includes('placeholder') &&
        config.SUPABASE_URL.startsWith('https://') &&
        config.SUPABASE_URL.includes('.supabase.co') &&
        config.SUPABASE_URL.length > 30;
      
      const hasValidKey = config.SUPABASE_ANON_KEY && 
        config.SUPABASE_ANON_KEY !== 'placeholder-key' &&
        !config.SUPABASE_ANON_KEY.includes('placeholder') &&
        config.SUPABASE_ANON_KEY.length > 50 &&
        config.SUPABASE_ANON_KEY.startsWith('eyJ');
      
      if (!hasValidUrl || !hasValidKey) {
        console.info('Supabase not configured properly - using fallback mode');
        this.isConnected = false;
        this.client = null;
        return;
      }

      // Initialize Supabase client
      this.client = createClient<Database>(
        config.SUPABASE_URL,
        config.SUPABASE_ANON_KEY,
        {
          auth: {
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: true,
          },
        }
      );

      this.isConnected = true;
      console.info('Supabase client initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Supabase client:', error);
      this.isConnected = false;
      this.client = null;
    }
  }

  private isValidSupabaseUrl(url: string): boolean {
    return url && 
      url !== 'https://placeholder.supabase.co' &&
      !url.includes('placeholder') &&
      url.startsWith('https://') &&
      url.includes('.supabase.co') &&
      url.length > 30;
  }

  private isValidSupabaseKey(key: string): boolean {
    return key && 
      key !== 'placeholder-key' &&
      !key.includes('placeholder') &&
      key.length > 50 &&
      key.startsWith('eyJ');
  }

  // Test connection to Supabase
  public async testConnection(): Promise<boolean> {
    if (!this.client || !this.isConnected) {
      return false;
    }

    try {
      const { data, error } = await this.client
        .from('users')
        .select('count')
        .limit(1);

      return !error;
    } catch (error) {
      console.error('Supabase connection test failed:', error);
      // Don't throw error, just return false for graceful fallback
      return false;
    }
  }

  // Get current user with profile
  public async getCurrentUserWithProfile() {
    if (!this.client || !this.isConnected) {
      return null;
    }

    try {
      const { data: { user }, error: authError } = await this.client.auth.getUser();
      
      if (authError || !user) {
        return null;
      }

      // Get user data from our users table
      const { data: userData, error: userError } = await this.client
        .from('users')
        .select(`
          *,
          profiles (*)
        `)
        .eq('id', user.id)
        .single();

      if (userError) {
        console.error('Error fetching user data:', userError);
        return null;
      }

      return {
        ...user,
        ...userData,
        profile: userData.profiles?.[0] || null,
      };
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Update last login timestamp
  public async updateLastLogin(userId: string): Promise<void> {
    if (!this.client || !this.isConnected) {
      return;
    }

    try {
      await this.client
        .from('users')
        .update({ last_login: new Date().toISOString() })
        .eq('id', userId);
    } catch (error) {
      console.error('Error updating last login:', error);
    }
  }

  // Create user profile
  public async createUserProfile(userId: string, profileData: {
    first_name?: string;
    last_name?: string;
    phone?: string;
  }): Promise<void> {
    if (!this.client || !this.isConnected) {
      return;
    }

    try {
      await this.client
        .from('profiles')
        .insert({
          user_id: userId,
          first_name: profileData.first_name || '',
          last_name: profileData.last_name || '',
          phone: profileData.phone || null,
        });
    } catch (error) {
      console.error('Error creating user profile:', error);
    }
  }

  // Admin: Get all users
  public async getAllUsers() {
    if (!this.client || !this.isConnected) {
      return [];
    }

    try {
      const { data, error } = await this.client
        .from('users')
        .select(`
          *,
          profiles (*)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching users:', error);
        return [];
      }

      return data.map(user => ({
        ...user,
        profile: user.profiles?.[0] || null,
      }));
    } catch (error) {
      console.error('Error getting all users:', error);
      return [];
    }
  }

  // Admin: Update user role
  public async updateUserRole(userId: string, role: 'admin' | 'moderator' | 'user'): Promise<boolean> {
    if (!this.client || !this.isConnected) {
      return false;
    }

    try {
      const { error } = await this.client
        .from('users')
        .update({ role })
        .eq('id', userId);

      return !error;
    } catch (error) {
      console.error('Error updating user role:', error);
      return false;
    }
  }

  // Admin: Toggle user active status
  public async toggleUserStatus(userId: string, isActive: boolean): Promise<boolean> {
    if (!this.client || !this.isConnected) {
      return false;
    }

    try {
      const { error } = await this.client
        .from('users')
        .update({ is_active: isActive })
        .eq('id', userId);

      return !error;
    } catch (error) {
      console.error('Error updating user status:', error);
      return false;
    }
  }
}

// Export singleton instance
export const supabaseService = SupabaseService.getInstance();
export const supabase = supabaseService.client;
export default supabaseService;