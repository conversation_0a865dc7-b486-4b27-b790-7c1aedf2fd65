<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Header Authentication Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Header Authentication State Test</h1>
    
    <div class="test-section info">
        <h2>Test Instructions</h2>
        <p>This test verifies the header authentication improvements:</p>
        <ol>
            <li><strong>Before Login:</strong> Should show "Войти" and "Регистрация" buttons</li>
            <li><strong>After Login:</strong> Should show user profile with name/email and dropdown menu</li>
            <li><strong>Removed Elements:</strong> "Регистрация" and "Admin" buttons should be removed from top bar</li>
            <li><strong>User Experience:</strong> Clear indication of authentication status</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button onclick="testUnauthenticatedState()">Test Unauthenticated State</button>
        <button onclick="testAuthenticatedState()">Test Authenticated State</button>
        <button onclick="simulateLogin()">Simulate Login</button>
        <button onclick="simulateLogout()">Simulate Logout</button>
        <button onclick="checkHeaderElements()">Check Header Elements</button>
    </div>

    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="test-result ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            console.log(`[${timestamp}] ${message}`);
        }

        function testUnauthenticatedState() {
            log('Testing unauthenticated state...', 'info');
            
            // Clear any existing auth data
            localStorage.removeItem('auth_user');
            localStorage.removeItem('auth_token');
            
            // Check what should be visible
            const checks = [
                'Login button should be visible',
                'Registration button should be visible in main header',
                'User profile should NOT be visible',
                'Admin button should NOT be visible in top bar',
                'Registration button should NOT be visible in top bar'
            ];
            
            log(`
                <h3>Unauthenticated State Checklist:</h3>
                <ul>
                    ${checks.map(check => `<li>${check}</li>`).join('')}
                </ul>
                <p><strong>Action:</strong> Please verify these elements in the main application header.</p>
            `, 'info');
        }

        function testAuthenticatedState() {
            log('Testing authenticated state...', 'info');
            
            // Simulate authenticated user
            const mockUser = {
                email: '<EMAIL>',
                role: 'user',
                isAuthenticated: true,
                loginTime: new Date().toISOString()
            };
            
            localStorage.setItem('auth_user', JSON.stringify(mockUser));
            localStorage.setItem('auth_token', 'mock-jwt-token');
            
            const checks = [
                'User profile button should be visible with user name/email',
                'Login/Registration buttons should be HIDDEN',
                'User dropdown menu should work when clicked',
                'Dropdown should show user email and role',
                'Dropdown should show "История курсов" option',
                'Dropdown should show "Выйти" option',
                'Online status indicator should be visible'
            ];
            
            log(`
                <h3>Authenticated State Checklist:</h3>
                <ul>
                    ${checks.map(check => `<li>${check}</li>`).join('')}
                </ul>
                <p><strong>Action:</strong> Please refresh the page and verify these elements in the header.</p>
            `, 'success');
        }

        function simulateLogin() {
            log('Simulating user login...', 'info');
            
            const testUser = {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                role: 'user',
                isAuthenticated: true,
                loginTime: new Date().toISOString()
            };
            
            // Store in the same format as the auth system
            localStorage.setItem('auth_user', JSON.stringify(testUser));
            localStorage.setItem('auth_token', 'simulated-jwt-token-' + Date.now());
            
            log(`
                <h3>Login Simulation Complete</h3>
                <p>User: ${testUser.email}</p>
                <p>Role: ${testUser.role}</p>
                <p><strong>Please refresh the page to see the authenticated header state.</strong></p>
            `, 'success');
        }

        function simulateLogout() {
            log('Simulating user logout...', 'info');
            
            // Clear auth data
            localStorage.removeItem('auth_user');
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_session');
            
            log(`
                <h3>Logout Simulation Complete</h3>
                <p><strong>Please refresh the page to see the unauthenticated header state.</strong></p>
            `, 'info');
        }

        function checkHeaderElements() {
            log('Checking current header elements...', 'info');
            
            const authUser = localStorage.getItem('auth_user');
            const authToken = localStorage.getItem('auth_token');
            const isAuthenticated = authUser && authToken;
            
            let status = '';
            if (isAuthenticated) {
                const user = JSON.parse(authUser);
                status = `
                    <h3>Current Authentication Status: AUTHENTICATED</h3>
                    <p>User: ${user.email}</p>
                    <p>Role: ${user.role || 'user'}</p>
                    <p>Login Time: ${user.loginTime || 'Unknown'}</p>
                `;
            } else {
                status = `
                    <h3>Current Authentication Status: NOT AUTHENTICATED</h3>
                    <p>No user session found</p>
                `;
            }
            
            log(`
                ${status}
                <h4>Expected Header Elements:</h4>
                <ul>
                    ${isAuthenticated ? `
                        <li>✅ User profile button with name/email</li>
                        <li>✅ User dropdown menu</li>
                        <li>❌ Login button (should be hidden)</li>
                        <li>❌ Registration button (should be hidden)</li>
                    ` : `
                        <li>✅ Login button</li>
                        <li>✅ Registration button</li>
                        <li>❌ User profile (should be hidden)</li>
                    `}
                    <li>❌ Admin button in top bar (should be removed)</li>
                    <li>❌ Registration button in top bar (should be removed)</li>
                </ul>
            `, isAuthenticated ? 'success' : 'info');
        }

        // Check initial state
        window.addEventListener('load', () => {
            log('Header Authentication Test Loaded', 'info');
            checkHeaderElements();
            
            log(`
                <h3>Test Summary</h3>
                <p>The header has been updated with the following improvements:</p>
                <ul>
                    <li><strong>Removed redundant buttons:</strong> "Регистрация" and "Admin" buttons from top bar</li>
                    <li><strong>Enhanced user display:</strong> Better visual indication when authenticated</li>
                    <li><strong>Improved dropdown:</strong> More informative user menu with status indicators</li>
                    <li><strong>Consistent mobile experience:</strong> Same improvements on mobile devices</li>
                    <li><strong>Clean authentication flow:</strong> Clear transition between authenticated/unauthenticated states</li>
                </ul>
            `, 'success');
        });
    </script>
</body>
</html>
