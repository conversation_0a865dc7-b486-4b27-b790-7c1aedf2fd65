"""
Admin API routes
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import logging

from services.exchange_service import exchange_service
from services.google_sheets_service import google_sheets_service
from services.cache_service import cache_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/status", response_model=Dict[str, Any])
async def get_system_status():
    """Get system status and health check"""
    try:
        status = await exchange_service.get_system_status()
        return {
            'success': True,
            'data': status
        }
        
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/rates/update", response_model=Dict[str, Any])
async def force_update_rates():
    """Force update exchange rates from Google Sheets"""
    try:
        result = await exchange_service.force_update_rates()
        
        if result.get('success'):
            return {
                'success': True,
                'data': result
            }
        else:
            raise HTTPException(status_code=503, detail=result.get('message', 'Update failed'))
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating rates: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/cache/clear", response_model=Dict[str, Any])
async def clear_cache():
    """Clear exchange rates cache"""
    try:
        success = await cache_service.clear_cache()
        
        if success:
            return {
                'success': True,
                'message': 'Cache cleared successfully'
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to clear cache")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/cache/info", response_model=Dict[str, Any])
async def get_cache_info():
    """Get cache information"""
    try:
        cache_info = await cache_service.get_cache_info()
        
        return {
            'success': True,
            'data': cache_info
        }
        
    except Exception as e:
        logger.error(f"Error getting cache info: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/sheets/test", response_model=Dict[str, Any])
async def test_google_sheets_connection():
    """Test Google Sheets connection"""
    try:
        result = await google_sheets_service.test_connection()
        
        return {
            'success': result.get('success', False),
            'data': result
        }
        
    except Exception as e:
        logger.error(f"Error testing Google Sheets connection: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")