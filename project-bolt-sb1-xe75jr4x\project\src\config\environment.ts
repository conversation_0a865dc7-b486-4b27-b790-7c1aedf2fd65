// Конфигурация окружения для продакшна
export interface EnvironmentConfig {
  API_BASE_URL: string;
  APP_ENV: 'development' | 'staging' | 'production';
  APP_VERSION: string;
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
  SENTRY_DSN?: string;
  GOOGLE_ANALYTICS_ID?: string;
  GOOGLE_MAPS_API_KEY?: string;
  MAPBOX_TOKEN?: string;
  ENABLE_ANALYTICS: boolean;
  ENABLE_ERROR_REPORTING: boolean;
  LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error';
  CACHE_TTL: number;
  API_TIMEOUT: number;
}

// Получение конфигурации из переменных окружения
const getEnvironmentConfig = (): EnvironmentConfig => {
  const env = import.meta.env.MODE as 'development' | 'staging' | 'production';
  
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  // More strict validation for Supabase configuration
  const isValidSupabaseUrl = supabaseUrl && 
    supabaseUrl !== 'https://placeholder.supabase.co' &&
    !supabaseUrl.includes('placeholder') &&
    supabaseUrl.startsWith('https://') &&
    supabaseUrl.includes('.supabase.co') &&
    supabaseUrl.length > 30;
  
  const isValidSupabaseKey = supabaseAnonKey && 
    supabaseAnonKey !== 'placeholder-key' &&
    !supabaseAnonKey.includes('placeholder') &&
    supabaseAnonKey.length > 50 &&
    supabaseAnonKey.startsWith('eyJ');

  // Базовая конфигурация
  const baseConfig: EnvironmentConfig = {
    API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1',
    APP_ENV: env,
    APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
    SUPABASE_URL: isValidSupabaseUrl ? supabaseUrl : '',
    SUPABASE_ANON_KEY: isValidSupabaseKey ? supabaseAnonKey : '',
    SENTRY_DSN: import.meta.env.VITE_SENTRY_DSN,
    GOOGLE_ANALYTICS_ID: import.meta.env.VITE_GA_ID,
    GOOGLE_MAPS_API_KEY: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
    MAPBOX_TOKEN: import.meta.env.VITE_MAPBOX_TOKEN,
    ENABLE_ANALYTICS: env === 'production',
    ENABLE_ERROR_REPORTING: env !== 'development',
    LOG_LEVEL: env === 'production' ? 'error' : 'debug',
    CACHE_TTL: 5 * 60 * 1000, // 5 минут
    API_TIMEOUT: 10000, // 10 секунд
  };

  // Конфигурация для разных окружений
  switch (env) {
    case 'production':
      return {
        ...baseConfig,
        API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'https://api.thailand-exchange.com/api/v1',
        ENABLE_ANALYTICS: true,
        ENABLE_ERROR_REPORTING: true,
        LOG_LEVEL: 'error',
        CACHE_TTL: 15 * 60 * 1000, // 15 минут в продакшне
      };
    
    case 'staging':
      return {
        ...baseConfig,
        API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'https://staging-api.thailand-exchange.com/api/v1',
        ENABLE_ANALYTICS: false,
        ENABLE_ERROR_REPORTING: true,
        LOG_LEVEL: 'warn',
      };
    
    default: // development
      return {
        ...baseConfig,
        ENABLE_ANALYTICS: false,
        ENABLE_ERROR_REPORTING: false,
        LOG_LEVEL: 'debug',
        CACHE_TTL: 1 * 60 * 1000, // 1 минута в разработке
      };
  }
};

export const config = getEnvironmentConfig();

// Валидация обязательных переменных окружения
export const validateEnvironment = (): void => {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  const isValidSupabaseUrl = supabaseUrl && 
    supabaseUrl !== 'https://placeholder.supabase.co' &&
    !supabaseUrl.includes('placeholder') &&
    supabaseUrl.startsWith('https://') &&
    supabaseUrl.includes('.supabase.co') &&
    supabaseUrl.length > 30;
  
  const isValidSupabaseKey = supabaseAnonKey && 
    supabaseAnonKey !== 'placeholder-key' &&
    !supabaseAnonKey.includes('placeholder') &&
    supabaseAnonKey.length > 50 &&
    supabaseAnonKey.startsWith('eyJ');
  
  if (!isValidSupabaseUrl || !isValidSupabaseKey) {
    console.warn(
      'Supabase not configured properly. Using fallback authentication.\n' +
      'To enable full functionality, please configure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env file.'
    );
  }
  
  if (config.APP_ENV === 'production' && (!import.meta.env.VITE_SENTRY_DSN || !import.meta.env.VITE_GA_ID)) {
    console.warn(
      'Production environment detected but monitoring services not configured.\n' +
      'Please configure VITE_SENTRY_DSN and VITE_GA_ID for production use.'
    );
  }
};

// Утилиты для работы с конфигурацией
export const isProduction = () => config.APP_ENV === 'production';
export const isDevelopment = () => config.APP_ENV === 'development';
export const isStaging = () => config.APP_ENV === 'staging';