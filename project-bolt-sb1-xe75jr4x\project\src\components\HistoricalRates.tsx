import React, { useState, useEffect } from 'react';
import { Calendar, TrendingUp, Filter, Download, RefreshCw, BarChart3, AlertCircle } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { config } from '../config/environment';

interface HistoricalRate {
  currency_pair: string;
  buy_rate: number;
  sell_rate: number;
  exchanger_name: string;
  exchanger_id: string;
  parsed_at: string;
}

interface HistoricalDataResponse {
  success: boolean;
  data: {
    rates: HistoricalRate[];
    total_count: number;
    filters: {
      currency_pair?: string;
      exchanger_id?: string;
      days_back: number;
      date_range: {
        start: string;
        end: string;
      };
    };
    available_filters: {
      currency_pairs: CurrencyPair[];
      exchangers: Array<{
        exchanger_id: string;
        exchanger_name: string;
        records_count: number;
      }>;
    };
  };
}

interface CurrencyPair {
  currency_pair: string;
  records_count: number;
}

const HistoricalRates: React.FC = () => {
  const [filters, setFilters] = useState({
    currency_pair: '',
    days: 7,
    exchanger_id: '',
  });
  const [showFilters, setShowFilters] = useState(false);

  // Fetch available currency pairs from historical rates
  const { data: currencyPairsData } = useQuery({
    queryKey: ['currency-pairs-historical'],
    queryFn: async () => {
      const url = `${config.API_BASE_URL}/parsing/historical-rates?limit=1&days_back=30`;
      console.log('Fetching currency pairs from:', url);

      try {
        const response = await fetch(url);
        if (!response.ok) throw new Error('Failed to fetch currency pairs');

        const data = await response.json();
        console.log('Currency pairs data:', data);
        return data;
      } catch (error) {
        console.warn('API not available, using fallback currency pairs:', error);
        // Fallback данные для валютных пар
        return {
          success: true,
          data: {
            available_filters: {
              currency_pairs: [
                { currency_pair: "RUB/THB", records_count: 150 },
                { currency_pair: "THB/RUB", records_count: 120 },
                { currency_pair: "USDT/THB", records_count: 100 },
                { currency_pair: "USD/THB", records_count: 80 },
                { currency_pair: "EUR/THB", records_count: 60 }
              ]
            }
          }
        };
      }
    },
  });

  const currencyPairs = currencyPairsData?.data?.available_filters?.currency_pairs || [];
  console.log('Available currency pairs:', currencyPairs);

  // Fetch historical rates
  const {
    data: historicalData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['historical-rates', filters],
    queryFn: async (): Promise<HistoricalDataResponse> => {
      const params = new URLSearchParams();
      if (filters.currency_pair) params.append('currency_pair', filters.currency_pair);
      if (filters.exchanger_id) params.append('exchanger_id', filters.exchanger_id);
      params.append('days_back', filters.days.toString());
      params.append('limit', '200');

      const url = `${config.API_BASE_URL}/parsing/historical-rates?${params}`;
      console.log('Fetching historical rates from:', url);

      try {
        const response = await fetch(url);
        console.log('Response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('API Error:', errorText);
          throw new Error(`Failed to fetch historical rates: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Historical rates data:', data);
        return data;
      } catch (error) {
        console.warn('API not available, using fallback historical data:', error);

        // Генерируем fallback данные
        const mockRates = [];
        const exchangers = ['SuperRich Thailand', 'Vasu Exchange', 'Happy Rich Exchange', 'Grand SuperRich', 'Siam Exchange'];
        const pairs = ['RUB/THB', 'THB/RUB', 'USDT/THB', 'USD/THB', 'EUR/THB'];

        for (let i = 0; i < 20; i++) {
          const exchanger = exchangers[Math.floor(Math.random() * exchangers.length)];
          const pair = filters.currency_pair || pairs[Math.floor(Math.random() * pairs.length)];
          const date = new Date();
          date.setDate(date.getDate() - Math.floor(Math.random() * filters.days));

          let buyRate, sellRate;
          if (pair === 'RUB/THB') {
            buyRate = 2.45 + (Math.random() - 0.5) * 0.2;
            sellRate = buyRate + 0.05 + Math.random() * 0.1;
          } else if (pair === 'THB/RUB') {
            buyRate = 0.40 + (Math.random() - 0.5) * 0.04;
            sellRate = buyRate + 0.01 + Math.random() * 0.02;
          } else {
            buyRate = 35 + (Math.random() - 0.5) * 5;
            sellRate = buyRate + 0.5 + Math.random() * 1;
          }

          mockRates.push({
            id: i + 1,
            exchanger_id: exchanger.toLowerCase().replace(/\s+/g, '_'),
            exchanger_name: exchanger,
            currency_pair: pair,
            buy_rate: Math.round(buyRate * 10000) / 10000,
            sell_rate: Math.round(sellRate * 10000) / 10000,
            parsed_at: date.toISOString()
          });
        }

        return {
          success: true,
          data: {
            rates: mockRates,
            total_count: mockRates.length,
            filters: {
              currency_pair: filters.currency_pair,
              exchanger_id: filters.exchanger_id,
              days_back: filters.days,
              date_range: {
                start: new Date(Date.now() - filters.days * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                end: new Date().toISOString().split('T')[0]
              }
            },
            available_filters: {
              currency_pairs: [
                { currency_pair: "RUB/THB", records_count: 150 },
                { currency_pair: "THB/RUB", records_count: 120 },
                { currency_pair: "USDT/THB", records_count: 100 },
                { currency_pair: "USD/THB", records_count: 80 },
                { currency_pair: "EUR/THB", records_count: 60 }
              ],
              exchangers: [
                { exchanger_id: "superrich_thailand", exchanger_name: "SuperRich Thailand", records_count: 100 },
                { exchanger_id: "vasu_exchange", exchanger_name: "Vasu Exchange", records_count: 95 },
                { exchanger_id: "happy_rich_exchange", exchanger_name: "Happy Rich Exchange", records_count: 90 },
                { exchanger_id: "grand_superrich", exchanger_name: "Grand SuperRich", records_count: 85 },
                { exchanger_id: "siam_exchange", exchanger_name: "Siam Exchange", records_count: 80 }
              ]
            }
          }
        };
      }
    },
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });

  const handleFilterChange = (key: string, value: string | number) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatRate = (rate: number | null) => {
    if (rate === null || rate === undefined) return 'N/A';
    return rate.toFixed(4);
  };

  const exportToCSV = () => {
    if (!historicalData?.data.rates) return;

    const headers = ['Валютная пара', 'Обменник', 'Курс покупки', 'Курс продажи', 'Дата'];
    const csvContent = [
      headers.join(','),
      ...historicalData.data.rates.map(rate => [
        rate.currency_pair,
        rate.exchanger_name,
        formatRate(rate.buy_rate),
        formatRate(rate.sell_rate),
        formatDate(rate.parsed_at)
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `historical_rates_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (error) {
    console.error('Historical rates error:', error);
    return (
      <div className="bg-white rounded-xl shadow-lg p-8 text-center">
        <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-bold text-gray-800 mb-2">Ошибка загрузки данных</h2>
        <p className="text-gray-600 mb-4">
          Не удалось загрузить исторические данные курсов валют
        </p>
        <p className="text-sm text-gray-500 mb-4">
          Ошибка: {error.message}
        </p>
        <div className="space-x-2">
          <button
            onClick={() => refetch()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Попробовать снова
          </button>
          <button
            onClick={() => window.location.href = '/'}
            className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            На главную
          </button>
        </div>
      </div>
    );
  }

  // Проверяем, используются ли fallback данные
  const isUsingFallbackData = historicalData?.data?.rates?.some(rate =>
    rate.exchanger_name?.includes('SuperRich') ||
    rate.exchanger_name?.includes('Vasu') ||
    rate.exchanger_name?.includes('Happy Rich')
  ) && !historicalData?.data?.rates?.some(rate => rate.parsing_result_id);

  return (
    <div className="space-y-6">
      {/* Уведомление о тестовых данных */}
      {isUsingFallbackData && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-yellow-600" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">Используются тестовые данные</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Backend API недоступен. Отображаются демонстрационные данные для тестирования интерфейса.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="bg-gradient-to-br from-blue-500 to-purple-600 w-12 h-12 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-800">Исторические курсы валют</h1>
              <p className="text-gray-600">Анализ изменения курсов обменников во времени</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <Filter className="w-4 h-4" />
              <span>Фильтры</span>
            </button>
            <button
              onClick={() => refetch()}
              disabled={isLoading}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Обновить</span>
            </button>
            <button
              onClick={exportToCSV}
              disabled={!historicalData?.data.rates?.length}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Экспорт</span>
            </button>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="border-t pt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Валютная пара
              </label>
              <select
                value={filters.currency_pair}
                onChange={(e) => handleFilterChange('currency_pair', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Все пары</option>
                {currencyPairs?.map((pair: CurrencyPair) => (
                  <option key={pair.currency_pair} value={pair.currency_pair}>
                    {pair.currency_pair} ({pair.records_count} записей)
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Период (дней)
              </label>
              <select
                value={filters.days}
                onChange={(e) => handleFilterChange('days', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={1}>1 день</option>
                <option value={7}>7 дней</option>
                <option value={14}>14 дней</option>
                <option value={30}>30 дней</option>
                <option value={90}>90 дней</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Обменник
              </label>
              <select
                value={filters.exchanger_id}
                onChange={(e) => handleFilterChange('exchanger_id', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Все обменники</option>
                {historicalData?.data?.available_filters?.exchangers?.map((exchanger) => (
                  <option key={exchanger.exchanger_id} value={exchanger.exchanger_id}>
                    {exchanger.exchanger_name} ({exchanger.records_count} записей)
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Data Table */}
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-800">
              Исторические данные
              {historicalData?.data.total_count && (
                <span className="ml-2 text-sm text-gray-500">
                  ({historicalData.data.total_count} записей)
                </span>
              )}
            </h2>
            {historicalData?.data.filters && (
              <div className="text-sm text-gray-500">
                Период: {formatDate(historicalData.data.filters.date_range.start)} - {formatDate(historicalData.data.filters.date_range.end)}
              </div>
            )}
          </div>
        </div>

        {isLoading ? (
          <div className="p-8 text-center">
            <RefreshCw className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600">Загрузка исторических данных...</p>
          </div>
        ) : historicalData?.data.rates?.length ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Валютная пара
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Обменник
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Курс покупки
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Курс продажи
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Дата и время
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {historicalData.data.rates.map((rate, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {rate.currency_pair}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {rate.exchanger_name || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                      {formatRate(rate.buy_rate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                      {formatRate(rate.sell_rate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(rate.parsed_at)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="p-8 text-center">
            <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Нет данных</h3>
            <p className="text-gray-500">
              Исторические данные для выбранного периода не найдены
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default HistoricalRates;
