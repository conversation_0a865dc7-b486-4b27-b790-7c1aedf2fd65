import React from 'react';
import { Star, MapPin, Phone, Clock, TrendingUp, TrendingDown, ExternalLink, Shield, CheckCircle, Globe, MessageCircle, Banknote } from 'lucide-react';
import { Exchanger } from '../types';

interface ExchangerCardProps {
  exchanger: Exchanger;
  selectedCurrency: string;
  onViewDetails?: (id: number) => void;
  onViewReviews?: (id: number) => void;
}

const ExchangerCard: React.FC<ExchangerCardProps> = ({ 
  exchanger, 
  selectedCurrency, 
  onViewDetails,
  onViewReviews
}) => {
  const rate = exchanger.rates.find(r => r.currency === selectedCurrency);
  const thbRubRate = exchanger.rates.find(r => r.currency === 'THB/RUB');
  const thbUsdtRate = exchanger.rates.find(r => r.currency === 'THB/USDT');
  
  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-4 h-4 ${
              i < Math.floor(rating) 
                ? 'text-yellow-400 fill-current' 
                : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-2 text-sm font-semibold text-gray-700">
          {rating.toFixed(1)}
        </span>
      </div>
    );
  };

  const handleWebsiteClick = () => {
    if (exchanger.website) {
      console.log('Opening website:', exchanger.website);
      window.open(exchanger.website, '_blank', 'noopener,noreferrer');
    } else {
      console.warn('No website URL available for exchanger:', exchanger.name);
    }
  };

  const handleTelegramClick = () => {
    if (exchanger.telegramBot) {
      console.log('Opening Telegram bot:', exchanger.telegramBot);
      window.open(exchanger.telegramBot, '_blank', 'noopener,noreferrer');
    } else {
      console.warn('No Telegram bot URL available for exchanger:', exchanger.name);
    }
  };

  const formatCryptoRate = (rate: number, currency: string) => {
    if (currency.includes('THB/RUB')) {
      return rate.toFixed(3);
    } else if (currency.includes('THB/USDT')) {
      return rate.toFixed(4);
    } else if (currency.includes('USDT/BAHT')) {
      return rate.toFixed(2);
    }
    return rate.toFixed(2);
  };

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-100 overflow-hidden hover:shadow-lg transition-all duration-300">
      {/* Header with gradient */}
      <div className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-3 relative">
        <div className="absolute top-2 right-2">
          {exchanger.rating >= 4.5 && (
            <div className="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-bold flex items-center">
              <Shield className="w-3 h-3 mr-1" />
              ТОП
            </div>
          )}
        </div>
        
        <div className="text-white">
          <h3 className="text-base font-bold mb-1 leading-tight">{exchanger.name}</h3>
          <div className="flex items-center space-x-2 text-sm opacity-90">
            <MapPin className="w-4 h-4" />
            <span className="truncate">{exchanger.address}</span>
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Rating and Reviews */}
        <div className="flex items-center justify-between mb-3">
          {renderStars(exchanger.rating)}
          <span className="text-sm text-gray-500">
            {exchanger.reviewCount} отзывов
          </span>
        </div>

        {/* Crypto Rates Display */}
        <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-lg p-3 mb-3 border border-emerald-200">
          <div className="text-center mb-3">
            <div className="flex items-center justify-center space-x-2 text-sm font-medium text-gray-700 mb-2">
              <Banknote className="w-4 h-4 text-emerald-500" />
              <span>Курсы THB</span>
              {(!thbRubRate && !thbUsdtRate) && (
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  Курсы недоступны
                </span>
              )}
            </div>
          </div>
          
          <div className="space-y-2">
            {/* THB/RUB */}
            {thbRubRate && (
              <div className="bg-white rounded-lg p-2 border border-gray-200">
                <div className="text-xs text-gray-500 mb-1 text-center">THB/RUB</div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-center">
                    <div className="text-xs text-gray-500">Покупка</div>
                    <div className="font-bold text-green-600">
                      {formatCryptoRate(thbRubRate.buy, 'THB/RUB')}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-gray-500">Продажа</div>
                    <div className="font-bold text-red-600">
                      {formatCryptoRate(thbRubRate.sell, 'THB/RUB')}
                    </div>
                  </div>
                </div>
                <div className="mt-2 flex items-center justify-center">
                  <div className={`flex items-center space-x-1 text-xs ${
                    thbRubRate.change > 0 ? 'text-green-600' : 
                    thbRubRate.change < 0 ? 'text-red-600' : 'text-gray-500'
                  }`}>
                    {thbRubRate.change > 0 ? <TrendingUp className="w-3 h-3" /> : 
                     thbRubRate.change < 0 ? <TrendingDown className="w-3 h-3" /> : null}
                    <span className="font-semibold">
                      {thbRubRate.change > 0 ? '+' : ''}{thbRubRate.change.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            )}
            
            {/* THB/USDT */}
            {thbUsdtRate && (
              <div className="bg-white rounded-lg p-2 border border-gray-200">
                <div className="text-xs text-gray-500 mb-1 text-center">THB/USDT</div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-center">
                    <div className="text-xs text-gray-500">Покупка</div>
                    <div className="font-bold text-green-600">
                      {formatCryptoRate(thbUsdtRate.buy, 'THB/USDT')}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-gray-500">Продажа</div>
                    <div className="font-bold text-red-600">
                      {formatCryptoRate(thbUsdtRate.sell, 'THB/USDT')}
                    </div>
                  </div>
                </div>
                <div className="mt-2 flex items-center justify-center">
                  <div className={`flex items-center space-x-1 text-xs ${
                    thbUsdtRate.change > 0 ? 'text-green-600' : 
                    thbUsdtRate.change < 0 ? 'text-red-600' : 'text-gray-500'
                  }`}>
                    {thbUsdtRate.change > 0 ? <TrendingUp className="w-3 h-3" /> : 
                     thbUsdtRate.change < 0 ? <TrendingDown className="w-3 h-3" /> : null}
                    <span className="font-semibold">
                      {thbUsdtRate.change > 0 ? '+' : ''}{thbUsdtRate.change.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            )}
            
            {/* No rates available message */}
            {!thbRubRate && !thbUsdtRate && (
              <div className="bg-gray-100 rounded-lg p-3 text-center">
                <div className="text-sm text-gray-600">
                  Курсы THB временно недоступны
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Обратитесь в обменник для уточнения
                </div>
              </div>
            )}
          </div>
          
          <div className="mt-3 text-center">
            <div className="text-xs text-gray-500">
              Обновлено: {new Date().toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' })}
            </div>
          </div>
        </div>

        {/* Current Rate Display */}
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg p-3 mb-3">
          <div className="text-center">
            <div className="text-sm text-gray-600 mb-2">Курс {selectedCurrency}</div>
            {rate ? (
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-xs text-gray-500 mb-1">Покупка</div>
                  <div className="text-xl font-bold text-green-600">
                    {rate.buy.toFixed(selectedCurrency === 'THB/RUB' ? 3 : 2)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-gray-500 mb-1">Продажа</div>
                  <div className="text-xl font-bold text-red-600">
                    {rate.sell.toFixed(selectedCurrency === 'THB/RUB' ? 3 : 2)}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-gray-500 text-sm">
                Курс {selectedCurrency} недоступен
              </div>
            )}
            
            {/* Change indicator */}
            {rate && (
              <div className="mt-3 flex items-center justify-center">
                <div className={`flex items-center space-x-1 text-sm ${
                  (rate.change || 0) > 0 ? 'text-green-600' : 
                  (rate.change || 0) < 0 ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {(rate.change || 0) > 0 ? <TrendingUp className="w-4 h-4" /> : 
                   (rate.change || 0) < 0 ? <TrendingDown className="w-4 h-4" /> : null}
                  <span className="font-semibold">
                    {(rate.change || 0) > 0 ? '+' : ''}{(rate.change || 0).toFixed(2)}%
                  </span>
                  <span className="text-gray-500">за сутки</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div className="space-y-1 mb-3 text-xs">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Phone className="w-3 h-3 text-gray-500" />
            <span>{exchanger.phone}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Clock className="w-3 h-3 text-gray-500" />
            <span>{exchanger.hours}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          {/* Referral Links */}
          <div className="grid grid-cols-2 gap-2">
            {exchanger.website && (
              <button
                onClick={handleWebsiteClick}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-3 py-2 rounded-lg font-medium transition-all duration-200 text-xs flex items-center justify-center space-x-1 group"
              >
                <Globe className="w-3 h-3 group-hover:scale-110 transition-transform" />
                <span>Сайт</span>
              </button>
            )}
            {exchanger.telegramBot && (
              <button
                onClick={handleTelegramClick}
                className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white px-3 py-2 rounded-lg font-medium transition-all duration-200 text-xs flex items-center justify-center space-x-1 group"
              >
                <MessageCircle className="w-3 h-3 group-hover:scale-110 transition-transform" />
                <span>Telegram</span>
              </button>
            )}
          </div>
          
          {/* Additional Actions */}
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => onViewReviews?.(exchanger.id)}
              className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors text-xs flex items-center justify-center space-x-1"
            >
              <MessageCircle className="w-3 h-3" />
              <span>Отзывы</span>
            </button>
            <button
              onClick={() => window.open(`tel:${exchanger.phone}`, '_self')}
              className="bg-green-100 hover:bg-green-200 text-green-700 px-3 py-2 rounded-lg font-medium transition-colors text-xs flex items-center justify-center space-x-1"
            >
              <Phone className="w-3 h-3" />
              <span>Позвонить</span>
            </button>
          </div>
        </div>

        {/* Trust indicators */}
        <div className="mt-3 pt-2 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center space-x-1">
              <Shield className="w-3 h-3 text-green-500" />
              <span>Проверен</span>
            </div>
            <div className="flex items-center space-x-1">
              <CheckCircle className="w-3 h-3 text-blue-500" />
              <span>Лицензия</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExchangerCard;