<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐛 Отладка API админки</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: white; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; max-height: 400px; }
        .endpoint { font-family: monospace; background: #e9ecef; padding: 2px 4px; border-radius: 3px; }
        .status { padding: 5px 10px; border-radius: 4px; font-weight: bold; }
        .status.ok { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Отладка API админки</h1>
        
        <div class="card info">
            <h2>📋 Цель отладки</h2>
            <p>Эта страница поможет диагностировать проблемы с API endpoints для админки:</p>
            <ul>
                <li><strong>Результаты парсинга</strong> - <span class="endpoint">GET /api/v1/parsing/results</span></li>
                <li><strong>Тренды курсов</strong> - <span class="endpoint">GET /api/v1/parsing/historical-rates/trends</span></li>
            </ul>
        </div>

        <div class="card">
            <h2>🧪 Быстрые тесты</h2>
            <div class="grid">
                <button onclick="testParsingResults()">Тест результатов парсинга</button>
                <button onclick="testRateTrends()">Тест трендов курсов</button>
                <button onclick="testBothEndpoints()">Тест обоих endpoints</button>
                <button onclick="clearResults()">Очистить результаты</button>
            </div>
        </div>

        <div id="results"></div>

        <div class="card">
            <h2>🔗 Прямые ссылки</h2>
            <div class="grid">
                <a href="http://localhost:8000/health" target="_blank">Health Check</a>
                <a href="http://localhost:8000/api/v1/parsing/results" target="_blank">Parsing Results</a>
                <a href="http://localhost:8000/api/v1/parsing/historical-rates/trends" target="_blank">Rate Trends</a>
                <a href="http://localhost:5174/" target="_blank">Админка</a>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="card ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function testEndpoint(url, name) {
            try {
                log(`🔄 Тестирование: ${name}<br>URL: <span class="endpoint">${url}</span>`, 'info');
                
                const startTime = Date.now();
                const response = await fetch(url);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                log(`📊 Ответ получен за ${responseTime}ms<br>Status: ${response.status} ${response.statusText}`, 'info');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error(`Неверный Content-Type: ${contentType}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    const dataCount = data.data?.results?.length || data.data?.trends?.length || 0;
                    log(`✅ ${name} - УСПЕХ<br>
                        📈 Записей получено: ${dataCount}<br>
                        ⏱️ Время ответа: ${responseTime}ms<br>
                        <details><summary>Первые 3 записи</summary><pre>${JSON.stringify(data.data?.results?.slice(0, 3) || data.data?.trends?.slice(0, 3) || [], null, 2)}</pre></details>`, 'success');
                } else {
                    throw new Error(`API вернул success: false`);
                }
                
            } catch (error) {
                log(`❌ ${name} - ОШИБКА<br>
                    🚨 Error: ${error.message}<br>
                    🔍 Проверьте, что сервер запущен на порту 8000`, 'error');
            }
        }

        async function testParsingResults() {
            await testEndpoint(`${API_BASE_URL}/parsing/results`, 'Результаты парсинга');
        }

        async function testRateTrends() {
            await testEndpoint(`${API_BASE_URL}/parsing/historical-rates/trends?currency_pair=RUB/THB&days_back=30&interval=daily`, 'Тренды курсов');
        }

        async function testBothEndpoints() {
            clearResults();
            log('🚀 Запуск комплексного тестирования...', 'info');
            
            await testParsingResults();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testRateTrends();
            
            log('🎉 Комплексное тестирование завершено!', 'success');
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Автоматический тест при загрузке
        window.addEventListener('load', () => {
            log('🔧 Страница отладки API админки загружена', 'info');
            
            // Проверяем доступность сервера
            setTimeout(async () => {
                try {
                    const response = await fetch('http://localhost:8000/health');
                    if (response.ok) {
                        const data = await response.json();
                        log(`✅ Сервер доступен<br>Status: ${data.status}<br>Server: ${data.server}`, 'success');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    log(`❌ Сервер недоступен<br>Error: ${error.message}<br>🔧 Убедитесь, что сервер запущен: <code>python new_server.py</code>`, 'error');
                }
            }, 1000);
        });

        // Мониторинг состояния сервера
        setInterval(async () => {
            try {
                const response = await fetch('http://localhost:8000/health');
                const statusElement = document.querySelector('.server-status') || (() => {
                    const element = document.createElement('div');
                    element.className = 'server-status';
                    document.querySelector('h1').after(element);
                    return element;
                })();
                
                if (response.ok) {
                    statusElement.innerHTML = '<span class="status ok">🟢 Сервер работает</span>';
                } else {
                    statusElement.innerHTML = '<span class="status error">🔴 Сервер недоступен</span>';
                }
            } catch (error) {
                const statusElement = document.querySelector('.server-status') || (() => {
                    const element = document.createElement('div');
                    element.className = 'server-status';
                    document.querySelector('h1').after(element);
                    return element;
                })();
                statusElement.innerHTML = '<span class="status error">🔴 Сервер недоступен</span>';
            }
        }, 5000);
    </script>
</body>
</html>
