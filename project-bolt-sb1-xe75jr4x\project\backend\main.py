"""
FastAPI main application
"""
import logging
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse

from config.settings import settings
from api.routes import exchangers, admin, auth, rates, users
from api.routes import parsing
from api import parsing_results
from services.exchange_service import exchange_service
from services.cache_service import cache_service
from services.google_sheets_service import google_sheets_service
from services.rate_parser_service import rate_parser_service
from services.scheduler_service import scheduler_service
from services.database_service import database_service

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting Thailand Exchange Platform API")
    
    # Initialize services
    await cache_service.initialize()
    await google_sheets_service.initialize()
    await database_service.initialize()
    await rate_parser_service.initialize()
    
    # Start scheduler for automatic parsing
    scheduler_service.start()
    
    # Load initial data
    try:
        await exchange_service.get_exchange_rates(force_refresh=True)
        logger.info("Initial exchange rates loaded successfully")
    except Exception as e:
        logger.error(f"Failed to load initial exchange rates: {e}")
    
    yield
    
    # Shutdown
    scheduler_service.stop()
    await rate_parser_service.close()
    await database_service.close()
    logger.info("Shutting down Thailand Exchange Platform API")


# Create FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="API for Thailand currency exchange platform",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add trusted host middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Configure properly for production
)

# Include routers
app.include_router(
    auth.router,
    prefix=f"{settings.API_V1_STR}/auth",
    tags=["auth"]
)

app.include_router(
    exchangers.router,
    prefix=f"{settings.API_V1_STR}/exchangers",
    tags=["exchangers"]
)

app.include_router(
    admin.router,
    prefix=f"{settings.API_V1_STR}/admin",
    tags=["admin"]
)

app.include_router(
    parsing.router,
    prefix=f"{settings.API_V1_STR}/parsing",
    tags=["parsing"]
)

app.include_router(
    parsing_results.router,
    tags=["parsing-results"]
)

app.include_router(
    rates.router,
    prefix=f"{settings.API_V1_STR}/rates",
    tags=["rates"]
)

app.include_router(
    users.router,
    prefix=f"{settings.API_V1_STR}/users",
    tags=["users"]
)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Thailand Exchange Platform API",
        "version": settings.APP_VERSION,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Quick health checks
        cache_info = await cache_service.get_cache_info()
        
        return {
            "status": "healthy",
            "timestamp": "2024-01-20T10:00:00Z",
            "services": {
                "cache": "up" if cache_info.get('exists') is not None else "down",
                "google_sheets": "up"  # Simplified check
            }
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error",
            "detail": str(exc) if settings.DEBUG else "An error occurred"
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )