"""
Main exchange service that combines Google Sheets and caching
"""
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import asyncio

from services.google_sheets_service import google_sheets_service
from services.cache_service import cache_service
from config.settings import settings

logger = logging.getLogger(__name__)


class ExchangeService:
    """Main service for managing exchange rates"""
    
    def __init__(self):
        self.last_update = None
        self.update_interval = timedelta(minutes=settings.RATES_UPDATE_INTERVAL_MINUTES)
    
    async def get_exchange_rates(self, force_refresh: bool = False) -> Optional[Dict[str, Any]]:
        """Get exchange rates with caching"""
        try:
            # Check if we need to refresh
            if not force_refresh:
                cached_data = await cache_service.get_exchange_rates()
                if cached_data:
                    return cached_data
            
            # Fetch fresh data from Google Sheets
            logger.info("Fetching fresh exchange rates from Google Sheets")
            fresh_data = await google_sheets_service.fetch_exchange_rates()
            
            if fresh_data:
                # Cache the fresh data
                await cache_service.set_exchange_rates(fresh_data)
                self.last_update = datetime.now()
                return fresh_data
            else:
                # If Google Sheets fails, try to return cached data
                logger.warning("Google Sheets fetch failed, trying cache")
                return await cache_service.get_exchange_rates()
                
        except Exception as e:
            logger.error(f"Error in get_exchange_rates: {e}")
            return None
    
    async def get_exchanger_by_id(self, exchanger_id: int) -> Optional[Dict[str, Any]]:
        """Get specific exchanger by ID"""
        rates_data = await self.get_exchange_rates()
        if not rates_data or 'exchangers' not in rates_data:
            return None
        
        # In a real app, you'd have proper IDs. For now, use index
        exchangers = rates_data['exchangers']
        if 0 <= exchanger_id < len(exchangers):
            return exchangers[exchanger_id]
        
        return None
    
    async def get_top_exchangers(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top-rated exchangers"""
        rates_data = await self.get_exchange_rates()
        if not rates_data or 'exchangers' not in rates_data:
            return []
        
        exchangers = rates_data['exchangers']
        
        # Sort by best rates (for demo, we'll use a simple scoring)
        def calculate_score(exchanger):
            rates = exchanger.get('rates', {})
            score = 0
            
            # Score based on competitive rates
            for currency, rate_data in rates.items():
                if 'buy' in rate_data and 'sell' in rate_data:
                    spread = rate_data['sell'] - rate_data['buy']
                    # Lower spread is better
                    score += max(0, 10 - spread)
            
            return score
        
        sorted_exchangers = sorted(
            exchangers, 
            key=calculate_score, 
            reverse=True
        )
        
        return sorted_exchangers[:limit]
    
    async def search_exchangers(self, query: str, district: str = None) -> List[Dict[str, Any]]:
        """Search exchangers by name or location"""
        rates_data = await self.get_exchange_rates()
        if not rates_data or 'exchangers' not in rates_data:
            return []
        
        exchangers = rates_data['exchangers']
        results = []
        
        query_lower = query.lower() if query else ""
        
        for exchanger in exchangers:
            # Check name match
            name_match = query_lower in exchanger.get('name', '').lower()
            
            # Check district match
            district_match = (
                not district or 
                district == 'all' or 
                exchanger.get('district', '').lower() == district.lower()
            )
            
            # Check address match
            address_match = query_lower in exchanger.get('address', '').lower()
            
            if (name_match or address_match) and district_match:
                results.append(exchanger)
        
        return results
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get system status information"""
        try:
            # Test Google Sheets connection
            sheets_status = await google_sheets_service.test_connection()
            
            # Get cache info
            cache_info = await cache_service.get_cache_info()
            
            # Get last update info
            rates_data = await cache_service.get_exchange_rates()
            last_update = None
            total_exchangers = 0
            
            if rates_data:
                last_update = rates_data.get('last_updated')
                total_exchangers = rates_data.get('total_count', 0)
            
            return {
                'google_sheets': sheets_status,
                'cache': cache_info,
                'last_update': last_update,
                'total_exchangers': total_exchangers,
                'update_interval_minutes': settings.RATES_UPDATE_INTERVAL_MINUTES,
                'system_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                'error': str(e),
                'system_time': datetime.now().isoformat()
            }
    
    async def force_update_rates(self) -> Dict[str, Any]:
        """Force update exchange rates from Google Sheets"""
        try:
            logger.info("Force updating exchange rates")
            
            # Clear cache first
            await cache_service.clear_cache()
            
            # Fetch fresh data
            fresh_data = await self.get_exchange_rates(force_refresh=True)
            
            if fresh_data:
                return {
                    'success': True,
                    'message': 'Exchange rates updated successfully',
                    'total_exchangers': fresh_data.get('total_count', 0),
                    'updated_at': fresh_data.get('last_updated')
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to update exchange rates'
                }
                
        except Exception as e:
            logger.error(f"Error in force_update_rates: {e}")
            return {
                'success': False,
                'message': f'Error updating rates: {str(e)}'
            }


# Global instance
exchange_service = ExchangeService()