<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration</title>
</head>
<body>
    <h1>Test Registration</h1>
    <form id="testForm">
        <div>
            <label>Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div>
            <label>Password:</label>
            <input type="password" id="password" value="test123456" required>
        </div>
        <div>
            <label>First Name:</label>
            <input type="text" id="firstName" value="Test">
        </div>
        <div>
            <label>Last Name:</label>
            <input type="text" id="lastName" value="User">
        </div>
        <button type="submit">Test Registration</button>
    </form>
    
    <div id="result"></div>

    <script>
        // Test Supabase connection directly
        async function testSupabase() {
            try {
                const response = await fetch('https://uwpexmzzqnohjckjqbhe.supabase.co/rest/v1/', {
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV3cGV4bXp6cW5vaGpja2pxYmhlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQxNzE5NjIsImV4cCI6MjA2OTc0Nzk2Mn0.I1gU6-o6aDkjwu9OVyIXyHMqa4URi-GniO0Nogs1zHA'
                    }
                });
                console.log('Supabase connection test:', response.status, response.statusText);
                return response.ok;
            } catch (error) {
                console.error('Supabase connection error:', error);
                return false;
            }
        }

        // Test backend API
        async function testBackendAPI() {
            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'test123456',
                        name: 'Test User'
                    })
                });
                console.log('Backend API test:', response.status, response.statusText);
                const result = await response.json();
                console.log('Backend API result:', result);
                return response.ok;
            } catch (error) {
                console.error('Backend API error:', error);
                return false;
            }
        }

        document.getElementById('testForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';

            console.log('=== Starting Registration Tests ===');
            
            // Test Supabase connection
            const supabaseOk = await testSupabase();
            console.log('Supabase connection:', supabaseOk ? 'OK' : 'FAILED');
            
            // Test Backend API
            const backendOk = await testBackendAPI();
            console.log('Backend API:', backendOk ? 'OK' : 'FAILED');
            
            resultDiv.innerHTML = `
                <h3>Test Results:</h3>
                <p>Supabase Connection: ${supabaseOk ? '✅ OK' : '❌ FAILED'}</p>
                <p>Backend API: ${backendOk ? '✅ OK' : '❌ FAILED'}</p>
                <p>Check browser console for detailed logs</p>
            `;
        });

        // Run tests on page load
        window.addEventListener('load', () => {
            console.log('Page loaded, running connection tests...');
            testSupabase();
            testBackendAPI();
        });
    </script>
</body>
</html>
