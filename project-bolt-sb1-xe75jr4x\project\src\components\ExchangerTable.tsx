import React from 'react';
import { TrendingUp, TrendingDown, Globe, MessageCircle, Building, MapPin, Star, Phone, Clock } from 'lucide-react';
import { Exchanger } from '../types';
import { districts } from '../data/mockData';
import { useExchangers } from '../hooks/useExchangeData';

interface ExchangerTableProps {
  exchangers: Exchanger[];
  selectedCurrency: string;
  onViewReviews?: (id: number) => void;
}

const ExchangerTable: React.FC<ExchangerTableProps> = ({ exchangers, selectedCurrency, onViewReviews }) => {
  const [sortConfig, setSortConfig] = React.useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  // Get services from real data
  const { data: exchangersData } = useExchangers({ limit: 100 });
  const allExchangers = exchangersData?.exchangers || [];
  
  // Group exchangers by service (simplified version)
  const services = React.useMemo(() => {
    const serviceMap = new Map();
    
    allExchangers.forEach(exchanger => {
      const serviceId = exchanger.serviceId || `single-${exchanger.id}`;
      
      if (!serviceMap.has(serviceId)) {
        serviceMap.set(serviceId, {
          serviceId,
          serviceName: exchanger.name.split(' - ')[0],
          totalOffices: 0,
          mainOffice: exchanger,
          branches: [],
          averageRating: exchanger.rating,
          totalReviews: exchanger.reviewCount,
          website: exchanger.website,
          telegramBot: exchanger.telegramBot
        });
      }
      
      const service = serviceMap.get(serviceId);
      service.totalOffices++;
      
      if (exchanger.isMainOffice) {
        service.mainOffice = exchanger;
      } else {
        service.branches.push(exchanger);
      }
    });
    
    return Array.from(serviceMap.values());
  }, [allExchangers]);
  
  // Filter services based on exchangers list
  const filteredServices = services.filter(service => 
    exchangers.some(exchanger => exchanger.serviceId === service.serviceId)
  );

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handleKeyDown = (event: React.KeyboardEvent, action: () => void) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      action();
    }
  };

  const getSortAriaLabel = (columnName: string, currentSort?: { key: string; direction: 'asc' | 'desc' } | null) => {
    if (currentSort && currentSort.key === columnName) {
      return `Сортировать по ${columnName} в ${currentSort.direction === 'asc' ? 'убывающем' : 'возрастающем'} порядке`;
    }
    return `Сортировать по ${columnName}`;
  };
  const getDistrictName = (districtId: string) => {
    return districts.find(d => d.id === districtId)?.name || districtId;
  };

  const getOfficeTypeIcon = (officeType?: string) => {
    switch (officeType) {
      case 'main': return '🏢';
      case 'branch': return '🏪';
      case 'kiosk': return '🏬';
      default: return '📍';
    }
  };

  const getOfficeTypeName = (officeType?: string) => {
    switch (officeType) {
      case 'main': return 'Главный офис';
      case 'branch': return 'Филиал';
      case 'kiosk': return 'Киоск';
      default: return 'Офис';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-slate-200 overflow-hidden" role="region" aria-label="Таблица обменников валют">
      <div className="overflow-x-auto">
        <table className="w-full" role="table" aria-label="Курсы валют обменников">
          <caption className="sr-only">
            Таблица курсов валют обменников с возможностью сортировки. 
            Показано {filteredServices.length} обменных сетей. 
            Используйте клавиши Tab для навигации и Enter или пробел для сортировки.
          </caption>
          <thead className="bg-gradient-to-r from-slate-50 to-slate-100" role="rowgroup">
            <tr>
              <th 
                className="px-4 py-3 text-left text-sm font-semibold text-slate-700 cursor-pointer hover:bg-slate-200 focus:bg-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                scope="col"
                tabIndex={0}
                onClick={() => handleSort('name')}
                onKeyDown={(e) => handleKeyDown(e, () => handleSort('name'))}
                aria-sort={sortConfig?.key === 'name' ? sortConfig.direction === 'asc' ? 'ascending' : 'descending' : 'none'}
                aria-label={getSortAriaLabel('названию обменника', sortConfig)}
              >
                <div className="flex items-center space-x-1">
                  <span>Обменная сеть / Офис</span>
                  {sortConfig?.key === 'name' && (
                    <span aria-hidden="true">
                      {sortConfig.direction === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th 
                className="px-3 py-3 text-center text-sm font-semibold text-slate-700 cursor-pointer hover:bg-slate-200 focus:bg-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                scope="col"
                tabIndex={0}
                onClick={() => handleSort('thb_rub_buy')}
                onKeyDown={(e) => handleKeyDown(e, () => handleSort('thb_rub_buy'))}
                aria-sort={sortConfig?.key === 'thb_rub_buy' ? sortConfig.direction === 'asc' ? 'ascending' : 'descending' : 'none'}
                aria-label={getSortAriaLabel('курсу покупки THB/RUB', sortConfig)}
              >
                <div className="flex flex-col items-center">
                  <div className="flex items-center space-x-1">
                    <span>THB/RUB</span>
                    {sortConfig?.key === 'thb_rub_buy' && (
                      <span aria-hidden="true">
                        {sortConfig.direction === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-slate-500">Покупка</span>
                </div>
              </th>
              <th 
                className="px-3 py-3 text-center text-sm font-semibold text-slate-700 cursor-pointer hover:bg-slate-200 focus:bg-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                scope="col"
                tabIndex={0}
                onClick={() => handleSort('thb_rub_sell')}
                onKeyDown={(e) => handleKeyDown(e, () => handleSort('thb_rub_sell'))}
                aria-sort={sortConfig?.key === 'thb_rub_sell' ? sortConfig.direction === 'asc' ? 'ascending' : 'descending' : 'none'}
                aria-label={getSortAriaLabel('курсу продажи THB/RUB', sortConfig)}
              >
                <div className="flex flex-col items-center">
                  <div className="flex items-center space-x-1">
                    <span>THB/RUB</span>
                    {sortConfig?.key === 'thb_rub_sell' && (
                      <span aria-hidden="true">
                        {sortConfig.direction === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-slate-500">Продажа</span>
                </div>
              </th>
              <th 
                className="px-3 py-3 text-center text-sm font-semibold text-slate-700 cursor-pointer hover:bg-slate-200 focus:bg-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                scope="col"
                tabIndex={0}
                onClick={() => handleSort('usdt_baht_buy')}
                onKeyDown={(e) => handleKeyDown(e, () => handleSort('usdt_baht_buy'))}
                aria-sort={sortConfig?.key === 'usdt_baht_buy' ? sortConfig.direction === 'asc' ? 'ascending' : 'descending' : 'none'}
                aria-label={getSortAriaLabel('курсу покупки USDT/BAHT', sortConfig)}
              >
                <div className="flex flex-col items-center">
                  <div className="flex items-center space-x-1">
                    <span>USDT/BAHT</span>
                    {sortConfig?.key === 'usdt_baht_buy' && (
                      <span aria-hidden="true">
                        {sortConfig.direction === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-slate-500">Покупка</span>
                </div>
              </th>
              <th 
                className="px-3 py-3 text-center text-sm font-semibold text-slate-700 cursor-pointer hover:bg-slate-200 focus:bg-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                scope="col"
                tabIndex={0}
                onClick={() => handleSort('usdt_baht_sell')}
                onKeyDown={(e) => handleKeyDown(e, () => handleSort('usdt_baht_sell'))}
                aria-sort={sortConfig?.key === 'usdt_baht_sell' ? sortConfig.direction === 'asc' ? 'ascending' : 'descending' : 'none'}
                aria-label={getSortAriaLabel('курсу продажи USDT/BAHT', sortConfig)}
              >
                <div className="flex flex-col items-center">
                  <div className="flex items-center space-x-1">
                    <span>USDT/BAHT</span>
                    {sortConfig?.key === 'usdt_baht_sell' && (
                      <span aria-hidden="true">
                        {sortConfig.direction === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-slate-500">Продажа</span>
                </div>
              </th>
              <th 
                className="px-3 py-3 text-center text-sm font-semibold text-slate-700 cursor-pointer hover:bg-slate-200 focus:bg-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                scope="col"
                tabIndex={0}
                onClick={() => handleSort('change')}
                onKeyDown={(e) => handleKeyDown(e, () => handleSort('change'))}
                aria-sort={sortConfig?.key === 'change' ? sortConfig.direction === 'asc' ? 'ascending' : 'descending' : 'none'}
                aria-label={getSortAriaLabel('изменению курса', sortConfig)}
              >
                <div className="flex items-center justify-center space-x-1">
                  <span>Изменение</span>
                  {sortConfig?.key === 'change' && (
                    <span aria-hidden="true">
                      {sortConfig.direction === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th className="px-4 py-3 text-left text-sm font-semibold text-slate-700" scope="col">
                Контакты
              </th>
              <th className="px-4 py-3 text-center text-sm font-semibold text-slate-700" scope="col">
                Действия
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-slate-100" role="rowgroup">
            {filteredServices.map((service) => {
              const allOffices = [service.mainOffice, ...service.branches];
              const serviceOffices = allOffices.filter(office => 
                exchangers.some(exchanger => exchanger.id === office.id)
              );

              return (
                <React.Fragment key={service.serviceId}>
                  {/* Service Header Row (if multiple offices) */}
                  {service.totalOffices > 1 && (
                    <tr className="bg-slate-50 border-l-4 border-teal-500" role="row">
                      <td colSpan={8} className="px-4 py-3" role="cell">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Building className="w-5 h-5 text-teal-600" />
                            <div>
                              <h3 className="font-bold text-slate-800 text-lg" id={`service-${service.serviceId}`}>
                                {service.serviceName}
                              </h3>
                              <div className="flex items-center space-x-4 text-sm text-slate-600">
                                <span className="flex items-center space-x-1">
                                  <MapPin className="w-4 h-4" />
                                  <span aria-label={`${service.totalOffices} офисов в сети`}>
                                    {service.totalOffices} офисов
                                  </span>
                                </span>
                                <span className="flex items-center space-x-1">
                                  <Star className="w-4 h-4 text-amber-500" />
                                  <span aria-label={`Рейтинг ${service.averageRating.toFixed(1)} из 5, основан на ${service.totalReviews} отзывах`}>
                                    {service.averageRating.toFixed(1)} ({service.totalReviews} отзывов)
                                  </span>
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {service.website && (
                              <button
                                onClick={() => window.open(service.website, '_blank', 'noopener,noreferrer')}
                                className="bg-slate-600 hover:bg-slate-700 focus:bg-slate-700 text-white px-3 py-1 rounded-lg text-xs flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                aria-label={`Открыть сайт сети ${service.serviceName} в новой вкладке`}
                              >
                                <Globe className="w-3 h-3" />
                                <span>Сайт сети</span>
                              </button>
                            )}
                            {service.telegramBot && (
                              <button
                                onClick={() => window.open(service.telegramBot, '_blank', 'noopener,noreferrer')}
                                className="bg-teal-500 hover:bg-teal-600 focus:bg-teal-600 text-white px-3 py-1 rounded-lg text-xs flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                aria-label={`Открыть Telegram бот сети ${service.serviceName} в новой вкладке`}
                              >
                                <MessageCircle className="w-3 h-3" />
                                <span>Telegram</span>
                              </button>
                            )}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}

                  {/* Individual Office Rows */}
                  {serviceOffices.map((exchanger, index) => {
                    const thbRubRate = exchanger.rates.find(r => r.currency === 'THB/RUB');
                    const usdtBahtRate = exchanger.rates.find(r => r.currency === 'USDT/BAHT');
                    const district = districts.find(d => d.id === exchanger.district);
                    const isSubOffice = service.totalOffices > 1 && !exchanger.isMainOffice;
                    
                    return (
                      <tr 
                        key={exchanger.id} 
                        className={`hover:bg-gray-50 focus-within:bg-gray-50 transition-colors ${
                          isSubOffice ? 'bg-slate-25' : ''
                        }`}
                        role="row"
                        aria-describedby={service.totalOffices > 1 ? `service-${service.serviceId}` : undefined}
                      >
                        <td className="px-4 py-4" role="cell">
                          <div className={`${isSubOffice ? 'ml-6' : ''}`}>
                            <div className="flex items-center space-x-2">
                              <span className="text-lg" aria-hidden="true">{getOfficeTypeIcon(exchanger.officeType)}</span>
                              <div>
                                <div className="font-semibold text-slate-800 text-base" id={`exchanger-${exchanger.id}`}>
                                  {service.totalOffices > 1 ? (
                                    <span>
                                      {getOfficeTypeName(exchanger.officeType)}
                                      {exchanger.officeType !== 'main' && ` - ${district?.name}`}
                                    </span>
                                  ) : (
                                    exchanger.name
                                  )}
                                </div>
                                <div className="text-sm text-slate-500" aria-label={`Адрес: ${exchanger.address}`}>
                                  {exchanger.address}
                                </div>
                                <div className="flex items-center mt-1">
                                  <div className="flex text-amber-400" role="img" aria-label={`Рейтинг ${exchanger.rating.toFixed(1)} из 5 звезд`}>
                                    {[...Array(5)].map((_, i) => (
                                      <span 
                                        key={i} 
                                        className={i < Math.floor(exchanger.rating) ? 'text-amber-400' : 'text-slate-300'}
                                        aria-hidden="true"
                                      >
                                        ★
                                      </span>
                                    ))}
                                  </div>
                                  <span className="text-sm text-slate-500 ml-2" aria-label={`${exchanger.reviewCount} отзывов`}>
                                    {exchanger.rating.toFixed(1)} ({exchanger.reviewCount})
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </td>
                        
                        {/* THB/RUB Buy */}
                        <td className="px-3 py-4 text-center" role="cell">
                          <div 
                            className="text-lg font-bold text-emerald-600"
                            aria-label={`Курс покупки THB/RUB: ${thbRubRate?.buy.toFixed(3) || 'не указан'}`}
                          >
                            {thbRubRate?.buy.toFixed(3) || '—'}
                          </div>
                        </td>
                        
                        {/* THB/RUB Sell */}
                        <td className="px-3 py-4 text-center" role="cell">
                          <div 
                            className="text-lg font-bold text-rose-600"
                            aria-label={`Курс продажи THB/RUB: ${thbRubRate?.sell.toFixed(3) || 'не указан'}`}
                          >
                            {thbRubRate?.sell.toFixed(3) || '—'}
                          </div>
                        </td>
                        
                        {/* USDT/RUB Buy */}
                        {/* USDT/BAHT Buy */}
                        <td className="px-3 py-4 text-center" role="cell">
                          <div 
                            className="text-lg font-bold text-emerald-600"
                            aria-label={`Курс покупки USDT/BAHT: ${usdtBahtRate?.buy.toFixed(2) || 'не указан'}`}
                          >
                            {usdtBahtRate?.buy.toFixed(2) || '—'}
                          </div>
                        </td>
                        
                        {/* USDT/BAHT Sell */}
                        <td className="px-3 py-4 text-center" role="cell">
                          <div 
                            className="text-lg font-bold text-rose-600"
                            aria-label={`Курс продажи USDT/BAHT: ${usdtBahtRate?.sell.toFixed(2) || 'не указан'}`}
                          >
                            {usdtBahtRate?.sell.toFixed(2) || '—'}
                          </div>
                        </td>
                        
                        {/* Change */}
                        <td className="px-3 py-4 text-center" role="cell">
                          <div className={`flex items-center justify-center space-x-1 ${
                            (thbRubRate?.change || 0) > 0 ? 'text-emerald-600' : 
                            (thbRubRate?.change || 0) < 0 ? 'text-rose-600' : 'text-slate-500'
                          }`}
                          aria-label={`Изменение курса: ${(thbRubRate?.change || 0) > 0 ? 'рост' : (thbRubRate?.change || 0) < 0 ? 'падение' : 'без изменений'} на ${Math.abs(thbRubRate?.change || 0).toFixed(2)} процентов`}
                          >
                            {(thbRubRate?.change || 0) > 0 ? <TrendingUp className="w-4 h-4" /> : 
                             (thbRubRate?.change || 0) < 0 ? <TrendingDown className="w-4 h-4" /> : null}
                            <span className="font-semibold" aria-hidden="true">
                              {(thbRubRate?.change || 0) > 0 ? '+' : ''}{(thbRubRate?.change || 0).toFixed(2)}%
                            </span>
                          </div>
                        </td>
                        
                        {/* Contacts */}
                        <td className="px-4 py-4" role="cell">
                          <div className="text-sm text-slate-600">
                            <div className="flex items-center space-x-1 font-medium">
                              <Phone className="w-3 h-3" aria-hidden="true" />
                              <a 
                                href={`tel:${exchanger.phone}`}
                                className="hover:text-blue-600 focus:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
                                aria-label={`Позвонить по номеру ${exchanger.phone}`}
                              >
                                {exchanger.phone}
                              </a>
                            </div>
                            <div className="flex items-center space-x-1 text-slate-500 mt-1">
                              <Clock className="w-3 h-3" aria-hidden="true" />
                              <span aria-label={`Часы работы: ${exchanger.hours}`}>
                                {exchanger.hours}
                              </span>
                            </div>
                          </div>
                        </td>
                        
                        {/* Actions */}
                        <td className="px-4 py-4" role="cell">
                          <div className="flex flex-col space-y-2">
                            <button
                              onClick={() => onViewReviews?.(exchanger.id)}
                              className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 focus:from-emerald-700 focus:to-teal-700 text-white px-3 py-2 rounded-lg font-medium transition-all duration-200 text-xs flex items-center justify-center space-x-1 cursor-pointer focus:outline-none focus:ring-2 focus:ring-white"
                              aria-label={`Посмотреть отзывы об обменнике ${exchanger.name}`}
                            >
                              <MessageCircle className="w-3 h-3" aria-hidden="true" />
                              <span>Отзывы</span>
                            </button>
                            {exchanger.website && (
                              <button
                                onClick={() => window.open(exchanger.website, '_blank', 'noopener,noreferrer')}
                                className="bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 focus:from-slate-700 focus:to-slate-800 text-white px-3 py-2 rounded-lg font-medium transition-all duration-200 text-xs flex items-center justify-center space-x-1 focus:outline-none focus:ring-2 focus:ring-white"
                                aria-label={`Открыть сайт обменника ${exchanger.name} в новой вкладке`}
                              >
                                <Globe className="w-3 h-3" aria-hidden="true" />
                                <span>Сайт</span>
                              </button>
                            )}
                            {exchanger.telegramBot && (
                              <button
                                onClick={() => window.open(exchanger.telegramBot, '_blank', 'noopener,noreferrer')}
                                className="bg-gradient-to-r from-teal-500 to-cyan-500 hover:from-teal-600 hover:to-cyan-600 focus:from-teal-600 focus:to-cyan-600 text-white px-3 py-2 rounded-lg font-medium transition-all duration-200 text-xs flex items-center justify-center space-x-1 focus:outline-none focus:ring-2 focus:ring-white"
                                aria-label={`Открыть Telegram бот обменника ${exchanger.name} в новой вкладке`}
                              >
                                <MessageCircle className="w-3 h-3" aria-hidden="true" />
                                <span>Telegram</span>
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </React.Fragment>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ExchangerTable;