import React, { useState, useEffect } from 'react';
import { Building, Plus, Search, Filter, Edit, Trash2, Eye, MapPin, Phone, Clock, Star, Globe, MessageCircle, AlertCircle, CheckCircle, RefreshCw, TrendingUp, TrendingDown, Banknote, X, Save, DollarSign } from 'lucide-react';
import { ExchangerManagement as ExchangerType, ExchangerFormData } from '../../types/admin';
import { adminAPI } from '../../data/adminData';
import { processIntelligentRates } from '../../utils/rateProcessor';
import ExchangerForm from './ExchangerForm';

const ExchangerManagement: React.FC = () => {
  const [exchangers, setExchangers] = useState<ExchangerType[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'pending'>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedExchanger, setSelectedExchanger] = useState<ExchangerType | null>(null);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
  } | null>(null);
  const [editingRates, setEditingRates] = useState<{[key: string]: boolean}>({});
  const [tempRates, setTempRates] = useState<{[key: string]: any}>({});
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    loadExchangers();
    setLastUpdate(new Date());
  }, [currentPage, searchQuery, statusFilter]);

  // Listen for data updates from rate manager
  useEffect(() => {
    const handleDataUpdate = (event: CustomEvent) => {
      console.log('ExchangerManagement received data update:', event.detail);
      loadExchangers();
      setLastUpdate(new Date());
      
      if (event.detail.source) {
        const sourceMessages = {
          'excel_upload': 'Курсы обновлены из Excel файла',
          'ai_parsing': 'Курсы обновлены через ИИ парсинг',
          'manual_edit': 'Курсы обновлены вручную'
        };
        const message = sourceMessages[event.detail.source as keyof typeof sourceMessages] || 'Данные обновлены';
        showNotification('info', message);
      }
    };

    window.addEventListener('exchangerDataUpdated', handleDataUpdate as EventListener);
    
    return () => {
      window.removeEventListener('exchangerDataUpdated', handleDataUpdate as EventListener);
    };
  }, []);

  const loadExchangers = async () => {
    setLoading(true);
    try {
      const response = await adminAPI.getExchangers(currentPage, 20, statusFilter);
      
      // Enrich exchangers with current rates from main page data
      const enrichedExchangers = await Promise.all(
        response.exchangers.map(async (exchanger) => {
          try {
            // Get current rates from main page data
            const { getActiveExchangersForMainPage } = await import('../../data/adminData');
            const mainPageExchangers = getActiveExchangersForMainPage();
            const mainPageExchanger = mainPageExchangers.find((e: any) => e.id.toString() === exchanger.id);
            
            return {
              ...exchanger,
              currentRates: mainPageExchanger?.rates || exchanger.rates || [],
              hasProcessedRates: mainPageExchanger?.hasProcessedRates || false,
              syncedAt: mainPageExchanger?.syncedAt || new Date().toISOString()
            };
          } catch (error) {
            console.error('Error enriching exchanger data:', error);
            return {
              ...exchanger,
              currentRates: exchanger.rates || [],
              hasProcessedRates: false,
              syncedAt: new Date().toISOString()
            };
          }
        })
      );
      
      setExchangers(enrichedExchangers);
      setTotalPages(response.totalPages);
    } catch (error) {
      console.error('Error loading exchangers:', error);
      showNotification('error', 'Ошибка загрузки обменников');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateExchanger = async (exchangerData: ExchangerFormData) => {
    try {
      await adminAPI.createExchanger(exchangerData);
      setShowCreateModal(false);
      showNotification('success', 'Обменник успешно создан');
      loadExchangers();
    } catch (error) {
      showNotification('error', 'Ошибка создания обменника');
    }
  };

  const handleUpdateExchanger = async (exchangerData: ExchangerFormData) => {
    if (!selectedExchanger) return;
    
    try {
      await adminAPI.updateExchanger(selectedExchanger.id!, exchangerData);
      setShowEditModal(false);
      setSelectedExchanger(null);
      showNotification('success', 'Обменник успешно обновлен');
      loadExchangers();
    } catch (error) {
      showNotification('error', 'Ошибка обновления обменника');
    }
  };

  const handleDeleteExchanger = async (id: string) => {
    if (!confirm('Вы уверены, что хотите удалить этот обменник?')) return;
    
    try {
      await adminAPI.softDeleteExchanger(parseInt(id));
      showNotification('success', 'Обменник успешно удален');
      loadExchangers();
    } catch (error) {
      showNotification('error', 'Ошибка удаления обменника');
    }
  };

  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  // Rate editing functions
  const startEditingRates = (exchangerId: string) => {
    const exchanger = exchangers.find(e => e.id === exchangerId);
    if (exchanger && exchanger.currentRates) {
      setTempRates({
        [exchangerId]: [...exchanger.currentRates]
      });
    } else {
      // Initialize with default rates if none exist
      setTempRates({
        [exchangerId]: [
          { currency: 'THB/RUB', buy: 2.45, sell: 2.52, change: 0 },
          { currency: 'USDT/BAHT', buy: 34.8, sell: 35.2, change: 0 }
        ]
      });
    }
    setEditingRates(prev => ({ ...prev, [exchangerId]: true }));
  };

  const cancelEditingRates = (exchangerId: string) => {
    setEditingRates(prev => ({ ...prev, [exchangerId]: false }));
    setTempRates(prev => {
      const newTempRates = { ...prev };
      delete newTempRates[exchangerId];
      return newTempRates;
    });
  };

  const updateTempRate = (exchangerId: string, rateIndex: number, field: 'buy' | 'sell', value: string) => {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) return;

    setTempRates(prev => ({
      ...prev,
      [exchangerId]: prev[exchangerId].map((rate: any, index: number) => 
        index === rateIndex ? { ...rate, [field]: numValue } : rate
      )
    }));
  };

  const saveRates = async (exchangerId: string) => {
    try {
      const rates = tempRates[exchangerId];
      if (!rates) return;

      // Apply intelligent rate processing
      const processedRates = processIntelligentRates(rates);
      
      // Validate rates
      const validationErrors = validateRates(processedRates);
      if (validationErrors.length > 0) {
        showNotification('error', `Ошибка валидации курсов: ${validationErrors.join(', ')}`);
        return;
      }

      // Update exchanger with new rates
      await adminAPI.updateExchanger(exchangerId, {
        rates: processedRates,
        lastUpdated: new Date().toISOString()
      });

      // Clear editing state
      setEditingRates(prev => ({ ...prev, [exchangerId]: false }));
      setTempRates(prev => {
        const newTempRates = { ...prev };
        delete newTempRates[exchangerId];
        return newTempRates;
      });

      showNotification('success', 'Курсы валют успешно обновлены');
      loadExchangers();

      // Trigger immediate sync with main page
      window.dispatchEvent(new CustomEvent('exchangerDataUpdated', { 
        detail: { 
          source: 'manual_edit', 
          exchangerId: exchangerId,
          timestamp: Date.now(),
          ratesCount: processedRates.length,
          message: 'Курсы валют обновлены вручную'
        } 
      }));
      sessionStorage.setItem('refreshExchangers', 'true');

    } catch (error) {
      console.error('Error saving rates:', error);
      showNotification('error', 'Ошибка сохранения курсов валют');
    }
  };

  // Rate validation function
  const validateRates = (rates: any[]): string[] => {
    const errors: string[] = [];
    
    rates.forEach(rate => {
      if (rate.sell < rate.buy) {
        errors.push(`${rate.currency}: курс продажи (${rate.sell}) не может быть меньше курса покупки (${rate.buy})`);
      }
      
      if (rate.currency === 'THB/RUB') {
        if (rate.buy < 1.5 || rate.buy > 5.0) {
          errors.push(`${rate.currency}: курс покупки ${rate.buy} вне нормального диапазона (1.5-5.0)`);
        }
        if (rate.sell < 1.5 || rate.sell > 5.0) {
          errors.push(`${rate.currency}: курс продажи ${rate.sell} вне нормального диапазона (1.5-5.0)`);
        }
      }
      
      if (rate.currency === 'USDT/BAHT') {
        if (rate.buy < 25.0 || rate.buy > 45.0) {
          errors.push(`${rate.currency}: курс покупки ${rate.buy} вне нормального диапазона (25.0-45.0)`);
        }
        if (rate.sell < 25.0 || rate.sell > 45.0) {
          errors.push(`${rate.currency}: курс продажи ${rate.sell} вне нормального диапазона (25.0-45.0)`);
        }
      }
    });
    
    return errors;
  };

  // Sync rates from main page
  const syncRatesFromMainPage = async (exchangerId: string) => {
    try {
      // Get current data from main page
      const { getActiveExchangersForMainPage } = await import('../../data/adminData');
      const mainPageExchangers = getActiveExchangersForMainPage();
      const mainPageExchanger = mainPageExchangers.find((e: any) => e.id.toString() === exchangerId);
      
      if (mainPageExchanger && mainPageExchanger.rates) {
        await adminAPI.updateExchanger(exchangerId, {
          rates: mainPageExchanger.rates,
          lastUpdated: new Date().toISOString()
        });
        
        showNotification('success', 'Курсы синхронизированы с главной страницы');
        loadExchangers();
      } else {
        showNotification('error', 'Курсы не найдены на главной странице для этого обменника');
      }
    } catch (error) {
      showNotification('error', 'Ошибка синхронизации курсов с главной страницы');
    }
  };

  const renderRatesDisplay = (exchanger: ExchangerType) => {
    const isEditing = editingRates[exchanger.id!];
    const rates = isEditing ? tempRates[exchanger.id!] : exchanger.currentRates;
    
    if (!rates || rates.length === 0) {
      return (
        <div className="text-center py-4">
          <div className="text-gray-400 text-sm mb-2">Курсы не доступны</div>
          <button
            onClick={() => startEditingRates(exchanger.id!)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm flex items-center space-x-1 mx-auto"
          >
            <Plus className="w-3 h-3" />
            <span>Добавить курсы</span>
          </button>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {/* Current Rates from Main Page Preview */}
        {!isEditing && (
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
            <div className="flex items-center justify-between mb-3">
              <h5 className="text-sm font-semibold text-blue-800 flex items-center">
                <Banknote className="w-4 h-4 mr-1" />
                Текущие курсы
                {exchanger.hasProcessedRates && (
                  <span className="ml-2 bg-orange-100 text-orange-600 px-2 py-0.5 rounded-full text-xs font-medium">
                    Обработано ИИ
                  </span>
                )}
              </h5>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => syncRatesFromMainPage(exchanger.id!)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs flex items-center space-x-1"
                  title="Синхронизировать курсы с главной страницы"
                >
                  <RefreshCw className="w-3 h-3" />
                  <span>Синх</span>
                </button>
                <button
                  onClick={() => startEditingRates(exchanger.id!)}
                  className="bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs flex items-center space-x-1"
                >
                  <Edit className="w-3 h-3" />
                  <span>Изменить</span>
                </button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {rates.slice(0, 4).map((rate: any, index: number) => (
                <div key={index} className="bg-white rounded-lg p-3 border border-blue-100 shadow-sm">
                  <div className="text-xs font-semibold text-gray-700 mb-2 flex items-center justify-between">
                    <span className="flex items-center">
                      <DollarSign className="w-3 h-3 mr-1 text-blue-500" />
                      {rate.currency}
                    </span>
                    {rate.converted && (
                      <span className="bg-orange-100 text-orange-600 px-1 py-0.5 rounded text-xs">
                        Конвертирован
                      </span>
                    )}
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-center">
                      <div className="text-sm font-bold text-emerald-600">
                        {rate.currency === 'THB/RUB' ? rate.buy.toFixed(3) : rate.buy.toFixed(2)}
                      </div>
                      <div className="text-xs text-gray-500">Покупка</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-bold text-rose-600">
                        {rate.currency === 'THB/RUB' ? rate.sell.toFixed(3) : rate.sell.toFixed(2)}
                      </div>
                      <div className="text-xs text-gray-500">Продажа</div>
                    </div>
                  </div>
                  {rate.change !== 0 && (
                    <div className="mt-2 flex items-center justify-center">
                      <div className={`flex items-center space-x-1 text-xs ${
                        rate.change > 0 ? 'text-emerald-600' : 'text-rose-600'
                      }`}>
                        {rate.change > 0 ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                        <span className="font-semibold">
                          {rate.change > 0 ? '+' : ''}{rate.change.toFixed(2)}%
                        </span>
                      </div>
                    </div>
                  )}
                  {rate.lastUpdated && (
                    <div className="mt-1 text-xs text-gray-400 text-center">
                      {new Date(rate.lastUpdated).toLocaleTimeString('ru-RU')}
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            {rates.length > 4 && (
              <div className="mt-2 text-center">
                <span className="text-xs text-blue-600">
                  +{rates.length - 4} дополнительных курсов
                </span>
              </div>
            )}
            
            <div className="mt-3 text-xs text-blue-600 text-center">
              Синхронизировано: {new Date(exchanger.syncedAt || '').toLocaleTimeString('ru-RU')}
            </div>
          </div>
        )}

        {/* Editable Rates */}
        {isEditing && (
          <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg p-4 border border-yellow-200">
            <div className="flex items-center justify-between mb-3">
              <h5 className="text-sm font-semibold text-yellow-800 flex items-center">
                <Edit className="w-4 h-4 mr-1" />
                Редактирование курсов
              </h5>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => saveRates(exchanger.id!)}
                  className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs flex items-center space-x-1"
                >
                  <Save className="w-3 h-3" />
                  <span>Сохранить</span>
                </button>
                <button
                  onClick={() => cancelEditingRates(exchanger.id!)}
                  className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-xs flex items-center space-x-1"
                >
                  <X className="w-3 h-3" />
                  <span>Отмена</span>
                </button>
              </div>
            </div>
            
            <div className="space-y-3">
              {tempRates[exchanger.id!]?.map((rate: any, index: number) => (
                <div key={index} className="bg-white rounded-lg p-3 border border-gray-200">
                  <div className="text-sm font-medium text-gray-700 mb-2 flex items-center justify-between">
                    <span>{rate.currency}</span>
                    {rate.converted && (
                      <span className="bg-orange-100 text-orange-600 px-2 py-0.5 rounded-full text-xs">
                        Автоконвертация
                      </span>
                    )}
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">Курс покупки</label>
                      <input
                        type="number"
                        step={rate.currency === 'THB/RUB' ? '0.001' : '0.01'}
                        value={rate.buy}
                        onChange={(e) => updateTempRate(exchanger.id!, index, 'buy', e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-green-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">Курс продажи</label>
                      <input
                        type="number"
                        step={rate.currency === 'THB/RUB' ? '0.001' : '0.01'}
                        value={rate.sell}
                        onChange={(e) => updateTempRate(exchanger.id!, index, 'sell', e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-red-500"
                      />
                    </div>
                  </div>
                  {rate.converted && (
                    <div className="mt-2 text-xs text-orange-600 bg-orange-50 p-2 rounded border border-orange-200">
                      ⚠️ Этот курс был автоматически конвертирован из обратного формата
                      {rate.originalRate && (
                        <div className="mt-1 text-xs text-gray-600">
                          Исходный: {rate.originalRate.currency} = {rate.originalRate.buy}/{rate.originalRate.sell}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            <div className="mt-3 text-xs text-gray-600 bg-white p-2 rounded border">
              <strong>Умная обработка курсов:</strong> Курсы ниже 1.0 будут автоматически конвертированы в прямой формат (THB/RUB). 
              Валидация обеспечивает корректность курсов продажи ≥ покупки.
            </div>
          </div>
        )}
      </div>
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'inactive': return <AlertCircle className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      default: return <Building className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Building className="w-6 h-6 text-blue-600" />
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Управление обменниками</h2>
            <p className="text-sm text-gray-600">
              Последнее обновление: {lastUpdate.toLocaleTimeString('ru-RU')}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={loadExchangers}
            disabled={loading}
            className="bg-gray-100 hover:bg-gray-200 disabled:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>{loading ? 'Обновление...' : 'Обновить'}</span>
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Добавить обменник</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Поиск обменников..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as any)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">Все статусы</option>
            <option value="active">Активные</option>
            <option value="inactive">Неактивные</option>
            <option value="pending">На модерации</option>
          </select>
          
          <button
            onClick={loadExchangers}
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center justify-center space-x-2 transition-colors"
          >
            <Filter className="w-4 h-4" />
            <span>Применить фильтры</span>
          </button>
        </div>
      </div>

      {/* Exchangers Table */}
      <div className="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Загрузка обменников...</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Обменник
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Курсы валют
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Контакты
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Статус
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Действия
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {exchangers.map((exchanger) => (
                  <tr key={exchanger.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="bg-blue-500 w-10 h-10 rounded-full flex items-center justify-center">
                          <span className="text-white font-semibold">
                            {exchanger.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{exchanger.name}</div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <MapPin className="w-3 h-3 mr-1" />
                            {exchanger.address}
                          </div>
                          <div className="text-xs text-gray-400 mt-1">
                            Обновлено: {new Date(exchanger.lastUpdated).toLocaleString('ru-RU')}
                          </div>
                        </div>
                      </div>
                    </td>
                    
                    <td className="px-6 py-4">
                      {renderRatesDisplay(exchanger)}
                    </td>
                    
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-600 space-y-1">
                        <div className="flex items-center">
                          <Phone className="w-3 h-3 mr-1" />
                          <a 
                            href={`tel:${exchanger.phone}`}
                            className="hover:text-blue-600 transition-colors"
                          >
                            {exchanger.phone}
                          </a>
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {exchanger.hours}
                        </div>
                        {exchanger.websiteUrl && (
                          <div className="flex items-center">
                            <Globe className="w-3 h-3 mr-1" />
                            <a 
                              href={exchanger.websiteUrl} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 text-xs truncate max-w-[120px]"
                            >
                              Сайт
                            </a>
                          </div>
                        )}
                      </div>
                    </td>
                    
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(exchanger.status)}`}>
                        {getStatusIcon(exchanger.status)}
                        <span className="ml-1">{exchanger.status}</span>
                      </span>
                      <div className="text-xs text-gray-500 mt-1">
                        Создан: {new Date(exchanger.createdAt).toLocaleDateString('ru-RU')}
                      </div>
                    </td>
                    
                    <td className="px-6 py-4 text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => {
                            setSelectedExchanger(exchanger);
                            setShowEditModal(true);
                          }}
                          className="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
                          title="Редактировать обменник"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteExchanger(exchanger.id!)}
                          className="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                          title="Удалить обменник"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Страница {currentPage} из {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Назад
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Вперед
            </button>
          </div>
        </div>
      )}

      {/* Create Exchanger Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <ExchangerForm
            mode="create"
            onSubmit={handleCreateExchanger}
            onCancel={() => setShowCreateModal(false)}
            isLoading={loading}
          />
        </div>
      )}

      {/* Edit Exchanger Modal */}
      {showEditModal && selectedExchanger && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <ExchangerForm
            mode="edit"
            initialData={selectedExchanger}
            onSubmit={handleUpdateExchanger}
            onCancel={() => {
              setShowEditModal(false);
              setSelectedExchanger(null);
            }}
            isLoading={loading}
          />
        </div>
      )}

      {/* Notification */}
      {notification && (
        <div className="fixed top-4 right-4 z-50">
          <div className={`rounded-lg shadow-lg p-4 max-w-sm ${
            notification.type === 'success' ? 'bg-green-500 text-white' :
            notification.type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
          }`}>
            <div className="flex items-center space-x-3">
              {notification.type === 'success' && <CheckCircle className="w-5 h-5" />}
              {notification.type === 'error' && <AlertCircle className="w-5 h-5" />}
              {notification.type === 'info' && <Building className="w-5 h-5" />}
              <p className="font-medium text-sm">{notification.message}</p>
              <button
                onClick={() => setNotification(null)}
                className="text-white hover:text-gray-200"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExchangerManagement;