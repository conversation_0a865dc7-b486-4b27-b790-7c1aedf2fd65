import React from 'react';
import { Shield, CheckCircle, Star, Users } from 'lucide-react';

const TrustBadge: React.FC = () => {
  const trustFeatures = [
    {
      icon: <Shield className="w-5 h-5 text-green-500" />,
      title: 'Проверенные обменники',
      description: 'Все партнеры прошли верификацию'
    },
    {
      icon: <CheckCircle className="w-5 h-5 text-blue-500" />,
      title: 'Лицензированные',
      description: 'Работают по официальным лицензиям'
    },
    {
      icon: <Star className="w-5 h-5 text-yellow-500" />,
      title: 'Высокие рейтинги',
      description: 'Только обменники с рейтингом 4.0+'
    },
    {
      icon: <Users className="w-5 h-5 text-purple-500" />,
      title: 'Тысячи клиентов',
      description: 'Проверено реальными отзывами'
    }
  ];

  return (
    <div className="bg-gradient-to-r from-green-50 via-blue-50 to-purple-50 p-6 rounded-xl border border-green-100 mb-6">
      <div className="text-center mb-6">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full mb-4">
          <Shield className="w-8 h-8 text-white" />
        </div>
        <h3 className="text-2xl font-bold text-gray-800 mb-2">
          Размещены только проверенные обменники
        </h3>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Мы тщательно отбираем партнеров и проверяем каждый обменный пункт перед добавлением на платформу
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {trustFeatures.map((feature, index) => (
          <div
            key={index}
            className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow"
          >
            <div className="flex justify-center mb-3">
              {feature.icon}
            </div>
            <h4 className="font-semibold text-gray-800 mb-2 text-sm">
              {feature.title}
            </h4>
            <p className="text-gray-600 text-xs leading-relaxed">
              {feature.description}
            </p>
          </div>
        ))}
      </div>

      <div className="mt-6 text-center">
        <div className="inline-flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-sm border border-gray-200">
          <CheckCircle className="w-4 h-4 text-green-500" />
          <span className="text-sm font-medium text-gray-700">
            Гарантия безопасности и надежности
          </span>
        </div>
      </div>
    </div>
  );
};

export default TrustBadge;