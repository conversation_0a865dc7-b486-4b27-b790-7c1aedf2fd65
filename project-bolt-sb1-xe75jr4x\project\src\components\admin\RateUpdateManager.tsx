import React, { useState, useRef } from 'react';
import { Upload, Download, Bot, FileSpreadsheet, AlertCircle, CheckCircle, RefreshCw, Settings, Zap, Database, Globe, Clock } from 'lucide-react';
import { excelParsingService, ParsedExchangeData } from '../../services/excelParsingService';
import { aiParsingService } from '../../services/aiParsingService';
import { adminAPI } from '../../data/adminData';
import { processIntelligentRates, processRatesWithEnhancements, validateRateData, RateData } from '../../utils/rateProcessor';

interface AiParsingResult {
  site: string;
  url: string;
  status: 'success' | 'error' | 'pending';
  rates?: Array<{
    currency: string;
    buy: number;
    sell: number;
  }>;
  error?: string;
  lastUpdated?: string;
}

const RateUpdateManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'manual' | 'excel' | 'ai'>('manual');
  const [isProcessing, setIsProcessing] = useState(false);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
  } | null>(null);
  
  // Excel upload state
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [parseResults, setParseResults] = useState<ParsedExchangeData[] | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // AI parsing state
  const [aiConfig, setAiConfig] = useState({
    apiKey: '',
    model: 'deepseek-chat',
    baseUrl: 'https://api.deepseek.com/v1'
  });
  const [aiResults, setAiResults] = useState<AiParsingResult[] | null>(null);
  const [selectedSites, setSelectedSites] = useState<string[]>([]);

  // Список сайтов для ИИ парсинга
  const availableSites = [
    { name: 'EX24.PRO', url: 'https://qr.ex24.pro/' },
    { name: 'MoneyShop Phuket', url: 'https://moneyshopphuket.com' },
    { name: 'Arbi Exchange', url: 'https://arbi.exchange/' },
    { name: 'Senate Exchange', url: 'https://senateexchange.org/currency-exchange-rates/' },
    { name: 'Fast Money', url: 'https://www.fmbest.pro' },
    { name: 'ATM24', url: 'https://atm24.pro/ru/thailand' },
    { name: 'Mamy Exchange', url: 'https://mamyexchange.com/' },
  ];

  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  // Excel файл обработка
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = excelParsingService.validateExcelFile(file);
    if (!validation.valid) {
      showNotification('error', validation.error || 'Некорректный файл');
      return;
    }

    setUploadedFile(file);
    setParseResults(null);
  };

  const processExcelFile = async () => {
    if (!uploadedFile) return;

    setIsProcessing(true);
    try {
      const result = await excelParsingService.parseExcelFile(uploadedFile);
      
      if (result.success && result.data) {
        // Apply intelligent processing to all parsed data
        const processedData = result.data.map(exchangerData => ({
          ...exchangerData,
          rates: processIntelligentRates(exchangerData.rates)
        }));
        
        // Validate all rates
        let totalValidRates = 0;
        let totalInvalidRates = 0;
        let totalWarnings = 0;
        
        processedData.forEach(exchanger => {
          const validation = validateRateData(exchanger.rates);
          totalValidRates += validation.valid.length;
          totalInvalidRates += validation.invalid.length;
          totalWarnings += validation.warnings.length;
          
          if (validation.invalid.length > 0) {
            console.warn(`Invalid rates for ${exchanger.exchangerName}:`, validation.invalid);
          }
          if (validation.warnings.length > 0) {
            console.warn(`Rate warnings for ${exchanger.exchangerName}:`, validation.warnings);
          }
        });
        
        setParseResults(processedData);
        
        const message = `Excel processing completed: ${result.data.length} exchangers, ${totalValidRates} valid rates, ${totalInvalidRates} invalid rates${totalWarnings > 0 ? `, ${totalWarnings} warnings` : ''}`;
        showNotification(totalInvalidRates > 0 ? 'info' : 'success', message);
        
      } else {
        showNotification('error', result.error || 'Error processing Excel file');
      }
    } catch (error) {
      console.error('Excel processing error:', error);
      showNotification('error', `Excel processing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const applyExcelData = async () => {
    if (!parseResults) return;

    setIsProcessing(true);
    try {
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];
      
      // Process each exchanger with intelligent rate conversion
      for (const exchangerData of parseResults) {
        try {
          console.log(`Processing Excel data for ${exchangerData.exchangerName}:`, exchangerData);

          // Find existing exchanger or create new one
          const existingExchangers = await adminAPI.getExchangers(1, 100, 'all');
          const found = existingExchangers.exchangers.find(e =>
            e.name.toLowerCase().includes(exchangerData.exchangerName.toLowerCase())
          );

          console.log(`Looking for exchanger with name containing "${exchangerData.exchangerName}":`, found ? `Found: ${found.name}` : 'Not found');

          // Apply enhanced rate processing with automatic inverse calculation and mandatory pairs
          const processedRates = processRatesWithEnhancements(exchangerData.rates);
          console.log(`Processed ${exchangerData.rates.length} rates to ${processedRates.length} final rates (with enhancements):`, processedRates);

          // Check if we have valid rates to apply
          if (!processedRates || processedRates.length === 0) {
            console.warn(`No valid rates processed for ${exchangerData.exchangerName}, skipping`);
            errorCount++;
            errors.push(`No valid rates for ${exchangerData.exchangerName}`);
            continue;
          }
          
          if (found) {
            // Update existing exchanger
            await adminAPI.updateExchanger(found.id!, {
              name: exchangerData.exchangerName,
              address: exchangerData.address,
              district: exchangerData.district,
              phone: exchangerData.phone,
              hours: exchangerData.hours,
              status: 'active',
              rates: processedRates,
              lastUpdated: new Date().toISOString()
            });
          } else {
            // Create new exchanger
            await adminAPI.createExchanger({
              name: exchangerData.exchangerName,
              address: exchangerData.address,
              district: exchangerData.district,
              phone: exchangerData.phone,
              hours: exchangerData.hours,
              websiteUrl: '',
              parsingEnabled: false,
              parsingConfig: {
                enabled: false,
                selectors: {},
                updateInterval: 60,
                retryAttempts: 3,
                timeout: 30
              },
              additionalOffices: [],
              status: 'active',
              rates: processedRates,
              lastUpdated: new Date().toISOString()
            });
          }
          
          successCount++;
          console.log(`Successfully processed ${exchangerData.exchangerName} with ${processedRates.length} rates`);
          
        } catch (error) {
          errorCount++;
          const errorMessage = `Error updating ${exchangerData.exchangerName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(errorMessage);
          errors.push(errorMessage);
        }
      }

      // Show detailed results
      if (successCount > 0) {
        showNotification('success', `Excel data applied successfully: ${successCount} exchangers updated${errorCount > 0 ? `, ${errorCount} errors` : ''}`);
      } else {
        showNotification('error', `Failed to apply Excel data: ${errors.join('; ')}`);
      }
      
      setParseResults(null);
      setUploadedFile(null);
      
      // Trigger immediate refresh with detailed info
      window.dispatchEvent(new CustomEvent('exchangerDataUpdated', { 
        detail: { 
          source: 'excel_upload', 
          timestamp: Date.now(),
          processedCount: successCount,
          errorCount: errorCount,
          message: `Excel file processed: ${successCount} updated, ${errorCount} errors`
        } 
      }));
      sessionStorage.setItem('refreshExchangers', 'true');
      
    } catch (error) {
      console.error('Critical error applying Excel data:', error);
      showNotification('error', `Critical error applying Excel data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Manual rate update functionality
  const handleManualRateUpdate = async () => {
    setIsProcessing(true);
    try {
      // Try backend API first
      try {
        const response = await fetch('/api/v1/admin/rates/update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const result = await response.json();
          showNotification('success', 'Курсы валют успешно обновлены из Google Sheets');

          // Trigger data refresh
          window.dispatchEvent(new CustomEvent('exchangerDataUpdated', {
            detail: {
              source: 'manual_update',
              timestamp: Date.now(),
              message: 'Manual rate update completed'
            }
          }));
          return;
        } else {
          const error = await response.json();
          throw new Error(error.detail || 'Backend API error');
        }
      } catch (apiError) {
        console.warn('Backend API not available, using mock update:', apiError);

        // Fallback: Simulate Google Sheets update with mock data
        await simulateGoogleSheetsUpdate();

        showNotification('success', 'Курсы валют обновлены (режим разработки - используются тестовые данные)');

        // Trigger data refresh
        window.dispatchEvent(new CustomEvent('exchangerDataUpdated', {
          detail: {
            source: 'mock_google_sheets',
            timestamp: Date.now(),
            message: 'Mock Google Sheets update completed'
          }
        }));
      }
    } catch (error) {
      console.error('Manual rate update error:', error);
      showNotification('error', `Ошибка обновления курсов: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Simulate Google Sheets update with mock data
  const simulateGoogleSheetsUpdate = async () => {
    // Generate updated exchange rates with realistic variations
    const mockExchangers = [
      {
        name: 'Bangkok Exchange Center',
        district: 'Sukhumvit',
        address: '123 Sukhumvit Rd, Bangkok',
        phone: '+66 89 123 4567',
        hours: '09:00-18:00',
        rates: [
          { currency: 'RUB', buy: 35.2 + (Math.random() - 0.5) * 2, sell: 36.8 + (Math.random() - 0.5) * 2, change: Math.random() * 4 - 2 },
          { currency: 'USD', buy: 34.5 + (Math.random() - 0.5) * 2, sell: 35.2 + (Math.random() - 0.5) * 2, change: Math.random() * 4 - 2 },
          { currency: 'USDT', buy: 34.8 + (Math.random() - 0.5) * 2, sell: 35.5 + (Math.random() - 0.5) * 2, change: Math.random() * 4 - 2 }
        ]
      },
      {
        name: 'Pattaya Money Exchange',
        district: 'Pattaya',
        address: '456 Beach Rd, Pattaya',
        phone: '+66 89 234 5678',
        hours: '08:00-20:00',
        rates: [
          { currency: 'RUB', buy: 35.0 + (Math.random() - 0.5) * 2, sell: 36.5 + (Math.random() - 0.5) * 2, change: Math.random() * 4 - 2 },
          { currency: 'USD', buy: 34.3 + (Math.random() - 0.5) * 2, sell: 35.0 + (Math.random() - 0.5) * 2, change: Math.random() * 4 - 2 },
          { currency: 'USDT', buy: 34.6 + (Math.random() - 0.5) * 2, sell: 35.3 + (Math.random() - 0.5) * 2, change: Math.random() * 4 - 2 }
        ]
      },
      {
        name: 'Phuket Currency Exchange',
        district: 'Patong',
        address: '789 Bangla Rd, Phuket',
        phone: '+66 89 345 6789',
        hours: '10:00-22:00',
        rates: [
          { currency: 'RUB', buy: 35.1 + (Math.random() - 0.5) * 2, sell: 36.7 + (Math.random() - 0.5) * 2, change: Math.random() * 4 - 2 },
          { currency: 'USD', buy: 34.4 + (Math.random() - 0.5) * 2, sell: 35.1 + (Math.random() - 0.5) * 2, change: Math.random() * 4 - 2 },
          { currency: 'USDT', buy: 34.7 + (Math.random() - 0.5) * 2, sell: 35.4 + (Math.random() - 0.5) * 2, change: Math.random() * 4 - 2 }
        ]
      }
    ];

    // Update existing exchangers in adminData
    for (const mockExchanger of mockExchangers) {
      try {
        // Try to update existing exchanger
        const existingExchangers = await adminAPI.getExchangers(1, 100, 'all');
        const found = existingExchangers.exchangers.find(e =>
          e.name.toLowerCase().includes(mockExchanger.name.toLowerCase())
        );

        if (found) {
          await adminAPI.updateExchanger(found.id!, {
            ...found,
            rates: mockExchanger.rates,
            lastUpdated: new Date().toISOString()
          });
        } else {
          // Create new exchanger
          await adminAPI.createExchanger({
            name: mockExchanger.name,
            address: mockExchanger.address,
            district: mockExchanger.district,
            phone: mockExchanger.phone,
            hours: mockExchanger.hours,
            websiteUrl: '',
            parsingEnabled: false,
            parsingConfig: {
              enabled: false,
              selectors: {},
              updateInterval: 60,
              retryAttempts: 3,
              timeout: 30
            },
            additionalOffices: [],
            status: 'active',
            rates: mockExchanger.rates,
            lastUpdated: new Date().toISOString()
          });
        }
      } catch (error) {
        console.warn(`Failed to update ${mockExchanger.name}:`, error);
      }
    }

    // Add a small delay to simulate network request
    await new Promise(resolve => setTimeout(resolve, 1000));
  };

  const clearRateCache = async () => {
    setIsProcessing(true);
    try {
      // Try backend API first
      try {
        const response = await fetch('/api/v1/admin/cache/clear', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          showNotification('success', 'Кэш курсов очищен');
          return;
        } else {
          const error = await response.json();
          throw new Error(error.detail || 'Backend API error');
        }
      } catch (apiError) {
        console.warn('Backend API not available, using mock cache clear:', apiError);

        // Fallback: Clear local storage cache
        const cacheKeys = [
          'exchangerManagement',
          'exchangerData',
          'rateCache',
          'adminStats',
          'activity_logs'
        ];

        cacheKeys.forEach(key => {
          localStorage.removeItem(key);
          sessionStorage.removeItem(key);
        });

        // Clear any cached activity logs
        const sessionKeys = Object.keys(sessionStorage);
        sessionKeys.forEach(key => {
          if (key.startsWith('activity_logs_')) {
            sessionStorage.removeItem(key);
          }
        });

        showNotification('success', 'Локальный кэш очищен (режим разработки)');

        // Trigger data refresh
        window.dispatchEvent(new CustomEvent('exchangerDataUpdated', {
          detail: {
            source: 'cache_clear',
            timestamp: Date.now(),
            message: 'Cache cleared'
          }
        }));
      }
    } catch (error) {
      console.error('Cache clear error:', error);
      showNotification('error', `Ошибка очистки кэша: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // ИИ парсинг
  const testAiConnection = async () => {
    if (!aiConfig.apiKey) {
      showNotification('error', 'Введите API ключ DeepSeek');
      return;
    }

    setIsProcessing(true);
    try {
      aiParsingService.configure(aiConfig);
      const result = await aiParsingService.testApiKey();
      
      if (result.success) {
        showNotification('success', 'Подключение к DeepSeek API успешно!');
      } else {
        showNotification('error', result.error || 'Ошибка подключения к API');
      }
    } catch (error) {
      showNotification('error', 'Ошибка тестирования API');
    } finally {
      setIsProcessing(false);
    }
  };

  const startAiParsing = async () => {
    if (selectedSites.length === 0) {
      showNotification('error', 'Выберите сайты для парсинга');
      return;
    }

    if (!aiParsingService.isConfigured()) {
      showNotification('error', 'Настройте API ключ DeepSeek');
      return;
    }

    setIsProcessing(true);
    try {
      const sitesToParse = availableSites.filter(site => 
        selectedSites.includes(site.name)
      );

      const results = await aiParsingService.parseMultipleSites(sitesToParse);
      setAiResults(results);

      const successCount = results.filter(r => r.success).length;
      showNotification('success', `ИИ парсинг завершен: ${successCount}/${results.length} сайтов обработано успешно`);
    } catch (error) {
      showNotification('error', 'Ошибка ИИ парсинга');
    } finally {
      setIsProcessing(false);
    }
  };

  const applyAiData = async () => {
    if (!aiResults) return;

    setIsProcessing(true);
    try {
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];
      
      // Apply AI parsing data with intelligent conversion
      for (const result of aiResults) {
        if (!result.success) {
          console.warn(`Skipping failed AI result for ${result.siteName}:`, result.error);
          continue;
        }

        console.log(`Processing AI result for ${result.siteName}:`, result);

        // Convert AI data to app format with intelligent processing
        const rates = convertAiRatesToAppFormat(result.rates);
        console.log(`Converted ${result.rates.length} AI rates to ${rates.length} app rates:`, rates);

        const processedRates = processRatesWithEnhancements(rates);
        console.log(`Processed ${rates.length} rates to ${processedRates.length} final rates (with enhancements):`, processedRates);

        // Check if we have valid rates to apply
        if (!processedRates || processedRates.length === 0) {
          console.warn(`No valid rates processed for ${result.siteName}, skipping`);
          errorCount++;
          errors.push(`No valid rates for ${result.siteName}`);
          continue;
        }

        // Update exchanger
        try {
          const existingExchangers = await adminAPI.getExchangers(1, 100, 'all');
          const found = existingExchangers.exchangers.find(e =>
            e.name.toLowerCase().includes(result.siteName.toLowerCase())
          );

          console.log(`Looking for exchanger with name containing "${result.siteName}":`, found ? `Found: ${found.name}` : 'Not found');

          if (found) {
            // Update existing exchanger
            await adminAPI.updateExchanger(found.id!, {
              name: result.siteName,
              status: 'active',
              lastParsedAt: new Date().toISOString(),
              rates: processedRates,
              lastUpdated: new Date().toISOString()
            });
            successCount++;
          } else {
            // Create new exchanger if not found
            await adminAPI.createExchanger({
              name: result.siteName,
              address: `Parsed from ${result.siteName}`,
              district: 'AI Parsed',
              phone: '',
              hours: '09:00-18:00',
              websiteUrl: '',
              parsingEnabled: true,
              parsingConfig: {
                enabled: true,
                selectors: {},
                updateInterval: 60,
                retryAttempts: 3,
                timeout: 30
              },
              additionalOffices: [],
              status: 'active',
              rates: processedRates,
              lastUpdated: new Date().toISOString(),
              lastParsedAt: new Date().toISOString()
            });
            successCount++;
          }
        } catch (error) {
          errorCount++;
          const errorMessage = `Error updating ${result.siteName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(errorMessage);
          errors.push(errorMessage);
        }
      }

      // Show detailed results
      if (successCount > 0) {
        showNotification('success', `AI parsing data applied: ${successCount} exchangers updated${errorCount > 0 ? `, ${errorCount} errors` : ''}`);
      } else {
        showNotification('error', `Failed to apply AI data: ${errors.join('; ')}`);
      }
      
      setAiResults(null);
      
      // Trigger immediate refresh with detailed info
      window.dispatchEvent(new CustomEvent('exchangerDataUpdated', { 
        detail: { 
          source: 'ai_parsing', 
          timestamp: Date.now(),
          processedCount: successCount,
          errorCount: errorCount,
          message: `AI parsing completed: ${successCount} updated, ${errorCount} errors`
        } 
      }));
      sessionStorage.setItem('refreshExchangers', 'true');
      
    } catch (error) {
      console.error('Critical error applying AI data:', error);
      showNotification('error', `Critical error applying AI data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const convertAiRatesToAppFormat = (aiRates: any[]) => {
    // Convert AI data to app format
    const appRates: any[] = [];
    const pairs = new Map();

    aiRates.forEach(rate => {
      const pair = `${rate.currency_from}/${rate.currency_to}`;
      if (!pairs.has(pair)) {
        pairs.set(pair, { buy: null, sell: null });
      }
      
      const pairData = pairs.get(pair);
      if (rate.type === 'buy') {
        pairData.buy = rate.rate;
      } else if (rate.type === 'sell') {
        pairData.sell = rate.rate;
      }
    });

    pairs.forEach((data, pair) => {
      // Accept rates even if only buy or sell is available
      if (data.buy || data.sell) {
        const rate = {
          currency: pair,
          buy: data.buy || data.sell, // Use sell as buy if buy is missing
          sell: data.sell || data.buy, // Use buy as sell if sell is missing
          change: 0 // Will be calculated by intelligent processing
        };

        // If only one rate is available, add a small spread (1-2%)
        if (!data.buy && data.sell) {
          rate.buy = data.sell * 0.99; // Buy rate slightly lower than sell
        } else if (data.buy && !data.sell) {
          rate.sell = data.buy * 1.01; // Sell rate slightly higher than buy
        }

        appRates.push(rate);
      }
    });

    return appRates;
  };

  // Intelligent rate processing function
  const processIntelligentRates = (rates: any[]) => {
    const processedRates: any[] = [];
    const INVERSE_THRESHOLD = 1.0;
    
    rates.forEach(rate => {
      try {
        let processedRate = { ...rate };
        
        // Validate rate values
        if (!rate.buy || !rate.sell || isNaN(rate.buy) || isNaN(rate.sell)) {
          console.warn(`Invalid rate data for ${rate.currency}:`, rate);
          return;
        }
        
        // Check if this is an inverse rate (RUB/THB instead of THB/RUB)
        if (rate.currency === 'RUB/THB' || (rate.buy < INVERSE_THRESHOLD && rate.sell < INVERSE_THRESHOLD)) {
          console.log(`Converting inverse rate for ${rate.currency}: buy=${rate.buy}, sell=${rate.sell}`);
          
          // Convert inverse rates to direct rates
          processedRate = {
            currency: 'THB/RUB',
            buy: parseFloat((1 / rate.sell).toFixed(4)), // Inverse of sell becomes buy
            sell: parseFloat((1 / rate.buy).toFixed(4)), // Inverse of buy becomes sell
            change: rate.change || 0,
            converted: true,
            originalRate: rate
          };
          
          console.log(`Converted to direct rate: buy=${processedRate.buy}, sell=${processedRate.sell}`);
        }
        
        // Validate that sell rate >= buy rate (basic sanity check)
        if (processedRate.sell < processedRate.buy) {
          console.warn(`Invalid rate spread for ${processedRate.currency}: sell (${processedRate.sell}) < buy (${processedRate.buy})`);
          // Swap if needed
          const temp = processedRate.buy;
          processedRate.buy = processedRate.sell;
          processedRate.sell = temp;
        }
        
        // Ensure reasonable rate ranges for THB/RUB (typically 2-4)
        if (processedRate.currency === 'THB/RUB') {
          if (processedRate.buy < 1.5 || processedRate.buy > 5.0) {
            console.warn(`Unusual THB/RUB buy rate: ${processedRate.buy}, may need manual review`);
          }
          if (processedRate.sell < 1.5 || processedRate.sell > 5.0) {
            console.warn(`Unusual THB/RUB sell rate: ${processedRate.sell}, may need manual review`);
          }
        }
        
        // Round to appropriate decimal places
        if (processedRate.currency === 'THB/RUB') {
          processedRate.buy = parseFloat(processedRate.buy.toFixed(3));
          processedRate.sell = parseFloat(processedRate.sell.toFixed(3));
        } else if (processedRate.currency === 'USDT/BAHT') {
          processedRate.buy = parseFloat(processedRate.buy.toFixed(2));
          processedRate.sell = parseFloat(processedRate.sell.toFixed(2));
        } else {
          processedRate.buy = parseFloat(processedRate.buy.toFixed(4));
          processedRate.sell = parseFloat(processedRate.sell.toFixed(4));
        }
        
        processedRates.push(processedRate);

      } catch (error) {
        console.error(`Error processing rate for ${rate.currency}:`, error);
      }
    });

    console.log(`Processed ${processedRates.length} rates from ${rates.length} input rates`);
    console.log('Rates processed intelligently:', processedRates);
    return processedRates;
  };

  const exportCurrentData = () => {
    try {
      // Получаем текущие данные обменников
      adminAPI.getExchangers(1, 100, 'all').then(data => {
        excelParsingService.exportToExcel(data.exchangers, 'current_exchange_rates.xlsx');
        showNotification('success', 'Данные экспортированы в Excel файл');
      });
    } catch (error) {
      console.error('Error applying rates:', error);
      showNotification('error', 'Ошибка экспорта данных');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Database className="w-6 h-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-800">Обновление курсов валют</h2>
        </div>
        <button
          onClick={exportCurrentData}
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Экспорт в Excel</span>
        </button>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('manual')}
              className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'manual'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <div className="flex items-center space-x-2">
                <RefreshCw className="w-4 h-4" />
                <span>Ручное обновление</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('excel')}
              className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'excel'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <div className="flex items-center space-x-2">
                <FileSpreadsheet className="w-4 h-4" />
                <span>Загрузка из Excel</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('ai')}
              className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'ai'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <div className="flex items-center space-x-2">
                <Bot className="w-4 h-4" />
                <span>ИИ Парсинг</span>
              </div>
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'manual' ? (
            <div className="space-y-6">
              {/* Manual Update Section */}
              <div className="bg-green-50 rounded-lg p-6 border border-green-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <RefreshCw className="w-5 h-5 mr-2 text-green-600" />
                  Ручное обновление курсов валют
                </h3>

                <div className="space-y-4">
                  <p className="text-gray-600">
                    Обновите курсы валют напрямую из Google Sheets или очистите кэш для принудительного обновления.
                  </p>

                  <div className="flex flex-col sm:flex-row gap-4">
                    <button
                      onClick={handleManualRateUpdate}
                      disabled={isProcessing}
                      className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg flex items-center justify-center space-x-2 flex-1"
                    >
                      {isProcessing ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <Database className="w-4 h-4" />
                      )}
                      <span>{isProcessing ? 'Обновление...' : 'Обновить из Google Sheets'}</span>
                    </button>

                    <button
                      onClick={clearRateCache}
                      disabled={isProcessing}
                      className="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg flex items-center justify-center space-x-2 flex-1"
                    >
                      {isProcessing ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <Zap className="w-4 h-4" />
                      )}
                      <span>{isProcessing ? 'Очистка...' : 'Очистить кэш'}</span>
                    </button>
                  </div>

                  <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <div className="flex items-start space-x-3">
                      <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-1">Информация об обновлении:</p>
                        <ul className="list-disc list-inside space-y-1 text-blue-700">
                          <li>Обновление из Google Sheets загружает актуальные курсы валют</li>
                          <li>Очистка кэша принудительно обновляет все данные при следующем запросе</li>
                          <li>Изменения отобразятся на главной странице в течение нескольких секунд</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : activeTab === 'excel' ? (
            <div className="space-y-6">
              {/* Excel Upload Section */}
              <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <FileSpreadsheet className="w-5 h-5 mr-2 text-blue-600" />
                  Загрузка курсов из Excel файла
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Выберите Excel файл с курсами валют
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept=".xlsx,.xls,.csv"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                      >
                        <Upload className="w-4 h-4" />
                        <span>Выбрать Excel/CSV файл</span>
                      </button>
                      {uploadedFile && (
                        <span className="text-sm text-gray-600">
                          Выбран: {uploadedFile.name}
                        </span>
                      )}
                    </div>
                  </div>

                  {uploadedFile && (
                    <button
                      onClick={processExcelFile}
                      disabled={isProcessing}
                      className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg flex items-center space-x-2"
                    >
                      {isProcessing ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <Database className="w-4 h-4" />
                      )}
                      <span>{isProcessing ? 'Обработка...' : 'Обработать файл'}</span>
                    </button>
                  )}
                </div>

                {/* Excel Results */}
                {parseResults && (
                  <div className="mt-6 bg-white rounded-lg p-4 border border-gray-200">
                    <h4 className="font-semibold text-gray-800 mb-3">
                      Результаты обработки ({parseResults.length} обменников)
                    </h4>
                    <div className="max-h-60 overflow-y-auto space-y-2">
                      {parseResults.map((exchanger, index) => (
                        <div key={index} className="bg-gray-50 p-3 rounded border">
                          <div className="font-medium text-gray-800">{exchanger.exchangerName}</div>
                          <div className="text-sm text-gray-600">
                            {exchanger.rates.length} валютных пар • {exchanger.district}
                          </div>
                        </div>
                      ))}
                    </div>
                    <button
                      onClick={applyExcelData}
                      disabled={isProcessing}
                      className="mt-4 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg flex items-center space-x-2"
                    >
                      {isProcessing ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <CheckCircle className="w-4 h-4" />
                      )}
                      <span>{isProcessing ? 'Применение...' : 'Применить данные'}</span>
                    </button>
                  </div>
                )}
              </div>

              {/* Excel Format Info */}
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <h4 className="font-medium text-gray-800 mb-2">Формат Excel файла:</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>• <strong>Колонка A:</strong> Сайт (название обменника)</p>
                  <p>• <strong>Колонка B:</strong> Метод поиска</p>
                  <p>• <strong>Колонка C:</strong> Валюта от (RUB, USD, EUR, etc.)</p>
                  <p>• <strong>Колонка D:</strong> Валюта к (THB, RUB, USDT)</p>
                  <p>• <strong>Колонка E:</strong> Курс покупки</p>
                  <p>• <strong>Колонка F:</strong> Курс продажи</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* AI Configuration */}
              <div className="bg-purple-50 rounded-lg p-6 border border-purple-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <Bot className="w-5 h-5 mr-2 text-purple-600" />
                  Настройка ИИ парсинга (DeepSeek API)
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API Ключ DeepSeek
                    </label>
                    <input
                      type="password"
                      value={aiConfig.apiKey}
                      onChange={(e) => setAiConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                      placeholder="sk-..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Модель
                    </label>
                    <select
                      value={aiConfig.model}
                      onChange={(e) => setAiConfig(prev => ({ ...prev, model: e.target.value }))}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                    >
                      <option value="deepseek-chat">deepseek-chat</option>
                      <option value="deepseek-coder">deepseek-coder</option>
                    </select>
                  </div>
                </div>

                <button
                  onClick={testAiConnection}
                  disabled={isProcessing || !aiConfig.apiKey}
                  className="mt-4 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                >
                  {isProcessing ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Zap className="w-4 h-4" />
                  )}
                  <span>{isProcessing ? 'Тестирование...' : 'Тест подключения'}</span>
                </button>
              </div>

              {/* Site Selection */}
              <div className="bg-white rounded-lg p-6 border border-gray-200">
                <h4 className="font-semibold text-gray-800 mb-4">Выберите сайты для парсинга:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {availableSites.map((site) => (
                    <label key={site.name} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                      <input
                        type="checkbox"
                        checked={selectedSites.includes(site.name)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedSites(prev => [...prev, site.name]);
                          } else {
                            setSelectedSites(prev => prev.filter(s => s !== site.name));
                          }
                        }}
                        className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                      />
                      <div>
                        <div className="font-medium text-gray-800">{site.name}</div>
                        <div className="text-xs text-gray-500">{site.url}</div>
                      </div>
                    </label>
                  ))}
                </div>

                <div className="mt-4 flex items-center space-x-4">
                  <button
                    onClick={() => setSelectedSites(availableSites.map(s => s.name))}
                    className="text-sm text-purple-600 hover:text-purple-800 underline"
                  >
                    Выбрать все
                  </button>
                  <button
                    onClick={() => setSelectedSites([])}
                    className="text-sm text-gray-600 hover:text-gray-800 underline"
                  >
                    Очистить выбор
                  </button>
                </div>

                <button
                  onClick={startAiParsing}
                  disabled={isProcessing || selectedSites.length === 0 || !aiParsingService.isConfigured()}
                  className="mt-4 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg flex items-center space-x-2"
                >
                  {isProcessing ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Bot className="w-4 h-4" />
                  )}
                  <span>{isProcessing ? 'Парсинг...' : 'Запустить ИИ парсинг'}</span>
                </button>
              </div>

              {/* AI Results */}
              {aiResults && (
                <div className="bg-white rounded-lg p-6 border border-gray-200">
                  <h4 className="font-semibold text-gray-800 mb-3">
                    Результаты ИИ парсинга ({aiResults.length} сайтов)
                  </h4>
                  <div className="max-h-80 overflow-y-auto space-y-3">
                    {aiResults.map((result, index) => (
                      <div key={index} className={`p-3 rounded border ${
                        result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                      }`}>
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-800">{result.siteName}</div>
                            <div className="text-sm text-gray-600">
                              {result.success 
                                ? `${result.rates.length} курсов найдено • ${result.executionTime}мс`
                                : result.error
                              }
                            </div>
                          </div>
                          {result.success ? (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          ) : (
                            <AlertCircle className="w-5 h-5 text-red-600" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  <button
                    onClick={applyAiData}
                    disabled={isProcessing}
                    className="mt-4 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg flex items-center space-x-2"
                  >
                    {isProcessing ? (
                      <RefreshCw className="w-4 h-4 animate-spin" />
                    ) : (
                      <CheckCircle className="w-4 h-4" />
                    )}
                    <span>{isProcessing ? 'Применение...' : 'Применить данные ИИ'}</span>
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <div className="fixed top-4 right-4 z-50">
          <div className={`rounded-lg shadow-lg p-4 max-w-sm ${
            notification.type === 'success' ? 'bg-green-500 text-white' :
            notification.type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
          }`}>
            <div className="flex items-center space-x-3">
              {notification.type === 'success' && <CheckCircle className="w-5 h-5" />}
              {notification.type === 'error' && <AlertCircle className="w-5 h-5" />}
              {notification.type === 'info' && <Database className="w-5 h-5" />}
              <p className="font-medium text-sm">{notification.message}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RateUpdateManager;