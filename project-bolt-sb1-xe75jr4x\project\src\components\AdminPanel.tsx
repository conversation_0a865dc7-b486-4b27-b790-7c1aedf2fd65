import React, { useState, useEffect, useRef } from 'react';
import { Settings, Users, MessageCircle, BarChart3, Shield, Database, Globe, Mail, Phone, MapPin, Star, CheckCircle, XCircle, Clock, User, Calendar, Filter, Search, X, AlertCircle, Building, Activity, RefreshCw, TrendingUp, Eye, Trash2 } from 'lucide-react';
import adminAPI, { getActiveExchangersForMainPage, getActiveExchangersCount } from '../data/adminData';
import { adminAuthService } from '../services/adminAuthService';
import { supabaseService } from '../services/supabaseClient';
import { pageStabilityMonitor } from '../utils/pageStabilityMonitor';
import UserManagement from './admin/UserManagement';
import SystemSettings from './admin/SystemSettings';
import SystemHealthCheck from './admin/SystemHealthCheck';
import Analytics from './admin/Analytics';
import ExchangerManagement from './admin/ExchangerManagement';
import RateUpdateManager from './admin/RateUpdateManager';
import ParsingResultsTable from './admin/ParsingResultsFixed';
import HistoricalRatesTrends from './admin/HistoricalRatesFixed';

interface AdminPanelProps {
  onLogout: () => void;
}

interface Review {
  id: number;
  author: string;
  rating: number;
  comment: string;
  date: string;
  status: 'pending' | 'approved' | 'rejected';
  exchangerId: number;
  email?: string;
  moderatedAt?: string;
}

const AdminPanel: React.FC<AdminPanelProps> = ({ onLogout }) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [pendingReviews, setPendingReviews] = useState<Review[]>([]);
  const [adminStats, setAdminStats] = useState<any>(null);
  const [currentAdmin, setCurrentAdmin] = useState<any>(null);
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
  } | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [realTimeStats, setRealTimeStats] = useState<any>(null);

  // Track logged events to prevent duplicates
  const loggedEventsRef = useRef(new Set<string>());

  // Мониторинг стабильности страницы
  const [stabilityReport, setStabilityReport] = useState<any>(null);

  // Prevent duplicate logging while allowing important admin actions
  const logOnce = (eventKey: string, logFunction: () => void, forceLog = false) => {
    // Always log if forceLog is true (for important admin actions)
    if (forceLog || !loggedEventsRef.current.has(eventKey)) {
      logFunction();

      // Only track non-forced logs to prevent duplicates
      if (!forceLog) {
        loggedEventsRef.current.add(eventKey);

        // Clean up old events (keep only last 100)
        if (loggedEventsRef.current.size > 100) {
          const oldEvents = Array.from(loggedEventsRef.current).slice(0, 50);
          oldEvents.forEach(event => loggedEventsRef.current.delete(event));
        }
      }
    }
  };

  const tabs = [
    { id: 'dashboard', label: 'Дашборд', icon: BarChart3 },
    { id: 'reviews', label: 'Управление отзывами', icon: MessageCircle },
    { id: 'exchangers', label: 'Обменники', icon: Building },
    { id: 'rates', label: 'Обновление курсов', icon: Database },
    { id: 'parsing-results', label: 'Результаты парсинга', icon: Database },
    { id: 'historical-trends', label: 'Исторические тренды', icon: TrendingUp },
    { id: 'users', label: 'Пользователи', icon: Users },
    { id: 'analytics', label: 'Аналитика', icon: Activity },
    { id: 'settings', label: 'Настройки', icon: Settings },
    { id: 'health', label: 'Проверка системы', icon: Activity }
  ];

  // Initialize current admin on component mount
  useEffect(() => {
    const admin = adminAuthService.getCurrentAdmin();
    setCurrentAdmin(admin);
  }, []);

  useEffect(() => {
    loadAdminData();
    setLastRefresh(new Date());

    // Получаем отчет о стабильности страницы
    setStabilityReport(pageStabilityMonitor.getStabilityReport());

    if (activeTab === 'reviews') {
      loadReviews();
    }
  }, [activeTab]);

  // Auto-refresh data every 5 minutes
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    
    const interval = setInterval(() => {
      if (!refreshing) {
        loadAdminData();
        if (activeTab === 'reviews') {
          loadReviews();
        }
        setLastRefresh(new Date());
        
        // Обновляем отчет о стабильности
        setStabilityReport(pageStabilityMonitor.getStabilityReport());
      }
    }, 5 * 60 * 1000); // 5 minutes

    intervalId = interval;
    
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [activeTab, refreshing]);

  const loadRealTimeStats = async () => {
    try {
      // Get real data from adminAPI
      const dashboardStats = await adminAPI.getDashboardStats();
      
      // Get active exchangers count from current data
      const activeExchangersCount = getActiveExchangersCount();
      
      // Get real reviews data
      const reviewsData = await adminAPI.getAllReviews(1, 1000, 'all');
      
      const realStats = {
        totalExchangers: activeExchangersCount,
        activeExchangers: dashboardStats.activeExchangers || activeExchangersCount,
        inactiveExchangers: dashboardStats.inactiveExchangers || 0,
        pendingExchangers: dashboardStats.pendingExchangers || 0,
        totalUsers: dashboardStats.totalUsers || 0,
        activeUsers: dashboardStats.activeUsers || 0,
        totalReviews: reviewsData.totalCount || 0,
        pendingReviews: reviewsData.statusCounts?.pending || 0,
        approvedReviews: reviewsData.statusCounts?.approved || 0,
        rejectedReviews: reviewsData.statusCounts?.rejected || 0,
        totalRates: dashboardStats.totalRates || 0,
        todayVisits: dashboardStats.todayVisits || 0,
        monthlyGrowth: dashboardStats.monthlyGrowth || 0,
        topExchangers: dashboardStats.topExchangers || [],
        lastUpdate: new Date().toISOString()
      };
      
      setRealTimeStats(realStats);
      const statsKey = `real_time_stats_${Math.floor(Date.now() / 300000)}`; // Group by 5 minutes
      logOnce(statsKey, () => {
        console.log('Real-time stats loaded:', realStats);
      });
      
    } catch (error) {
      console.error('Error loading real-time stats:', error);
      // Fallback to basic stats
      setRealTimeStats({
        totalExchangers: getActiveExchangersCount(),
        activeExchangers: getActiveExchangersCount(),
        inactiveExchangers: 0,
        pendingExchangers: 0,
        totalUsers: 0,
        activeUsers: 0,
        totalReviews: 0,
        pendingReviews: 0,
        approvedReviews: 0,
        rejectedReviews: 0,
        totalRates: 0,
        todayVisits: 0,
        monthlyGrowth: 0,
        topExchangers: [],
        lastUpdate: new Date().toISOString()
      });
    }
  };

  const getExchangerName = (exchangerId: number) => {
    // Get exchanger name from admin data
    const exchanger = (adminExchangers || []).find(e => e && e.id === exchangerId.toString());
    if (exchanger) {
      return exchanger.name;
    }
    
    // Fallback to mock data
    const mockExchanger = (allExchangers || []).find(e => e && e.id === exchangerId);
    if (mockExchanger) {
      return mockExchanger.name;
    }
    
    return `Обменник #${exchangerId}`;
  };

  const loadAdminData = async () => {
    setRefreshing(true);
    let hasErrors = false;

    try {
      // Load dashboard stats and real-time stats in parallel to avoid circular dependency
      const [dashboardResult, reviewsResult, authResult] = await Promise.allSettled([
        adminAPI.getDashboardStats(),
        adminAPI.getAllReviews(1, 1000, 'all'),
        adminAuthService.getAdminStats()
      ]);

      // Process dashboard stats
      if (dashboardResult.status === 'fulfilled') {
        const dashboardData = dashboardResult.value;
        setDashboardData(dashboardData);

        // Create real-time stats from dashboard data
        const activeExchangersCount = getActiveExchangersCount();
        const realStats = {
          totalExchangers: activeExchangersCount,
          activeExchangers: dashboardData.activeExchangers || activeExchangersCount,
          inactiveExchangers: dashboardData.inactiveExchangers || 0,
          pendingExchangers: dashboardData.pendingExchangers || 0,
          totalUsers: dashboardData.totalUsers || 0,
          activeUsers: dashboardData.activeUsers || 0,
          totalReviews: reviewsResult.status === 'fulfilled' ? (reviewsResult.value.totalCount || 0) : 0,
          pendingReviews: reviewsResult.status === 'fulfilled' ? (reviewsResult.value.statusCounts?.pending || 0) : 0,
          approvedReviews: reviewsResult.status === 'fulfilled' ? (reviewsResult.value.statusCounts?.approved || 0) : 0,
          rejectedReviews: reviewsResult.status === 'fulfilled' ? (reviewsResult.value.statusCounts?.rejected || 0) : 0,
          totalRates: dashboardData.totalRates || 0,
          todayVisits: dashboardData.todayVisits || 0,
          monthlyGrowth: dashboardData.monthlyGrowth || 0,
          topExchangers: dashboardData.topExchangers || [],
          lastUpdate: new Date().toISOString()
        };
        setRealTimeStats(realStats);

        // Log successful data load
        const successKey = `admin_data_success_${Math.floor(Date.now() / 600000)}`; // 10-minute intervals
        logOnce(successKey, () => {
          console.log('Admin dashboard data loaded successfully:', {
            totalExchangers: dashboardData.totalExchangers,
            activeExchangers: dashboardData.activeExchangers,
            totalUsers: dashboardData.totalUsers,
            dataSource: dashboardData.dataSource
          });
        });

      } else {
        hasErrors = true;
        const apiErrorKey = `api_error_${Math.floor(Date.now() / 300000)}`;
        logOnce(apiErrorKey, () => {
          console.warn('Dashboard API error:', dashboardResult.reason);
        });

        // Create fallback data
        const activeExchangersCount = getActiveExchangersCount();
        const fallbackData = {
          totalExchangers: activeExchangersCount,
          activeExchangers: activeExchangersCount,
          totalUsers: 0,
          dataSource: 'fallback',
          error: 'API unavailable'
        };
        setDashboardData(fallbackData);
        setRealTimeStats(fallbackData);
      }

      // Process admin auth stats
      if (authResult.status === 'fulfilled') {
        setAdminStats(authResult.value);
      } else {
        hasErrors = true;
        const authErrorKey = `auth_error_${Math.floor(Date.now() / 300000)}`;
        logOnce(authErrorKey, () => {
          console.warn('Admin auth stats error:', authResult.reason);
        });

        // Set fallback auth stats
        setAdminStats({
          totalUsers: 0,
          adminSessions: 1,
          lastUpdate: new Date().toISOString(),
          error: 'Auth stats unavailable'
        });
      }

      // Show success notification only if no errors and manual refresh
      if (!hasErrors && refreshing) {
        showNotification('success', 'Данные администратора обновлены');
      }

    } catch (error) {
      // Always log critical errors
      console.error('Critical error loading admin data:', error);
      showNotification('error', 'Критическая ошибка загрузки данных администратора');

      // Set emergency fallback data
      const activeExchangersCount = getActiveExchangersCount();
      const emergencyData = {
        totalExchangers: activeExchangersCount,
        activeExchangers: activeExchangersCount,
        totalUsers: 0,
        dataSource: 'emergency_fallback',
        error: 'Critical error'
      };
      setDashboardData(emergencyData);
      setRealTimeStats(emergencyData);
      setAdminStats({
        totalUsers: 0,
        adminSessions: 1,
        lastUpdate: new Date().toISOString(),
        error: 'Critical error'
      });
    } finally {
      setRefreshing(false);
    }
  };

  const loadReviews = async () => {
    setIsLoading(true);
    try {
      // Load all reviews using the API
      try {
        const data = await adminAPI.getAllReviews(1, 100, 'all');
        setReviews(data.reviews || []);
        setPendingReviews(data.reviews || []);
        
        console.log('Reviews loaded successfully:', {
          total: data.totalCount,
          pending: data.statusCounts?.pending || 0,
          approved: data.statusCounts?.approved || 0,
          rejected: data.statusCounts?.rejected || 0
        });
      } catch (apiError) {
        console.warn('API not available for reviews, using mock data:', apiError);
        
        // Generate mock reviews if none exist
        const mockReviews = [
          {
            id: 1,
            author: 'Иван Петров',
            rating: 5,
            comment: 'Отличный обменник, быстро и надежно!',
            date: '2024-01-15',
            status: 'pending' as const,
            exchangerId: 1,
            email: '<EMAIL>'
          },
          {
            id: 2,
            author: 'Мария Сидорова',
            rating: 4,
            comment: 'Хороший сервис, рекомендую',
            date: '2024-01-14',
            status: 'approved' as const,
            exchangerId: 2,
            email: '<EMAIL>',
            moderatedAt: '2024-01-14T10:30:00Z'
          },
          {
            id: 3,
            author: 'Алексей Иванов',
            rating: 5,
            comment: 'Лучший курс в городе, всегда обмениваю здесь',
            date: '2024-01-13',
            status: 'pending' as const,
            exchangerId: 1,
            email: '<EMAIL>'
          }
        ];
        
        setReviews(mockReviews);
        setPendingReviews(mockReviews);
      }
    } catch (error) {
      console.error('Error loading reviews:', error);
      showNotification('error', 'Ошибка загрузки отзывов');
      // Set empty arrays as fallback
      setReviews([]);
      setPendingReviews([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleManualRefresh = async () => {
    setRefreshing(true);
    await loadAdminData();
    if (activeTab === 'reviews') {
      await loadReviews();
    }
    setLastRefresh(new Date());
    showNotification('success', 'Данные обновлены');
  };

  const handleApproveReview = async (reviewId: number) => {
    try {
      await adminAPI.moderateReview(reviewId, 'approve');

      // Always log admin actions (force logging)
      logOnce(`review_approved_${reviewId}`, () => {
        console.log(`Admin action: Review ${reviewId} approved by ${currentAdmin?.login || 'admin'}`);
      }, true);

      showNotification('success', 'Отзыв успешно одобрен');
      loadReviews();
    } catch (error) {
      // Always log errors for admin actions
      logOnce(`review_approve_error_${reviewId}`, () => {
        console.error('Error approving review:', error);
      }, true);
      showNotification('error', 'Ошибка при одобрении отзыва');
    }
  };

  const handleRejectReview = async (reviewId: number) => {
    try {
      await adminAPI.moderateReview(reviewId, 'reject');

      // Always log admin actions (force logging)
      logOnce(`review_rejected_${reviewId}`, () => {
        console.log(`Admin action: Review ${reviewId} rejected by ${currentAdmin?.login || 'admin'}`);
      }, true);

      showNotification('success', 'Отзыв отклонен');
      loadReviews();
    } catch (error) {
      // Always log errors for admin actions
      logOnce(`review_reject_error_${reviewId}`, () => {
        console.error('Error rejecting review:', error);
      }, true);
      showNotification('error', 'Ошибка при отклонении отзыва');
    }
  };

  const handleDeleteReview = async (reviewId: number) => {
    if (!confirm('Вы уверены, что хотите удалить этот отзыв? Это действие нельзя отменить.')) return;
    
    try {
      await adminAPI.deleteReview(reviewId);
      showNotification('success', 'Отзыв успешно удален');
      loadReviews();
    } catch (error) {
      showNotification('error', 'Ошибка удаления отзыва');
    }
  };

  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  const filteredReviews = (reviews || []).filter(review => {
    if (!review) return false;
    const matchesStatus = filterStatus === 'all' || review.status === filterStatus;
    const matchesSearch = searchQuery === '' || 
      (review.author && review.author.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (review.comment && review.comment.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesStatus && matchesSearch;
  });

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-4 h-4 ${
              i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'approved': return <CheckCircle className="w-4 h-4" />;
      case 'rejected': return <XCircle className="w-4 h-4" />;
      default: return <MessageCircle className="w-4 h-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'На модерации';
      case 'approved': return 'Одобрен';
      case 'rejected': return 'Отклонен';
      default: return 'Неизвестно';
    }
  };

  const renderDashboard = () => (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <h3 className="text-xl font-bold text-gray-800">Обзор системы</h3>
        <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4">
          {currentAdmin && (
            <div className="text-sm text-gray-600">
              <div className="font-medium">Администратор: <strong>{currentAdmin.login}</strong></div>
              <div className="text-xs text-gray-500">
                Вход: {new Date(currentAdmin.loginTime).toLocaleString('ru-RU')}
              </div>
            </div>
          )}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleManualRefresh}
              disabled={refreshing}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-3 py-2 rounded-lg text-sm flex items-center space-x-2 transition-colors"
              title="Обновить данные"
            >
              <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span>{refreshing ? 'Обновление...' : 'Обновить'}</span>
            </button>
            <div className="text-xs text-gray-500">
              Обновлено: {lastRefresh.toLocaleTimeString('ru-RU')}
            </div>
          </div>
        </div>
      </div>
      
      {/* System Status */}
      {supabaseService.isConnected ? (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 text-green-800">
            <CheckCircle className="w-5 h-5" />
            <span className="font-medium">Supabase подключен</span>
          </div>
          <p className="text-green-700 text-sm mt-1">
            База данных работает нормально. Все функции доступны.
          </p>
        </div>
      ) : (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 text-yellow-800">
            <AlertCircle className="w-5 h-5" />
            <span className="font-medium">Режим разработки</span>
          </div>
          <p className="text-yellow-700 text-sm mt-1">
            Используются локальные данные. Для полной функциональности подключите Supabase.
          </p>
        </div>
      )}
      
      {/* Page Stability Status */}
      {stabilityReport && !stabilityReport.isStable && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 text-red-800">
            <AlertCircle className="w-5 h-5" />
            <span className="font-medium">Обнаружены проблемы стабильности</span>
          </div>
          <div className="text-red-700 text-sm mt-1">
            Перезагрузок: {stabilityReport.reloadCount} | Ошибок: {stabilityReport.errorCount}
            {stabilityReport.performanceIssues.length > 0 && (
              <span> | Проблемы производительности: {stabilityReport.performanceIssues.length}</span>
            )}
          </div>
          <button
            onClick={() => {
              pageStabilityMonitor.resetCounters();
              setStabilityReport(pageStabilityMonitor.getStabilityReport());
            }}
            className="mt-2 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
          >
            Сбросить счетчики
          </button>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-600 text-sm font-medium">Всего обменников</p>
              <p className="text-2xl font-bold text-blue-800">
                {realTimeStats?.totalExchangers || dashboardData?.totalExchangers || 0}
              </p>
              <p className="text-xs text-blue-600 mt-1 flex items-center">
                <TrendingUp className="w-3 h-3 mr-1" />
                Активных: {realTimeStats?.activeExchangers || dashboardData?.activeExchangers || 0}
              </p>
            </div>
            <Shield className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-green-50 p-6 rounded-xl border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-600 text-sm font-medium">Всего пользователей</p>
              <p className="text-2xl font-bold text-green-800">
                {realTimeStats?.totalUsers || adminStats?.totalUsers || 0}
              </p>
              <p className="text-xs text-green-600 mt-1">
                Активных: {realTimeStats?.activeUsers || adminStats?.activeUsers || 0}
              </p>
            </div>
            <Users className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-yellow-50 p-6 rounded-xl border border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-600 text-sm font-medium">Отзывов на модерации</p>
              <p className="text-2xl font-bold text-yellow-800">
                {realTimeStats?.pendingReviews || adminStats?.pendingReviews || 0}
              </p>
              <p className="text-xs text-yellow-600 mt-1">
                Всего отзывов: {realTimeStats?.totalReviews || adminStats?.totalReviews || 0}
              </p>
            </div>
            <Clock className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
        
        <div className="bg-purple-50 p-6 rounded-xl border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-600 text-sm font-medium">Курсов валют</p>
              <p className="text-2xl font-bold text-purple-800">
                {realTimeStats?.totalRates || adminStats?.totalRates || 0}
              </p>
              <p className="text-xs text-purple-600 mt-1">
                Обновлено: {realTimeStats?.lastUpdate ? new Date(realTimeStats.lastUpdate).toLocaleTimeString('ru-RU') : 'Неизвестно'}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>
      
      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow border border-gray-200 p-6">
        <h4 className="text-lg font-semibold text-gray-800 mb-4">Быстрые действия</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => setActiveTab('reviews')}
            className="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 p-4 rounded-lg text-left transition-colors"
          >
            <MessageCircle className="w-6 h-6 mb-2" />
            <div className="font-medium">Управление отзывами</div>
            <div className="text-sm opacity-75">
              {realTimeStats?.pendingReviews || adminStats?.pendingReviews || 0} на модерации
            </div>
          </button>
          
          <button
            onClick={() => setActiveTab('users')}
            className="bg-blue-100 hover:bg-blue-200 text-blue-800 p-4 rounded-lg text-left transition-colors"
          >
            <Users className="w-6 h-6 mb-2" />
            <div className="font-medium">Управление пользователями</div>
            <div className="text-sm opacity-75">
              {realTimeStats?.totalUsers || adminStats?.totalUsers || 0} пользователей
            </div>
          </button>
          
          <button
            onClick={() => setActiveTab('analytics')}
            className="bg-green-100 hover:bg-green-200 text-green-800 p-4 rounded-lg text-left transition-colors"
          >
            <BarChart3 className="w-6 h-6 mb-2" />
            <div className="font-medium">Аналитика</div>
            <div className="text-sm opacity-75">Отчеты и статистика</div>
          </button>
        </div>
      </div>
    </div>
  );

  const renderReviewsModeration = () => (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <h3 className="text-xl font-bold text-gray-800">Управление отзывами</h3>
        <div className="flex items-center space-x-3">
          <div className="text-sm text-gray-600">
            Всего: <strong>{realTimeStats?.totalReviews || 0}</strong> | 
            На модерации: <strong>{(reviews || []).filter(r => r && r.status === 'pending').length}</strong> |
            Одобрено: <strong>{(reviews || []).filter(r => r && r.status === 'approved').length}</strong>
          </div>
          <button
            onClick={loadReviews}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>{isLoading ? 'Загрузка...' : 'Обновить'}</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex flex-wrap items-center gap-4">
            <Filter className="w-5 h-5 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Фильтры:</span>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 bg-white"
            >
              <option value="all">Все отзывы</option>
              <option value="pending">На модерации</option>
              <option value="approved">Одобренные</option>
              <option value="rejected">Отклоненные</option>
            </select>
          </div>
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Поиск по автору или тексту..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 w-full md:w-80 bg-white"
            />
          </div>
        </div>
      </div>

      {/* Review Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 text-center">
          <div className="text-2xl font-bold text-blue-800">{(reviews || []).length}</div>
          <div className="text-sm text-blue-600">Всего отзывов</div>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 text-center">
          <div className="text-2xl font-bold text-yellow-800">{(reviews || []).filter(r => r && r.status === 'pending').length}</div>
          <div className="text-sm text-yellow-600">На модерации</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg border border-green-200 text-center">
          <div className="text-2xl font-bold text-green-800">{(reviews || []).filter(r => r && r.status === 'approved').length}</div>
          <div className="text-sm text-green-600">Одобрено</div>
        </div>
        <div className="bg-red-50 p-4 rounded-lg border border-red-200 text-center">
          <div className="text-2xl font-bold text-red-800">{(reviews || []).filter(r => r && r.status === 'rejected').length}</div>
          <div className="text-sm text-red-600">Отклонено</div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-4 max-h-[600px] overflow-y-auto bg-gray-50 p-4 rounded-lg">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Загрузка отзывов...</p>
          </div>
        ) : filteredReviews.length === 0 ? (
          <div className="text-center py-8">
            <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">
              {filterStatus === 'all' ? 'Отзывы не найдены' : 
               filterStatus === 'pending' ? 'Нет отзывов на модерации' :
               filterStatus === 'approved' ? 'Нет одобренных отзывов' :
               'Нет отклоненных отзывов'}
            </p>
            <p className="text-gray-500 text-sm mt-2">
              {searchQuery ? 'Попробуйте изменить поисковый запрос' : 'Отзывы появятся после их добавления пользователями'}
            </p>
            <button
              onClick={() => {
                setSearchQuery('');
                setFilterStatus('all');
                loadReviews();
              }}
              className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm"
            >
              Сбросить фильтры
            </button>
          </div>
        ) : (
          filteredReviews.map((review) => (
            <div key={review.id} className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="bg-gradient-to-br from-blue-500 to-purple-500 w-10 h-10 rounded-full flex items-center justify-center shadow-md">
                    <User className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">{review.author}</h4>
                    <div className="flex items-center space-x-2 mt-1">
                      {renderStars(review.rating)}
                      <span className="text-sm text-gray-500">
                        <Calendar className="w-4 h-4 inline mr-1" />
                        {new Date(review.date).toLocaleDateString('ru-RU')}
                      </span>
                    </div>
                  </div>
                </div>
                
                <span className={`px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1 ${getStatusColor(review.status)}`}>
                  {getStatusIcon(review.status)}
                  <span>{getStatusText(review.status)}</span>
                </span>
              </div>
              
              <p className="text-gray-700 mb-4 leading-relaxed">{review.comment}</p>
              
              {/* Exchanger Info */}
              <div className="text-sm text-gray-600 mb-3 bg-gray-50 p-2 rounded">
                <strong>Обменник:</strong> {getExchangerName(review.exchangerId)}
                {review.email && <span className="ml-4"><strong>Email:</strong> {review.email}</span>}
              </div>
              
              {review.status === 'pending' && (
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleApproveReview(review.id)}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 hover:shadow-md"
                  >
                    <CheckCircle className="w-4 h-4" />
                    <span>Одобрить</span>
                  </button>
                  <button
                    onClick={() => handleRejectReview(review.id)}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 hover:shadow-md"
                  >
                    <XCircle className="w-4 h-4" />
                    <span>Отклонить</span>
                  </button>
                  <button
                    onClick={() => handleDeleteReview(review.id)}
                    className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 hover:shadow-md"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>Удалить</span>
                  </button>
                  <div className="text-xs text-gray-500 ml-auto">
                    ID: {review.id}
                  </div>
                </div>
              )}
              
              {/* Actions for all reviews */}
              <div className="flex items-center justify-between text-xs text-gray-500 mt-3 pt-3 border-t border-gray-100">
                <div className="flex items-center space-x-4">
                  <span>Статус: {getStatusText(review.status)}</span>
                  <span>ID: {review.id}</span>
                  {review.moderatedAt && (
                    <span>Модерирован: {new Date(review.moderatedAt).toLocaleDateString('ru-RU')}</span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  {review.status !== 'pending' && (
                    <>
                      {review.status === 'approved' && (
                        <button
                          onClick={() => handleRejectReview(review.id)}
                          className="text-red-600 hover:text-red-800 text-xs underline"
                        >
                          Отклонить
                        </button>
                      )}
                      {review.status === 'rejected' && (
                        <button
                          onClick={() => handleApproveReview(review.id)}
                          className="text-green-600 hover:text-green-800 text-xs underline"
                        >
                          Одобрить
                        </button>
                      )}
                    </>
                  )}
                  <button
                    onClick={() => handleDeleteReview(review.id)}
                    className="text-red-600 hover:text-red-800 text-xs underline"
                  >
                    Удалить
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );

  const renderUsers = () => <UserManagement />;

  const renderSettings = () => <SystemSettings />;

  const renderHealthCheck = () => <SystemHealthCheck />;

  const renderAnalytics = () => <Analytics />;

  const renderExchangers = () => <ExchangerManagement />;

  const renderRates = () => <RateUpdateManager />;

  const renderParsingResults = () => <ParsingResultsTable />;

  const renderHistoricalTrends = () => <HistoricalRatesTrends />;

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard': return renderDashboard();
      case 'reviews': return renderReviewsModeration();
      case 'exchangers': return renderExchangers();
      case 'rates': return renderRates();
      case 'parsing-results': return renderParsingResults();
      case 'historical-trends': return renderHistoricalTrends();
      case 'users': return renderUsers();
      case 'analytics': return renderAnalytics();
      case 'settings': return renderSettings();
      case 'health': return renderHealthCheck();
      default: return renderDashboard();
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="bg-white shadow-lg">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Shield className="w-6 h-6" />
              <div>
                <h2 className="text-xl font-bold">Административная панель</h2>
                <p className="text-blue-100 text-sm">Управление системой обменников</p>
              </div>
            </div>
            <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4">
              <div className="text-right text-sm text-blue-100">
                <div>Последнее обновление:</div>
                <div className="font-medium">{lastRefresh.toLocaleTimeString('ru-RU')}</div>
              </div>
              <a
                href="/"
                className="text-blue-100 hover:text-white text-sm underline transition-colors"
              >
                ← На главную
              </a>
              <button
                onClick={onLogout}
                className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg text-sm transition-all duration-200 hover:shadow-md"
              >
                Выйти
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row bg-white rounded-xl shadow-lg overflow-hidden min-h-[600px]">
          {/* Sidebar */}
          <div className="w-full lg:w-64 bg-gray-50 p-4 border-b lg:border-b-0 lg:border-r border-gray-200">
            <nav className="space-y-2">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-3 px-2">
                Разделы управления
              </div>
              {tabs.map((tab) => {
                const Icon = tab.icon;
                const pendingCount = tab.id === 'reviews' ? (reviews || []).filter(r => r && r.status === 'pending').length : 0;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-blue-600 text-white shadow-md'
                        : 'text-gray-700 hover:bg-gray-200 hover:shadow-sm'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{tab.label}</span>
                    {pendingCount > 0 && (
                      <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 ml-auto animate-pulse">
                        {pendingCount}
                      </span>
                    )}
                  </button>
                );
              })}
            </nav>
            
            {/* Sidebar Footer */}
            <div className="mt-8 pt-4 border-t border-gray-200">
              <div className="text-xs text-gray-500 space-y-1">
                <div className="flex items-center justify-between">
                  <span>Статус системы:</span>
                  <span className="text-green-600 font-medium">Онлайн</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Режим:</span>
                  <span className="text-blue-600 font-medium">
                    {supabaseService.isConnected ? 'Продакшн' : 'Разработка'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-6">
            {refreshing && (
              <div className="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center space-x-2 text-blue-800">
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span className="text-sm font-medium">Обновление данных...</span>
                </div>
              </div>
            )}
            {renderContent()}
          </div>
        </div>
      </div>

      {/* Notification Toast */}
      {notification && (
        <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-right duration-300 max-w-sm">
          <div className={`rounded-lg shadow-lg p-4 max-w-sm ${
            notification.type === 'success' ? 'bg-green-500 text-white' :
            notification.type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
          }`}>
            <div className="flex items-center space-x-3">
              {notification.type === 'success' && <CheckCircle className="w-5 h-5" />}
              {notification.type === 'error' && <AlertCircle className="w-5 h-5" />}
              {notification.type === 'info' && <MessageCircle className="w-5 h-5" />}
              <div>
                <p className="font-medium text-sm">{notification.message}</p>
              </div>
              <button
                onClick={() => setNotification(null)}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminPanel;