export interface MapDistrict {
  id: string;
  name: string;
  nameEn: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  boundaries: Array<{lat: number; lng: number}>;
  color: string;
  population?: number;
  area?: number; // in km²
  description: string;
}

export interface Landmark {
  id: string;
  name: string;
  nameEn: string;
  type: 'beach' | 'temple' | 'market' | 'airport' | 'hospital' | 'shopping' | 'attraction';
  coordinates: {
    lat: number;
    lng: number;
  };
  district: string;
}

export interface Road {
  id: string;
  name: string;
  type: 'highway' | 'main' | 'secondary' | 'local';
  coordinates: Array<{lat: number; lng: number}>;
  color: string;
  width: number;
}

export const phuketDistricts: MapDistrict[] = [
  {
    id: 'patong',
    name: 'Патонг',
    nameEn: 'Patong',
    coordinates: { lat: 7.8963, lng: 98.2964 },
    boundaries: [
      { lat: 7.9100, lng: 98.2850 },
      { lat: 7.9100, lng: 98.3100 },
      { lat: 7.8800, lng: 98.3100 },
      { lat: 7.8800, lng: 98.2850 }
    ],
    color: '#FF6B6B',
    population: 15000,
    area: 12.5,
    description: 'Главный туристический район с пляжем и ночной жизнью'
  },
  {
    id: 'karon',
    name: 'Карон',
    nameEn: 'Karon',
    coordinates: { lat: 7.8361, lng: 98.2911 },
    boundaries: [
      { lat: 7.8500, lng: 98.2800 },
      { lat: 7.8500, lng: 98.3050 },
      { lat: 7.8200, lng: 98.3050 },
      { lat: 7.8200, lng: 98.2800 }
    ],
    color: '#4ECDC4',
    population: 8000,
    area: 15.2,
    description: 'Спокойный район с длинным пляжем'
  },
  {
    id: 'kata',
    name: 'Ката',
    nameEn: 'Kata',
    coordinates: { lat: 7.8169, lng: 98.2836 },
    boundaries: [
      { lat: 7.8300, lng: 98.2750 },
      { lat: 7.8300, lng: 98.2950 },
      { lat: 7.8000, lng: 98.2950 },
      { lat: 7.8000, lng: 98.2750 }
    ],
    color: '#45B7D1',
    population: 6000,
    area: 8.7,
    description: 'Уютный район с двумя пляжами'
  },
  {
    id: 'phuket-town',
    name: 'Пхукет Таун',
    nameEn: 'Phuket Town',
    coordinates: { lat: 7.8804, lng: 98.3923 },
    boundaries: [
      { lat: 7.9000, lng: 98.3700 },
      { lat: 7.9000, lng: 98.4200 },
      { lat: 7.8600, lng: 98.4200 },
      { lat: 7.8600, lng: 98.3700 }
    ],
    color: '#96CEB4',
    population: 75000,
    area: 25.3,
    description: 'Административный и культурный центр острова'
  },
  {
    id: 'rawai',
    name: 'Равай',
    nameEn: 'Rawai',
    coordinates: { lat: 7.7753, lng: 98.3267 },
    boundaries: [
      { lat: 7.7900, lng: 98.3100 },
      { lat: 7.7900, lng: 98.3450 },
      { lat: 7.7600, lng: 98.3450 },
      { lat: 7.7600, lng: 98.3100 }
    ],
    color: '#FFEAA7',
    population: 12000,
    area: 18.9,
    description: 'Южный район с рыбацкими деревнями'
  },
  {
    id: 'kamala',
    name: 'Камала',
    nameEn: 'Kamala',
    coordinates: { lat: 7.9633, lng: 98.2814 },
    boundaries: [
      { lat: 7.9800, lng: 98.2700 },
      { lat: 7.9800, lng: 98.2950 },
      { lat: 7.9450, lng: 98.2950 },
      { lat: 7.9450, lng: 98.2700 }
    ],
    color: '#DDA0DD',
    population: 5000,
    area: 11.4,
    description: 'Тихий район к северу от Патонга'
  },
  {
    id: 'bang-tao',
    name: 'Банг Тао',
    nameEn: 'Bang Tao',
    coordinates: { lat: 8.0200, lng: 98.2900 },
    boundaries: [
      { lat: 8.0400, lng: 98.2750 },
      { lat: 8.0400, lng: 98.3100 },
      { lat: 8.0000, lng: 98.3100 },
      { lat: 8.0000, lng: 98.2750 }
    ],
    color: '#FFB6C1',
    population: 7000,
    area: 14.6,
    description: 'Элитный район с роскошными отелями'
  },
  {
    id: 'nai-harn',
    name: 'Най Харн',
    nameEn: 'Nai Harn',
    coordinates: { lat: 7.7650, lng: 98.3050 },
    boundaries: [
      { lat: 7.7800, lng: 98.2950 },
      { lat: 7.7800, lng: 98.3200 },
      { lat: 7.7500, lng: 98.3200 },
      { lat: 7.7500, lng: 98.2950 }
    ],
    color: '#98FB98',
    population: 3000,
    area: 6.8,
    description: 'Живописный район на юге острова'
  }
];

export const landmarks: Landmark[] = [
  {
    id: 'patong-beach',
    name: 'Пляж Патонг',
    nameEn: 'Patong Beach',
    type: 'beach',
    coordinates: { lat: 7.8963, lng: 98.2900 },
    district: 'patong'
  },
  {
    id: 'big-buddha',
    name: 'Большой Будда',
    nameEn: 'Big Buddha',
    type: 'temple',
    coordinates: { lat: 7.8200, lng: 98.3100 },
    district: 'kata'
  },
  {
    id: 'phuket-airport',
    name: 'Аэропорт Пхукета',
    nameEn: 'Phuket Airport',
    type: 'airport',
    coordinates: { lat: 8.1132, lng: 98.3169 },
    district: 'mai-khao'
  },
  {
    id: 'weekend-market',
    name: 'Уикенд Маркет',
    nameEn: 'Weekend Market',
    type: 'market',
    coordinates: { lat: 7.8804, lng: 98.3850 },
    district: 'phuket-town'
  },
  {
    id: 'central-festival',
    name: 'Централ Фестиваль',
    nameEn: 'Central Festival',
    type: 'shopping',
    coordinates: { lat: 7.8900, lng: 98.2950 },
    district: 'patong'
  },
  {
    id: 'wat-chalong',
    name: 'Ват Чалонг',
    nameEn: 'Wat Chalong',
    type: 'temple',
    coordinates: { lat: 7.8400, lng: 98.3500 },
    district: 'chalong'
  },
  {
    id: 'karon-beach',
    name: 'Пляж Карон',
    nameEn: 'Karon Beach',
    type: 'beach',
    coordinates: { lat: 7.8361, lng: 98.2850 },
    district: 'karon'
  },
  {
    id: 'kata-beach',
    name: 'Пляж Ката',
    nameEn: 'Kata Beach',
    type: 'beach',
    coordinates: { lat: 7.8169, lng: 98.2800 },
    district: 'kata'
  }
];

export const roads: Road[] = [
  {
    id: 'highway-4030',
    name: 'Шоссе 4030',
    type: 'highway',
    coordinates: [
      { lat: 8.1000, lng: 98.3200 },
      { lat: 7.9500, lng: 98.3300 },
      { lat: 7.8800, lng: 98.3900 },
      { lat: 7.8000, lng: 98.3400 }
    ],
    color: '#FF4444',
    width: 4
  },
  {
    id: 'patong-beach-road',
    name: 'Патонг Бич Роуд',
    type: 'main',
    coordinates: [
      { lat: 7.9100, lng: 98.2950 },
      { lat: 7.8800, lng: 98.2950 }
    ],
    color: '#4444FF',
    width: 3
  },
  {
    id: 'karon-beach-road',
    name: 'Карон Бич Роуд',
    type: 'main',
    coordinates: [
      { lat: 7.8500, lng: 98.2900 },
      { lat: 7.8200, lng: 98.2900 }
    ],
    color: '#4444FF',
    width: 3
  }
];

export const mapBounds = {
  north: 8.1500,
  south: 7.7000,
  east: 98.4500,
  west: 98.2500
};

export const mapCenter = {
  lat: 7.9250,
  lng: 98.3500
};