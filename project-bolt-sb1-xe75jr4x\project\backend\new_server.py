#!/usr/bin/env python3
"""
Простой HTTP сервер для тестирования API админки
"""

import json
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
from datetime import datetime, timedelta
import random
import sys

PORT = 8000

class Handler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        try:
            print(f"Request: {self.path}")
            
            # Добавляем CORS заголовки
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            
            parsed = urlparse(self.path)
            path = parsed.path
            
            response = {}
            
            if path == '/health':
                response = {
                    "status": "ok",
                    "timestamp": datetime.now().isoformat(),
                    "server": "new_server.py"
                }
            
            elif path == '/api/v1/parsing/results':
                # Результаты парсинга для админки
                results = []
                statuses = ['success', 'failed', 'error', 'partial']
                exchangers = ['SuperRich Thailand', 'Vasu Exchange', 'Happy Rich Exchange']
                
                for i in range(15):
                    status = random.choice(statuses)
                    exchanger = random.choice(exchangers)
                    timestamp = datetime.now() - timedelta(hours=random.randint(1, 24))
                    
                    results.append({
                        "id": i + 1,
                        "exchanger_id": exchanger.lower().replace(' ', '_'),
                        "exchanger_name": exchanger,
                        "parsing_timestamp": timestamp.isoformat(),
                        "status": status,
                        "execution_time_ms": random.randint(500, 5000),
                        "source_url": f"https://{exchanger.lower().replace(' ', '')}.com/rates",
                        "parsed_pairs_count": random.randint(1, 5) if status == 'success' else 0,
                        "total_pairs_expected": 5,
                        "error_message": "Connection timeout" if status in ['failed', 'error'] else None,
                        "created_at": timestamp.isoformat()
                    })
                
                response = {
                    "success": True,
                    "data": {
                        "results": results,
                        "pagination": {
                            "total": len(results),
                            "limit": 100,
                            "offset": 0,
                            "has_more": False
                        }
                    }
                }
            
            elif path == '/api/v1/parsing/historical-rates/trends':
                # Тренды курсов для админки
                trends = []
                
                for i in range(30):
                    date = datetime.now() - timedelta(days=i)
                    base_buy_rate = 2.45
                    base_sell_rate = 2.55
                    volatility = random.uniform(-0.05, 0.05)
                    
                    trends.append({
                        "time_period": date.isoformat(),
                        "currency_pair": "RUB/THB",
                        "avg_buy_rate": round(base_buy_rate + volatility, 4),
                        "avg_sell_rate": round(base_sell_rate + volatility, 4),
                        "min_buy_rate": round(base_buy_rate + volatility - 0.02, 4),
                        "max_buy_rate": round(base_buy_rate + volatility + 0.02, 4),
                        "min_sell_rate": round(base_sell_rate + volatility - 0.02, 4),
                        "max_sell_rate": round(base_sell_rate + volatility + 0.02, 4),
                        "avg_spread_percentage": round(((base_sell_rate - base_buy_rate) / base_buy_rate) * 100, 2),
                        "data_points": random.randint(10, 50),
                        "unique_exchangers": random.randint(3, 5)
                    })
                
                response = {
                    "success": True,
                    "data": {
                        "trends": trends,
                        "currency_pair": "RUB/THB",
                        "interval": "daily"
                    }
                }
            
            elif path == '/api/v1/parsing/historical-rates':
                # Исторические курсы
                rates = []
                for i in range(10):
                    rates.append({
                        "id": i + 1,
                        "exchanger_id": f"test_exchanger_{i+1}",
                        "exchanger_name": f"Test Exchanger {i+1}",
                        "currency_pair": "RUB/THB",
                        "buy_rate": round(2.45 + random.uniform(-0.1, 0.1), 4),
                        "sell_rate": round(2.55 + random.uniform(-0.1, 0.1), 4),
                        "parsed_at": datetime.now().isoformat()
                    })
                
                response = {
                    "success": True,
                    "data": {
                        "rates": rates,
                        "total_count": len(rates),
                        "available_filters": {
                            "currency_pairs": [
                                {"currency_pair": "RUB/THB", "records_count": 100}
                            ],
                            "exchangers": [
                                {"exchanger_id": "test_exchanger_1", "exchanger_name": "Test Exchanger 1", "records_count": 50}
                            ]
                        }
                    }
                }
            
            else:
                response = {
                    "message": "Simple API Server",
                    "timestamp": datetime.now().isoformat(),
                    "path": path
                }
            
            # Отправляем ответ
            response_json = json.dumps(response, ensure_ascii=False, indent=2)
            self.wfile.write(response_json.encode('utf-8'))
            print(f"Response sent for {path}")
            
        except Exception as e:
            print(f"Error handling request: {e}")
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            error_response = {"error": str(e), "timestamp": datetime.now().isoformat()}
            self.wfile.write(json.dumps(error_response).encode('utf-8'))
    
    def do_OPTIONS(self):
        # Обработка preflight запросов
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        # Включаем подробное логирование
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

if __name__ == "__main__":
    print(f"Starting simple server on port {PORT}...")
    print(f"Health check: http://localhost:{PORT}/health")
    print(f"Parsing results: http://localhost:{PORT}/api/v1/parsing/results")
    print(f"Rate trends: http://localhost:{PORT}/api/v1/parsing/historical-rates/trends")
    print("Press Ctrl+C to stop")
    
    try:
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped")
    except Exception as e:
        print(f"Server error: {e}")
        sys.exit(1)
