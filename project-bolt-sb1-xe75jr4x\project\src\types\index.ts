export interface ExchangeRate {
  currency: string;
  buy: number;
  sell: number;
  change: number;
}

export interface Exchanger {
  id: number;
  name: string;
  serviceId?: string; // For grouping multiple offices under same service
  isMainOffice?: boolean;
  parentServiceName?: string;
  rating: number;
  reviewCount: number;
  address: string;
  district: string;
  city?: string;
  officeType?: 'main' | 'branch' | 'kiosk';
  phone: string;
  hours: string;
  website?: string;
  telegramBot?: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  rates: ExchangeRate[];
  logo?: string;
}

export interface ExchangeService {
  serviceId: string;
  serviceName: string;
  totalOffices: number;
  mainOffice: Exchanger;
  branches: Exchanger[];
  averageRating: number;
  totalReviews: number;
  website?: string;
  telegramBot?: string;
}
export interface Review {
  id: number;
  exchangerId: number;
  author: string;
  email?: string;
  rating: number;
  comment: string;
  date: string;
  moderated: boolean;
}

export interface District {
  id: string;
  name: string;
  coordinates: {
    lat: number;
    lng: number;
  };
}

export interface City {
  id: string;
  name: string;
  nameEn: string;
  country: string;
  timezone: string;
  currency: string;
  exchangerCount: number;
}

export interface AdminUser {
  id: number;
  username: string;
  role: 'admin' | 'moderator';
  lastLogin: string;
}