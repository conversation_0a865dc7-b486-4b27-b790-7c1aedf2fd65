# Backend Environment Variables for Development

# Application
APP_NAME="Thailand Exchange Platform"
APP_VERSION="1.0.0"
DEBUG=true

# Database (using SQLite for development)
DATABASE_URL="sqlite:///./thailand_exchange.db"
REDIS_URL="redis://localhost:6379"

# Google Sheets API (fallback mode - credentials not required for development)
GOOGLE_SHEETS_CREDENTIALS_FILE="./credentials/google-sheets-credentials.json"
GOOGLE_SHEETS_SPREADSHEET_ID="development-fallback-mode"
GOOGLE_SHEETS_WORKSHEET_NAME="Exchange Rates"

# Security
SECRET_KEY="development-secret-key-change-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
BACKEND_CORS_ORIGINS="http://localhost:3000,http://localhost:5173,http://localhost:5174"

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Cache Settings
CACHE_TTL_SECONDS=300
RATES_UPDATE_INTERVAL_MINUTES=15

# Monitoring
LOG_LEVEL="INFO"
