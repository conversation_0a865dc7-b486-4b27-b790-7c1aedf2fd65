#!/usr/bin/env python3
"""
Простейший HTTP сервер для тестирования
"""
import json
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
from datetime import datetime
import random

class HistoricalRatesHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        query_params = parse_qs(parsed_path.query)
        
        print(f"📡 Request: {self.command} {path}")
        
        # CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        
        if path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "message": "Simple Historical Rates API is running!",
                "timestamp": datetime.now().isoformat(),
                "status": "ok"
            }
            self.wfile.write(json.dumps(response).encode())
            
        elif path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat()
            }
            self.wfile.write(json.dumps(response).encode())
            
        elif path == '/api/v1/parsing/historical-rates':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            # Генерируем простые тестовые данные
            rates = []
            for i in range(5):
                rates.append({
                    "id": i + 1,
                    "exchanger_id": f"exchanger_{i+1}",
                    "exchanger_name": f"Test Exchanger {i+1}",
                    "currency_pair": "RUB/THB",
                    "buy_rate": round(2.45 + random.uniform(-0.1, 0.1), 4),
                    "sell_rate": round(2.55 + random.uniform(-0.1, 0.1), 4),
                    "parsed_at": datetime.now().isoformat()
                })
            
            response = {
                "success": True,
                "data": {
                    "rates": rates,
                    "total_count": len(rates),
                    "filters": {
                        "currency_pair": query_params.get('currency_pair', [None])[0],
                        "exchanger_id": query_params.get('exchanger_id', [None])[0],
                        "days_back": int(query_params.get('days_back', [7])[0])
                    },
                    "available_filters": {
                        "currency_pairs": [
                            {"currency_pair": "RUB/THB", "records_count": 100},
                            {"currency_pair": "THB/RUB", "records_count": 80}
                        ],
                        "exchangers": [
                            {"exchanger_id": "exchanger_1", "exchanger_name": "Test Exchanger 1", "records_count": 50},
                            {"exchanger_id": "exchanger_2", "exchanger_name": "Test Exchanger 2", "records_count": 45}
                        ]
                    }
                }
            }
            
            print(f"✅ Sending {len(rates)} rates")
            self.wfile.write(json.dumps(response).encode())
            
        else:
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {"error": "Not found", "path": path}
            self.wfile.write(json.dumps(response).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.end_headers()
    
    def log_message(self, format, *args):
        print(f"🌐 {datetime.now().strftime('%H:%M:%S')} - {format % args}")

if __name__ == "__main__":
    PORT = 8000
    
    print("🚀 Starting Simple HTTP Server for Historical Rates...")
    print(f"📍 Server will be available at: http://localhost:{PORT}")
    print(f"📊 Historical rates endpoint: http://localhost:{PORT}/api/v1/parsing/historical-rates")
    print(f"🔍 Health check: http://localhost:{PORT}/health")
    print("=" * 60)
    
    try:
        with socketserver.TCPServer(("", PORT), HistoricalRatesHandler) as httpd:
            print(f"✅ Server started successfully on port {PORT}")
            print("Press Ctrl+C to stop the server")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        import traceback
        traceback.print_exc()
