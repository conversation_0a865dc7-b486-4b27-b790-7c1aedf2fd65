<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Тест API админки - Обновлено</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .warning { border-left: 4px solid #ffc107; background: #fff3cd; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; max-height: 300px; }
        .endpoint { font-family: monospace; background: #e9ecef; padding: 2px 4px; border-radius: 3px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
        .metric { text-align: center; padding: 15px; background: #f8f9fa; border-radius: 4px; margin: 10px 0; }
        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .metric-label { font-size: 14px; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Тест API endpoints для админки</h1>
        
        <div class="card info">
            <h2>📋 Тестируемые endpoints</h2>
            <p>Эта страница тестирует API endpoints, которые используются в админке:</p>
            <ul>
                <li><strong>Результаты парсинга</strong> - <span class="endpoint">GET /api/v1/parsing/results</span></li>
                <li><strong>Тренды курсов</strong> - <span class="endpoint">GET /api/v1/parsing/historical-rates/trends</span></li>
                <li><strong>Исторические курсы</strong> - <span class="endpoint">GET /api/v1/parsing/historical-rates</span></li>
                <li><strong>Health check</strong> - <span class="endpoint">GET /health</span></li>
            </ul>
        </div>

        <div class="card">
            <h2>📊 Статус API</h2>
            <div class="grid">
                <div class="metric">
                    <div class="metric-value" id="server-status">Проверка...</div>
                    <div class="metric-label">Статус сервера</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="parsing-results-status">-</div>
                    <div class="metric-label">Результаты парсинга</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="trends-status">-</div>
                    <div class="metric-label">Тренды курсов</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="historical-status">-</div>
                    <div class="metric-label">Исторические курсы</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>🧪 Тесты API</h2>
            <div class="grid">
                <button onclick="testParsingResults()">Тест результатов парсинга</button>
                <button onclick="testRateTrends()">Тест трендов курсов</button>
                <button onclick="testHistoricalRates()">Тест исторических курсов</button>
                <button onclick="testHealthCheck()">Тест health check</button>
                <button onclick="runAllTests()">Запустить все тесты</button>
                <button onclick="clearResults()">Очистить результаты</button>
            </div>
        </div>

        <div id="test-results"></div>

        <div class="card">
            <h2>🔗 Быстрые ссылки</h2>
            <div class="grid">
                <button onclick="openAdminPanel()">Открыть админку</button>
                <button onclick="openHistoricalRates()">Открыть исторические курсы</button>
                <button onclick="openMainPage()">Открыть главную страницу</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="card ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function testEndpoint(url, description, statusElementId) {
            try {
                log(`🔄 Тестирование: ${description}<br>Endpoint: <span class="endpoint">${url}</span>`, 'info');
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const dataCount = data.data?.results?.length || data.data?.trends?.length || data.data?.rates?.length || 0;
                    log(`✅ ${description} - УСПЕХ<br>
                        📊 Получено записей: ${dataCount}<br>
                        📈 Статус: ${response.status}<br>
                        <details><summary>Данные</summary><pre>${JSON.stringify(data, null, 2)}</pre></details>`, 'success');
                    
                    if (statusElementId) {
                        document.getElementById(statusElementId).textContent = '✅ OK';
                        document.getElementById(statusElementId).style.color = '#28a745';
                    }
                } else {
                    log(`❌ ${description} - ОШИБКА<br>Status: ${response.status}<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                    
                    if (statusElementId) {
                        document.getElementById(statusElementId).textContent = '❌ ERROR';
                        document.getElementById(statusElementId).style.color = '#dc3545';
                    }
                }
            } catch (error) {
                log(`❌ ${description} - ИСКЛЮЧЕНИЕ<br>Error: ${error.message}`, 'error');
                
                if (statusElementId) {
                    document.getElementById(statusElementId).textContent = '❌ FAIL';
                    document.getElementById(statusElementId).style.color = '#dc3545';
                }
            }
        }

        async function testParsingResults() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/results?limit=10&hours_back=24`,
                'Результаты парсинга (админка)',
                'parsing-results-status'
            );
        }

        async function testRateTrends() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/historical-rates/trends?currency_pair=RUB/THB&days_back=30&interval=daily`,
                'Тренды курсов (админка)',
                'trends-status'
            );
        }

        async function testHistoricalRates() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/historical-rates?limit=10&days_back=7`,
                'Исторические курсы',
                'historical-status'
            );
        }

        async function testHealthCheck() {
            try {
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ Health Check - УСПЕХ<br>Status: ${data.status}<br>Time: ${data.timestamp}`, 'success');
                    document.getElementById('server-status').textContent = '✅ Работает';
                    document.getElementById('server-status').style.color = '#28a745';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ Health Check - ОШИБКА<br>Error: ${error.message}`, 'error');
                document.getElementById('server-status').textContent = '❌ Недоступен';
                document.getElementById('server-status').style.color = '#dc3545';
            }
        }

        async function runAllTests() {
            clearResults();
            log('🚀 Запуск комплексного тестирования API админки...', 'info');
            
            await testHealthCheck();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testParsingResults();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testRateTrends();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testHistoricalRates();
            
            log('🎉 Комплексное тестирование завершено!', 'success');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        function openAdminPanel() {
            window.open('http://localhost:5174/', '_blank');
            log('🔗 Админка открыта в новой вкладке', 'info');
        }

        function openHistoricalRates() {
            window.open('http://localhost:5174/historical-rates', '_blank');
            log('🔗 Исторические курсы открыты в новой вкладке', 'info');
        }

        function openMainPage() {
            window.open('http://localhost:5174/', '_blank');
            log('🔗 Главная страница открыта в новой вкладке', 'info');
        }

        // Автоматическое тестирование при загрузке
        window.addEventListener('load', async () => {
            log('🔧 Страница тестирования API админки загружена', 'info');
            
            // Автоматически запускаем health check
            setTimeout(async () => {
                await testHealthCheck();
            }, 1000);
        });

        // Периодическая проверка статуса сервера
        setInterval(async () => {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    document.getElementById('server-status').textContent = '✅ Работает';
                    document.getElementById('server-status').style.color = '#28a745';
                } else {
                    document.getElementById('server-status').textContent = '❌ Ошибка';
                    document.getElementById('server-status').style.color = '#dc3545';
                }
            } catch (error) {
                document.getElementById('server-status').textContent = '❌ Недоступен';
                document.getElementById('server-status').style.color = '#dc3545';
            }
        }, 10000); // Проверяем каждые 10 секунд
    </script>
</body>
</html>
