import React, { useState } from 'react';
import { User, Lock, Eye, EyeOff, AlertCircle, LogIn, ArrowLeft, CheckCircle } from 'lucide-react';
import { userRegistrationService } from '../../services/userRegistrationService';
import authService from '../../services/authService';

interface UserLoginFormProps {
  onSuccess: (user: any) => void;
  onSwitchToRegister?: () => void;
}

const UserLoginForm: React.FC<UserLoginFormProps> = ({ 
  onSuccess, 
  onSwitchToRegister 
}) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError('');
    if (successMessage) setSuccessMessage('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccessMessage('');
    setIsLoading(true);

    try {
      // Try to login using the auth service
      const response = await authService.login({
        email: formData.email,
        password: formData.password,
      });

      setSuccessMessage('Вход выполнен успешно!');

      // Call success callback with user data
      onSuccess(response.user);

    } catch (error: any) {
      console.error('Login error:', error);
      
      let errorMessage = 'Произошла ошибка при входе в систему';
      
      if (error.message) {
        if (error.message.includes('Invalid login credentials') || 
            error.message.includes('Неверный email или пароль')) {
          errorMessage = 'Неверный email или пароль';
        } else if (error.message.includes('Email not confirmed')) {
          errorMessage = 'Подтвердите email адрес для входа в систему';
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = 'Ошибка подключения к серверу. Проверьте интернет-соединение.';
        } else {
          errorMessage = error.message;
        }
      }
      
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-8">
        {/* Back to main site */}
        <div className="mb-6">
          <a
            href="/"
            className="text-gray-400 hover:text-gray-600 transition-colors flex items-center space-x-1 text-sm"
            title="Вернуться на главную страницу"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>На главную</span>
          </a>
        </div>

        {/* Header */}
        <div className="text-center mb-8">
          <div className="bg-gradient-to-br from-blue-500 to-indigo-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <LogIn className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Вход в систему</h1>
          <p className="text-gray-600">Войдите в свой аккаунт для доступа к дополнительным функциям</p>
        </div>

        {/* Success Message */}
        {successMessage && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center space-x-3">
            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
            <span className="text-green-700 text-sm">{successMessage}</span>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-3">
            <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        )}

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Email Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email адрес
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="<EMAIL>"
                required
                disabled={isLoading}
                autoComplete="email"
              />
            </div>
          </div>

          {/* Password Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Пароль
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="Введите пароль"
                required
                disabled={isLoading}
                autoComplete="current-password"
                minLength={6}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                disabled={isLoading}
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading || !formData.email || !formData.password}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed text-white py-3 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Вход...</span>
              </>
            ) : (
              <>
                <LogIn className="w-5 h-5" />
                <span>Войти</span>
              </>
            )}
          </button>
        </form>

        {/* Register Link */}
        {onSwitchToRegister && (
          <div className="mt-6 text-center">
            <p className="text-gray-600 text-sm">
              Нет аккаунта?{' '}
              <button
                onClick={onSwitchToRegister}
                className="text-blue-600 hover:text-blue-800 font-medium underline transition-colors"
                disabled={isLoading}
              >
                Зарегистрироваться
              </button>
            </p>
          </div>
        )}

        {/* Terms Notice */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <p className="text-xs text-gray-600 text-center">
            Входя в систему, вы соглашаетесь с{' '}
            <a href="/terms" className="text-blue-600 underline">Условиями использования</a>
            {' '}и{' '}
            <a href="/privacy" className="text-blue-600 underline">Политикой конфиденциальности</a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default UserLoginForm;
