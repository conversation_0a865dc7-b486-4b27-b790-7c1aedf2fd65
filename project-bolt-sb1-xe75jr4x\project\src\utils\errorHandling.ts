// Централизованная система обработки ошибок
import { config } from '../config/environment';

// Типы ошибок
export enum ErrorType {
  NETWORK = 'NETWORK',
  API = 'API',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN',
}

export interface AppError {
  type: ErrorType;
  message: string;
  code?: string | number;
  details?: any;
  timestamp: Date;
  userId?: string;
  url?: string;
  userAgent?: string;
}

// Класс для кастомных ошибок приложения
export class ApplicationError extends Error {
  public readonly type: ErrorType;
  public readonly code?: string | number;
  public readonly details?: any;
  public readonly timestamp: Date;

  constructor(
    type: ErrorType,
    message: string,
    code?: string | number,
    details?: any
  ) {
    super(message);
    this.name = 'ApplicationError';
    this.type = type;
    this.code = code;
    this.details = details;
    this.timestamp = new Date();
  }
}

// Обработчик ошибок
class ErrorHandler {
  private static instance: ErrorHandler;
  private errorQueue: AppError[] = [];
  private isOnline = navigator.onLine;

  private constructor() {
    this.setupGlobalErrorHandlers();
    this.setupNetworkListeners();
  }

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  // Настройка глобальных обработчиков ошибок
  private setupGlobalErrorHandlers(): void {
    // Обработка необработанных JavaScript ошибок
    window.addEventListener('error', (event) => {
      this.handleError(new ApplicationError(
        ErrorType.UNKNOWN,
        event.message,
        undefined,
        {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack,
        }
      ));
    });

    // Обработка необработанных Promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(new ApplicationError(
        ErrorType.UNKNOWN,
        event.reason?.message || 'Unhandled promise rejection',
        undefined,
        {
          reason: event.reason,
          stack: event.reason?.stack,
        }
      ));
    });
  }

  // Настройка слушателей сети
  private setupNetworkListeners(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushErrorQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  // Основной метод обработки ошибок
  public handleError(error: Error | ApplicationError, context?: any): void {
    const appError = this.normalizeError(error, context);
    
    // Логирование ошибки
    this.logError(appError);
    
    // Отправка ошибки в систему мониторинга
    if (config.ENABLE_ERROR_REPORTING) {
      this.reportError(appError);
    }
    
    // Показ уведомления пользователю (если необходимо)
    this.notifyUser(appError);
  }

  // Нормализация ошибки к единому формату
  private normalizeError(error: Error | ApplicationError, context?: any): AppError {
    if (error instanceof ApplicationError) {
      return {
        type: error.type,
        message: error.message,
        code: error.code,
        details: { ...error.details, ...context },
        timestamp: error.timestamp,
        userId: this.getCurrentUserId(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      };
    }

    // Определение типа ошибки по сообщению или контексту
    let errorType = ErrorType.UNKNOWN;
    
    if (error.message.includes('Network Error') || error.message.includes('fetch')) {
      errorType = ErrorType.NETWORK;
    } else if (error.message.includes('401') || error.message.includes('Unauthorized')) {
      errorType = ErrorType.AUTHENTICATION;
    } else if (error.message.includes('403') || error.message.includes('Forbidden')) {
      errorType = ErrorType.AUTHORIZATION;
    } else if (error.message.includes('404') || error.message.includes('Not Found')) {
      errorType = ErrorType.NOT_FOUND;
    } else if (error.message.includes('500') || error.message.includes('Server Error')) {
      errorType = ErrorType.SERVER;
    }

    return {
      type: errorType,
      message: error.message,
      details: { ...context, stack: error.stack },
      timestamp: new Date(),
      userId: this.getCurrentUserId(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    };
  }

  // Логирование ошибки
  private logError(error: AppError): void {
    const logLevel = this.getLogLevel(error.type);
    
    if (this.shouldLog(logLevel)) {
      const logMessage = `[${error.type}] ${error.message}`;
      const logData = {
        ...error,
        timestamp: error.timestamp.toISOString(),
      };

      switch (logLevel) {
        case 'error':
          console.error(logMessage, logData);
          break;
        case 'warn':
          console.warn(logMessage, logData);
          break;
        case 'info':
          console.info(logMessage, logData);
          break;
        default:
          console.log(logMessage, logData);
      }
    }
  }

  // Отправка ошибки в систему мониторинга
  private reportError(error: AppError): void {
    if (!this.isOnline) {
      this.errorQueue.push(error);
      return;
    }

    // Интеграция с Sentry (если настроен)
    if (config.SENTRY_DSN && window.Sentry) {
      window.Sentry.captureException(new Error(error.message), {
        tags: {
          errorType: error.type,
          userId: error.userId,
        },
        extra: error.details,
        level: this.getSentryLevel(error.type),
      });
    }

    // Отправка в собственную систему логирования
    this.sendToLoggingService(error).catch(console.error);
  }

  // Отправка в сервис логирования
  private async sendToLoggingService(error: AppError): Promise<void> {
    try {
      await fetch(`${config.API_BASE_URL}/logs/errors`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(error),
      });
    } catch (err) {
      // Если не удалось отправить, добавляем в очередь
      this.errorQueue.push(error);
    }
  }

  // Очистка очереди ошибок при восстановлении соединения
  private async flushErrorQueue(): Promise<void> {
    if (this.errorQueue.length === 0) return;

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    for (const error of errors) {
      try {
        await this.sendToLoggingService(error);
      } catch (err) {
        // Если снова не удалось, возвращаем в очередь
        this.errorQueue.push(error);
      }
    }
  }

  // Уведомление пользователя
  private notifyUser(error: AppError): void {
    // Показываем уведомление только для критических ошибок
    if (this.shouldNotifyUser(error.type)) {
      const userMessage = this.getUserFriendlyMessage(error);
      
      // Здесь можно интегрировать с системой уведомлений (toast, modal и т.д.)
      if (window.showNotification) {
        window.showNotification(userMessage, 'error');
      }
    }
  }

  // Вспомогательные методы
  private getCurrentUserId(): string | undefined {
    try {
      const token = localStorage.getItem('authToken');
      if (token) {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.userId;
      }
    } catch (err) {
      // Игнорируем ошибки парсинга токена
    }
    return undefined;
  }

  private getLogLevel(errorType: ErrorType): string {
    switch (errorType) {
      case ErrorType.SERVER:
      case ErrorType.UNKNOWN:
        return 'error';
      case ErrorType.NETWORK:
      case ErrorType.API:
        return 'warn';
      case ErrorType.VALIDATION:
        return 'info';
      default:
        return 'debug';
    }
  }

  private shouldLog(level: string): boolean {
    const levels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(config.LOG_LEVEL);
    const messageLevelIndex = levels.indexOf(level);
    return messageLevelIndex >= currentLevelIndex;
  }

  private getSentryLevel(errorType: ErrorType): string {
    switch (errorType) {
      case ErrorType.SERVER:
      case ErrorType.UNKNOWN:
        return 'error';
      case ErrorType.NETWORK:
      case ErrorType.API:
        return 'warning';
      default:
        return 'info';
    }
  }

  private shouldNotifyUser(errorType: ErrorType): boolean {
    return [
      ErrorType.NETWORK,
      ErrorType.SERVER,
      ErrorType.AUTHENTICATION,
    ].includes(errorType);
  }

  private getUserFriendlyMessage(error: AppError): string {
    switch (error.type) {
      case ErrorType.NETWORK:
        return 'Проблемы с подключением к интернету. Проверьте соединение и попробуйте снова.';
      case ErrorType.SERVER:
        return 'Временные проблемы на сервере. Мы уже работаем над их устранением.';
      case ErrorType.AUTHENTICATION:
        return 'Сессия истекла. Пожалуйста, войдите в систему заново.';
      case ErrorType.AUTHORIZATION:
        return 'У вас недостаточно прав для выполнения этого действия.';
      case ErrorType.NOT_FOUND:
        return 'Запрашиваемая информация не найдена.';
      case ErrorType.VALIDATION:
        return 'Проверьте правильность введенных данных.';
      default:
        return 'Произошла неожиданная ошибка. Попробуйте обновить страницу.';
    }
  }
}

// Экспорт singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Утилитарные функции для использования в компонентах
export const handleApiError = (error: any, context?: any) => {
  errorHandler.handleError(error, context);
};

export const createValidationError = (message: string, details?: any) => {
  return new ApplicationError(ErrorType.VALIDATION, message, undefined, details);
};

export const createNetworkError = (message: string, details?: any) => {
  return new ApplicationError(ErrorType.NETWORK, message, undefined, details);
};

// Декоратор для автоматической обработки ошибок в async функциях
export const withErrorHandling = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  context?: any
): T => {
  return (async (...args: any[]) => {
    try {
      return await fn(...args);
    } catch (error) {
      errorHandler.handleError(error as Error, context);
      throw error;
    }
  }) as T;
};