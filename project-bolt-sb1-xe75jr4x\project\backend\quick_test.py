#!/usr/bin/env python3
"""
Быстрый тест API сервера
"""

import urllib.request
import json
import sys

def test_endpoint(url, name):
    try:
        print(f"🔄 Тестирование {name}: {url}")
        
        # Создаем запрос с таймаутом
        req = urllib.request.Request(url)
        req.add_header('User-Agent', 'Python Test Client')
        
        with urllib.request.urlopen(req, timeout=5) as response:
            status = response.getcode()
            content_type = response.headers.get('content-type', 'unknown')
            data = response.read().decode('utf-8')
            
            print(f"   ✅ Status: {status}")
            print(f"   📄 Content-Type: {content_type}")
            print(f"   📊 Response length: {len(data)} bytes")
            
            if content_type.startswith('application/json'):
                try:
                    json_data = json.loads(data)
                    if 'success' in json_data:
                        print(f"   🎯 Success: {json_data['success']}")
                        if json_data.get('success') and 'data' in json_data:
                            data_keys = list(json_data['data'].keys())
                            print(f"   🔑 Data keys: {data_keys}")
                    else:
                        print(f"   📋 Response keys: {list(json_data.keys())}")
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON decode error: {e}")
                    print(f"   📝 Raw response: {data[:200]}...")
            
            return True
            
    except urllib.error.HTTPError as e:
        print(f"   ❌ HTTP Error {e.code}: {e.reason}")
        return False
    except urllib.error.URLError as e:
        print(f"   ❌ URL Error: {e.reason}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False

def main():
    print("🧪 Быстрый тест API сервера")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    endpoints = [
        ("/health", "Health Check"),
        ("/api/v1/parsing/results", "Parsing Results"),
        ("/api/v1/parsing/historical-rates/trends", "Rate Trends"),
        ("/api/v1/parsing/historical-rates", "Historical Rates")
    ]
    
    success_count = 0
    total_count = len(endpoints)
    
    for endpoint, name in endpoints:
        url = base_url + endpoint
        if test_endpoint(url, name):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"🎯 Результат: {success_count}/{total_count} endpoints работают")
    
    if success_count == total_count:
        print("✅ Все endpoints работают корректно!")
        return 0
    else:
        print("❌ Некоторые endpoints не работают")
        return 1

if __name__ == "__main__":
    sys.exit(main())
