<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication State Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
    </style>
</head>
<body>
    <h1>Authentication State Fix Test</h1>
    
    <div class="test-section info">
        <h2>Issue Fixed</h2>
        <p>The authentication state was not updating properly in the header after login. The following fixes have been implemented:</p>
        <ul>
            <li><strong>useAuth Hook:</strong> Now always initializes auth state regardless of Supabase configuration</li>
            <li><strong>Event Listeners:</strong> Added storage and custom event listeners for real-time auth state updates</li>
            <li><strong>Auth Service Events:</strong> Auth service now dispatches custom events when tokens are stored/cleared</li>
            <li><strong>Immediate Updates:</strong> Login/logout functions dispatch events for immediate UI updates</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button onclick="simulateLogin()">🔑 Simulate Login</button>
        <button onclick="simulateLogout()">🚪 Simulate Logout</button>
        <button onclick="checkAuthState()">🔍 Check Auth State</button>
        <button onclick="testEventDispatch()">📡 Test Event Dispatch</button>
        <button onclick="clearAllAuth()">🧹 Clear All Auth Data</button>
    </div>

    <div class="test-section">
        <h2>Current Status</h2>
        <div id="status-display">
            <p><span class="status-indicator status-offline"></span>Checking authentication state...</p>
        </div>
    </div>

    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="test-result ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateStatus() {
            const statusDiv = document.getElementById('status-display');
            const authData = localStorage.getItem('auth');
            
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    const isExpired = Date.now() >= auth.expiresAt;
                    
                    if (isExpired) {
                        statusDiv.innerHTML = `
                            <p><span class="status-indicator status-offline"></span>Authentication: EXPIRED</p>
                            <p>Token expired at: ${new Date(auth.expiresAt).toLocaleString()}</p>
                        `;
                    } else {
                        statusDiv.innerHTML = `
                            <p><span class="status-indicator status-online"></span>Authentication: ACTIVE</p>
                            <p>User: ${auth.user.email}</p>
                            <p>Role: ${auth.user.role}</p>
                            <p>Expires: ${new Date(auth.expiresAt).toLocaleString()}</p>
                        `;
                    }
                } catch (error) {
                    statusDiv.innerHTML = `
                        <p><span class="status-indicator status-offline"></span>Authentication: ERROR</p>
                        <p>Invalid auth data in localStorage</p>
                    `;
                }
            } else {
                statusDiv.innerHTML = `
                    <p><span class="status-indicator status-offline"></span>Authentication: NOT AUTHENTICATED</p>
                    <p>No auth data found</p>
                `;
            }
        }

        function simulateLogin() {
            log('Simulating user login...', 'info');
            
            const mockAuthResponse = {
                user: {
                    id: `user-${Date.now()}`,
                    email: '<EMAIL>',
                    role: 'user',
                    permissions: ['basic'],
                    isActive: true,
                    lastLogin: new Date().toISOString()
                },
                accessToken: `token-${Date.now()}`,
                refreshToken: `refresh-${Date.now()}`,
                expiresAt: Date.now() + (3600 * 1000) // 1 hour
            };
            
            // Store auth data (simulating authService.storeTokens)
            localStorage.setItem('auth', JSON.stringify(mockAuthResponse));
            
            // Dispatch the custom event (simulating authService behavior)
            window.dispatchEvent(new CustomEvent('authStateChanged'));
            
            updateStatus();
            log(`
                <h3>Login Simulation Complete</h3>
                <p>✅ Auth data stored in localStorage</p>
                <p>✅ Custom event 'authStateChanged' dispatched</p>
                <p>📧 User: ${mockAuthResponse.user.email}</p>
                <p>🔑 Token expires: ${new Date(mockAuthResponse.expiresAt).toLocaleString()}</p>
                <p><strong>The header should now show the user profile instead of login/register buttons.</strong></p>
            `, 'success');
        }

        function simulateLogout() {
            log('Simulating user logout...', 'info');
            
            // Clear auth data (simulating authService.clearTokens)
            localStorage.removeItem('auth');
            
            // Dispatch the custom event (simulating authService behavior)
            window.dispatchEvent(new CustomEvent('authStateChanged'));
            
            updateStatus();
            log(`
                <h3>Logout Simulation Complete</h3>
                <p>✅ Auth data removed from localStorage</p>
                <p>✅ Custom event 'authStateChanged' dispatched</p>
                <p><strong>The header should now show login/register buttons instead of user profile.</strong></p>
            `, 'success');
        }

        function checkAuthState() {
            log('Checking current authentication state...', 'info');
            
            const authData = localStorage.getItem('auth');
            
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    const isExpired = Date.now() >= auth.expiresAt;
                    const timeLeft = Math.max(0, auth.expiresAt - Date.now());
                    const minutesLeft = Math.floor(timeLeft / (1000 * 60));
                    
                    log(`
                        <h3>Authentication State: ${isExpired ? 'EXPIRED' : 'ACTIVE'}</h3>
                        <p>User: ${auth.user.email}</p>
                        <p>Role: ${auth.user.role}</p>
                        <p>Token Status: ${isExpired ? '❌ Expired' : '✅ Valid'}</p>
                        <p>Time Remaining: ${isExpired ? '0 minutes' : `${minutesLeft} minutes`}</p>
                        <p>Expected Header State: ${isExpired ? 'Login/Register buttons' : 'User profile'}</p>
                    `, isExpired ? 'warning' : 'success');
                } catch (error) {
                    log(`
                        <h3>Authentication State: ERROR</h3>
                        <p>❌ Invalid auth data in localStorage</p>
                        <p>Error: ${error.message}</p>
                        <p>Expected Header State: Login/Register buttons</p>
                    `, 'error');
                }
            } else {
                log(`
                    <h3>Authentication State: NOT AUTHENTICATED</h3>
                    <p>❌ No auth data found</p>
                    <p>Expected Header State: Login/Register buttons</p>
                `, 'info');
            }
            
            updateStatus();
        }

        function testEventDispatch() {
            log('Testing custom event dispatch...', 'info');
            
            let eventReceived = false;
            
            // Add temporary event listener
            const eventHandler = () => {
                eventReceived = true;
                log('✅ Custom event "authStateChanged" received!', 'success');
            };
            
            window.addEventListener('authStateChanged', eventHandler);
            
            // Dispatch the event
            window.dispatchEvent(new CustomEvent('authStateChanged'));
            
            // Check if event was received
            setTimeout(() => {
                window.removeEventListener('authStateChanged', eventHandler);
                
                if (eventReceived) {
                    log(`
                        <h3>Event Dispatch Test: PASSED</h3>
                        <p>✅ Custom event system is working correctly</p>
                        <p>✅ Auth context should receive updates when auth state changes</p>
                    `, 'success');
                } else {
                    log(`
                        <h3>Event Dispatch Test: FAILED</h3>
                        <p>❌ Custom event was not received</p>
                        <p>❌ Auth context may not update properly</p>
                    `, 'error');
                }
            }, 100);
        }

        function clearAllAuth() {
            log('Clearing all authentication data...', 'info');
            
            // Clear all possible auth-related data
            localStorage.removeItem('auth');
            localStorage.removeItem('auth_user');
            localStorage.removeItem('auth_token');
            localStorage.removeItem('supabase.auth.token');
            
            // Dispatch event
            window.dispatchEvent(new CustomEvent('authStateChanged'));
            
            updateStatus();
            log(`
                <h3>All Auth Data Cleared</h3>
                <p>✅ Removed all authentication data from localStorage</p>
                <p>✅ Dispatched auth state change event</p>
                <p><strong>Header should now show login/register buttons.</strong></p>
            `, 'info');
        }

        // Listen for auth state changes
        window.addEventListener('authStateChanged', () => {
            log('🔄 Auth state change event received - updating status...', 'info');
            updateStatus();
        });

        // Initialize
        window.addEventListener('load', () => {
            log('Authentication Fix Test Loaded', 'info');
            updateStatus();
            
            log(`
                <h3>Fix Summary</h3>
                <p>The authentication state issue has been resolved with the following changes:</p>
                <ul>
                    <li><strong>useAuth Hook:</strong> Always initializes auth state (no Supabase dependency)</li>
                    <li><strong>Event System:</strong> Custom events notify components of auth changes</li>
                    <li><strong>Storage Listeners:</strong> Detects auth changes across browser tabs</li>
                    <li><strong>Immediate Updates:</strong> Auth context updates immediately after login/logout</li>
                </ul>
                <p><strong>Test the fix:</strong> Use the buttons above to simulate login/logout and verify the header updates correctly.</p>
            `, 'success');
        });

        // Update status every 30 seconds
        setInterval(updateStatus, 30000);
    </script>
</body>
</html>
