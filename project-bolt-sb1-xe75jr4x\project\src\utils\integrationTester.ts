/**
 * Integration Tester for Exchange Rate Management System
 * Tests all critical fixes work together seamlessly
 */

import { adminAPI } from '../data/adminData';
import { networkSynchronizer } from './networkSynchronization';
import { processIntelligentRates } from './rateProcessor';
import DataPersistenceDebugger from './dataPersistenceDebugger';
import settingsAPI from '../data/settingsData';
import settingsIntegration from '../services/settingsIntegration';
import SettingsTestRunner from './settingsTestRunner';

export interface TestResult {
  testName: string;
  success: boolean;
  message: string;
  details?: any;
  duration: number;
}

export interface IntegrationTestSuite {
  results: TestResult[];
  overallSuccess: boolean;
  totalDuration: number;
  summary: {
    passed: number;
    failed: number;
    total: number;
  };
}

export class IntegrationTester {
  private results: TestResult[] = [];

  /**
   * Run all integration tests
   */
  public async runAllTests(): Promise<IntegrationTestSuite> {
    console.log('🧪 Starting Integration Test Suite...');
    const startTime = Date.now();
    this.results = [];

    // Test 1: Network Rate Synchronization
    await this.testNetworkSynchronization();

    // Test 2: Currency Pair Display and Calculations
    await this.testCurrencyPairCalculations();

    // Test 3: Manual Rate Changes Persistence
    await this.testManualRatePersistence();

    // Test 4: City Field Integration
    await this.testCityFieldIntegration();

    // Test 5: Data Integrity
    await this.testDataIntegrity();

    // Test 6: Settings Management
    await this.testSettingsManagement();

    // Test 7: Real-time Updates
    await this.testRealTimeUpdates();

    const totalDuration = Date.now() - startTime;
    const passed = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;

    const suite: IntegrationTestSuite = {
      results: this.results,
      overallSuccess: failed === 0,
      totalDuration,
      summary: {
        passed,
        failed,
        total: this.results.length
      }
    };

    console.log('🏁 Integration Test Suite Complete:', suite.summary);
    return suite;
  }

  /**
   * Test 1: Network Rate Synchronization
   */
  private async testNetworkSynchronization(): Promise<void> {
    const startTime = Date.now();
    try {
      console.log('Testing network rate synchronization...');

      // Create test exchangers with same network
      const testExchanger1 = {
        id: 'test-network-1',
        name: 'Test Exchange Network - Main',
        address: 'Test Address 1',
        district: 'patong',
        city: 'phuket',
        phone: '+66 89 111 1111',
        hours: '9:00-20:00',
        websiteUrl: 'https://testnetwork.com',
        networkId: 'test-network',
        status: 'active' as const,
        parsingEnabled: false,
        parsingConfig: { enabled: false, selectors: {}, updateInterval: 60, retryAttempts: 3, timeout: 30 },
        additionalOffices: []
      };

      const testExchanger2 = {
        id: 'test-network-2',
        name: 'Test Exchange Network - Branch',
        address: 'Test Address 2',
        district: 'karon',
        city: 'phuket',
        phone: '+66 89 222 2222',
        hours: '9:00-20:00',
        websiteUrl: 'https://testnetwork.com',
        networkId: 'test-network',
        status: 'active' as const,
        parsingEnabled: false,
        parsingConfig: { enabled: false, selectors: {}, updateInterval: 60, retryAttempts: 3, timeout: 30 },
        additionalOffices: []
      };

      // Create test exchangers
      await adminAPI.createExchanger(testExchanger1);
      await adminAPI.createExchanger(testExchanger2);

      // Update rates for first exchanger
      const testRates = [
        { currency: 'THB/RUB', buy: 2.45, sell: 2.52, change: 0 },
        { currency: 'USDT/THB', buy: 34.8, sell: 35.2, change: 0 }
      ];

      await adminAPI.updateExchanger('test-network-1', { rates: testRates });

      // Wait for synchronization
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if second exchanger got the same rates
      const exchangers = await adminAPI.getExchangers(1, 100, 'all');
      const exchanger2 = exchangers.exchangers.find(e => e.id === 'test-network-2');

      const success = exchanger2 && exchanger2.rates && exchanger2.rates.length > 0;

      this.results.push({
        testName: 'Network Rate Synchronization',
        success: !!success,
        message: success ? 'Network synchronization working correctly' : 'Network synchronization failed',
        details: { exchanger2Rates: exchanger2?.rates },
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Network Rate Synchronization',
        success: false,
        message: `Network synchronization test failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Test 2: Currency Pair Display and Calculations
   */
  private async testCurrencyPairCalculations(): Promise<void> {
    const startTime = Date.now();
    try {
      console.log('Testing currency pair calculations...');

      // Test inverse rate calculations
      const testRates = [
        { currency: 'THB/RUB', buy: 2.5, sell: 2.6, change: 0 }
      ];

      const processedRates = processIntelligentRates(testRates);
      
      // Should have both THB/RUB and RUB/THB
      const hasThbRub = processedRates.some(r => r.currency === 'THB/RUB');
      const hasRubThb = processedRates.some(r => r.currency === 'RUB/THB');
      
      // Check for division by zero protection
      const rubThbRate = processedRates.find(r => r.currency === 'RUB/THB');
      const hasValidRates = rubThbRate && 
                           isFinite(rubThbRate.buy) && 
                           isFinite(rubThbRate.sell) &&
                           rubThbRate.buy > 0 && 
                           rubThbRate.sell > 0;

      const success = hasThbRub && hasRubThb && hasValidRates;

      this.results.push({
        testName: 'Currency Pair Calculations',
        success,
        message: success ? 'Currency calculations working correctly' : 'Currency calculation issues detected',
        details: { 
          processedRates, 
          hasThbRub, 
          hasRubThb, 
          rubThbRate,
          hasValidRates 
        },
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Currency Pair Calculations',
        success: false,
        message: `Currency calculation test failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Test 3: Manual Rate Changes Persistence
   */
  private async testManualRatePersistence(): Promise<void> {
    const startTime = Date.now();
    try {
      console.log('Testing manual rate persistence...');

      // Create test exchanger
      const testExchanger = await adminAPI.createExchanger({
        name: 'Test Persistence Exchange',
        address: 'Test Address',
        district: 'patong',
        city: 'phuket',
        phone: '+66 89 333 3333',
        hours: '9:00-20:00',
        status: 'active',
        parsingEnabled: false,
        parsingConfig: { enabled: false, selectors: {}, updateInterval: 60, retryAttempts: 3, timeout: 30 },
        additionalOffices: []
      });

      // Update rates manually
      const manualRates = [
        { currency: 'THB/RUB', buy: 2.48, sell: 2.55, change: 0 },
        { currency: 'USDT/THB', buy: 34.9, sell: 35.1, change: 0 }
      ];

      await adminAPI.updateExchanger(testExchanger.id, { rates: manualRates });

      // Verify persistence by reloading
      const exchangers = await adminAPI.getExchangers(1, 100, 'all');
      const updatedExchanger = exchangers.exchangers.find(e => e.id === testExchanger.id);

      const success = updatedExchanger && 
                     updatedExchanger.rates && 
                     updatedExchanger.rates.length >= 2 &&
                     updatedExchanger.rates.some(r => r.currency === 'THB/RUB' && r.buy === 2.48);

      this.results.push({
        testName: 'Manual Rate Persistence',
        success: !!success,
        message: success ? 'Manual rate changes persist correctly' : 'Manual rate persistence failed',
        details: { 
          originalRates: manualRates, 
          persistedRates: updatedExchanger?.rates 
        },
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Manual Rate Persistence',
        success: false,
        message: `Manual rate persistence test failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Test 4: City Field Integration
   */
  private async testCityFieldIntegration(): Promise<void> {
    const startTime = Date.now();
    try {
      console.log('Testing city field integration...');

      // Create exchanger with city field
      const testExchanger = await adminAPI.createExchanger({
        name: 'Test City Exchange',
        address: 'Test Address',
        district: 'patong',
        city: 'bangkok',
        phone: '+66 89 444 4444',
        hours: '9:00-20:00',
        status: 'active',
        parsingEnabled: false,
        parsingConfig: { enabled: false, selectors: {}, updateInterval: 60, retryAttempts: 3, timeout: 30 },
        additionalOffices: []
      });

      // Verify city field is saved and retrieved
      const exchangers = await adminAPI.getExchangers(1, 100, 'all');
      const createdExchanger = exchangers.exchangers.find(e => e.id === testExchanger.id);

      const success = createdExchanger && createdExchanger.city === 'bangkok';

      this.results.push({
        testName: 'City Field Integration',
        success: !!success,
        message: success ? 'City field integration working correctly' : 'City field integration failed',
        details: { 
          expectedCity: 'bangkok', 
          actualCity: createdExchanger?.city 
        },
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'City Field Integration',
        success: false,
        message: `City field integration test failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Test 5: Data Integrity
   */
  private async testDataIntegrity(): Promise<void> {
    const startTime = Date.now();
    try {
      console.log('Testing data integrity...');

      const integrity = DataPersistenceDebugger.verifyDataIntegrity();
      
      this.results.push({
        testName: 'Data Integrity',
        success: integrity.isValid,
        message: integrity.isValid ? 'Data integrity verified' : `Data integrity issues: ${integrity.errors.join(', ')}`,
        details: integrity,
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Data Integrity',
        success: false,
        message: `Data integrity test failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Test 6: Settings Management
   */
  private async testSettingsManagement(): Promise<void> {
    const startTime = Date.now();
    try {
      console.log('Testing settings management...');

      // Run dedicated settings tests
      const settingsTestRunner = new SettingsTestRunner();
      const settingsResults = await settingsTestRunner.runAllTests();

      // Aggregate results
      const allPassed = settingsResults.every(r => r.success);
      const passedCount = settingsResults.filter(r => r.success).length;
      const totalCount = settingsResults.length;

      this.results.push({
        testName: 'Settings Management',
        success: allPassed,
        message: allPassed
          ? `All ${totalCount} settings tests passed`
          : `${passedCount}/${totalCount} settings tests passed`,
        details: {
          settingsTestResults: settingsResults,
          summary: {
            total: totalCount,
            passed: passedCount,
            failed: totalCount - passedCount
          }
        },
        duration: Date.now() - startTime
      });

      // Add individual settings test results to main results
      settingsResults.forEach(result => {
        this.results.push({
          testName: `Settings: ${result.testName}`,
          success: result.success,
          message: result.message,
          details: result.details,
          duration: result.duration
        });
      });

    } catch (error) {
      this.results.push({
        testName: 'Settings Management',
        success: false,
        message: `Settings management test failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Test 7: Real-time Updates
   */
  private async testRealTimeUpdates(): Promise<void> {
    const startTime = Date.now();
    try {
      console.log('Testing real-time updates...');

      let eventReceived = false;
      const eventHandler = () => { eventReceived = true; };

      // Listen for update events
      window.addEventListener('exchangerDataUpdated', eventHandler);

      // Trigger an update
      const testExchanger = await adminAPI.createExchanger({
        name: 'Test Real-time Exchange',
        address: 'Test Address',
        district: 'patong',
        city: 'phuket',
        phone: '+66 89 555 5555',
        hours: '9:00-20:00',
        status: 'active',
        parsingEnabled: false,
        parsingConfig: { enabled: false, selectors: {}, updateInterval: 60, retryAttempts: 3, timeout: 30 },
        additionalOffices: []
      });

      // Wait for event
      await new Promise(resolve => setTimeout(resolve, 500));

      window.removeEventListener('exchangerDataUpdated', eventHandler);

      this.results.push({
        testName: 'Real-time Updates',
        success: eventReceived,
        message: eventReceived ? 'Real-time updates working correctly' : 'Real-time update events not received',
        details: { eventReceived },
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Real-time Updates',
        success: false,
        message: `Real-time updates test failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Generate test report
   */
  public generateReport(suite: IntegrationTestSuite): string {
    let report = '# Exchange Rate Management System - Integration Test Report\n\n';
    report += `**Overall Result:** ${suite.overallSuccess ? '✅ PASSED' : '❌ FAILED'}\n`;
    report += `**Total Duration:** ${suite.totalDuration}ms\n`;
    report += `**Tests:** ${suite.summary.passed}/${suite.summary.total} passed\n\n`;

    report += '## Test Results\n\n';
    suite.results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      report += `### ${status} ${result.testName}\n`;
      report += `- **Status:** ${result.success ? 'PASSED' : 'FAILED'}\n`;
      report += `- **Duration:** ${result.duration}ms\n`;
      report += `- **Message:** ${result.message}\n`;
      if (result.details) {
        report += `- **Details:** \`${JSON.stringify(result.details, null, 2)}\`\n`;
      }
      report += '\n';
    });

    return report;
  }
}

// Global test runner for console access
(window as any).runIntegrationTests = async () => {
  const tester = new IntegrationTester();
  const results = await tester.runAllTests();
  console.log(tester.generateReport(results));
  return results;
};

export default IntegrationTester;
