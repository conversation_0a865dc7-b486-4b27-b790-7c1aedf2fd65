<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Тест компонентов админки</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: white; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        button:hover { background: #0056b3; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
        iframe { width: 100%; height: 600px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Тест компонентов админки</h1>
        
        <div class="card info">
            <h2>📋 Диагностика</h2>
            <p>Эта страница поможет понять, почему компоненты админки не делают запросы к API.</p>
        </div>

        <div class="card">
            <h2>🧪 Тесты</h2>
            <div class="grid">
                <button onclick="openAdminInFrame()">Загрузить админку в iframe</button>
                <button onclick="openParsingResults()">Открыть "Результаты парсинга"</button>
                <button onclick="openHistoricalTrends()">Открыть "Исторические тренды"</button>
                <button onclick="checkConsoleErrors()">Проверить ошибки консоли</button>
            </div>
        </div>

        <div class="card">
            <h2>🔗 Прямые ссылки для тестирования</h2>
            <div class="grid">
                <a href="http://localhost:5174/" target="_blank">Админка (новая вкладка)</a>
                <a href="http://localhost:8000/api/v1/parsing/results?limit=10" target="_blank">API: Результаты парсинга</a>
                <a href="http://localhost:8000/api/v1/parsing/historical-rates/trends?currency_pair=RUB/THB&days_back=7&interval=daily" target="_blank">API: Тренды курсов</a>
                <a href="http://localhost:5174/test-admin-api.html" target="_blank">Тест API</a>
            </div>
        </div>

        <div id="iframe-container" style="display: none;">
            <div class="card">
                <h2>🖼️ Админка в iframe</h2>
                <iframe id="admin-iframe" src=""></iframe>
            </div>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="card ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function openAdminInFrame() {
            const container = document.getElementById('iframe-container');
            const iframe = document.getElementById('admin-iframe');
            
            iframe.src = 'http://localhost:5174/';
            container.style.display = 'block';
            
            log('🖼️ Админка загружена в iframe. Проверьте, делает ли она запросы к API.', 'info');
            
            // Мониторим загрузку iframe
            iframe.onload = () => {
                log('✅ Iframe загружен успешно', 'success');
                
                // Пытаемся получить доступ к консоли iframe (может не работать из-за CORS)
                try {
                    const iframeWindow = iframe.contentWindow;
                    const iframeDocument = iframe.contentDocument;
                    
                    if (iframeDocument) {
                        log('📄 Доступ к документу iframe получен', 'success');
                        
                        // Проверяем наличие ошибок
                        setTimeout(() => {
                            const errorElements = iframeDocument.querySelectorAll('[class*="error"], [class*="Error"]');
                            if (errorElements.length > 0) {
                                log(`⚠️ Найдено ${errorElements.length} элементов с ошибками в iframe`, 'error');
                            } else {
                                log('✅ Видимых ошибок в iframe не найдено', 'success');
                            }
                        }, 3000);
                    }
                } catch (e) {
                    log('⚠️ Не удалось получить доступ к содержимому iframe (CORS)', 'info');
                }
            };
            
            iframe.onerror = () => {
                log('❌ Ошибка загрузки iframe', 'error');
            };
        }

        function openParsingResults() {
            // Открываем админку с хешем для перехода на нужную вкладку
            const url = 'http://localhost:5174/#parsing-results';
            window.open(url, '_blank');
            log('🔗 Открыта вкладка "Результаты парсинга" в новом окне', 'info');
        }

        function openHistoricalTrends() {
            // Открываем админку с хешем для перехода на нужную вкладку
            const url = 'http://localhost:5174/#historical-trends';
            window.open(url, '_blank');
            log('🔗 Открыта вкладка "Исторические тренды" в новом окне', 'info');
        }

        function checkConsoleErrors() {
            log('🔍 Проверка ошибок консоли...', 'info');
            
            // Перехватываем ошибки консоли
            const originalError = console.error;
            const originalWarn = console.warn;
            
            let errorCount = 0;
            let warnCount = 0;
            
            console.error = function(...args) {
                errorCount++;
                log(`🚨 Console Error #${errorCount}: ${args.join(' ')}`, 'error');
                originalError.apply(console, args);
            };
            
            console.warn = function(...args) {
                warnCount++;
                log(`⚠️ Console Warning #${warnCount}: ${args.join(' ')}`, 'error');
                originalWarn.apply(console, args);
            };
            
            log('✅ Перехват ошибок консоли активирован', 'success');
            
            // Восстанавливаем через 30 секунд
            setTimeout(() => {
                console.error = originalError;
                console.warn = originalWarn;
                log(`📊 Перехват завершен. Найдено ошибок: ${errorCount}, предупреждений: ${warnCount}`, 'info');
            }, 30000);
        }

        // Автоматическая диагностика при загрузке
        window.addEventListener('load', () => {
            log('🔧 Страница диагностики компонентов админки загружена', 'info');
            log('💡 Используйте кнопки выше для тестирования различных аспектов админки', 'info');
        });

        // Мониторинг сетевых запросов (если возможно)
        if ('fetch' in window) {
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                const url = args[0];
                if (typeof url === 'string' && url.includes('api/v1')) {
                    log(`🌐 Fetch запрос: ${url}`, 'info');
                }
                return originalFetch.apply(this, args);
            };
        }

        // Обработка ошибок
        window.addEventListener('unhandledrejection', event => {
            log(`🚨 Необработанная ошибка Promise: ${event.reason}`, 'error');
            console.error('Unhandled promise rejection:', event.reason);
        });

        window.addEventListener('error', event => {
            log(`🚨 JavaScript ошибка: ${event.message}`, 'error');
            console.error('JavaScript error:', event.error);
        });
    </script>
</body>
</html>
