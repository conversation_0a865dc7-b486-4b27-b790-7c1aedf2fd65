<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enhanced Rate Parsing API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 300px; }
        .endpoint { font-family: monospace; background: #e9ecef; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Enhanced Rate Parsing API Test</h1>
    
    <div class="test-section info">
        <h2>✅ Enhanced Rate Parsing Features</h2>
        <p>This page tests the new enhanced rate parsing functionality including:</p>
        <ul>
            <li><strong>Parsing Results API</strong> - Monitor parsing operations with detailed status tracking</li>
            <li><strong>Historical Rates API</strong> - Access historical exchange rate data with trends</li>
            <li><strong>Rate Trends API</strong> - Analyze rate trends with statistical aggregation</li>
            <li><strong>Session Management</strong> - Track current parsing session results</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>API Endpoint Tests</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;">
            <button onclick="testParsingResults()">Test Parsing Results</button>
            <button onclick="testParsingSummary()">Test Parsing Summary</button>
            <button onclick="testHistoricalRates()">Test Historical Rates</button>
            <button onclick="testRateTrends()">Test Rate Trends</button>
            <button onclick="testCurrentSession()">Test Current Session</button>
            <button onclick="testAllEndpoints()">Test All Endpoints</button>
        </div>
    </div>

    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="test-section ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function testEndpoint(url, description) {
            try {
                log(`🔄 Testing: ${description}<br>Endpoint: <span class="endpoint">${url}</span>`, 'info');
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    log(`✅ ${description} - SUCCESS<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    log(`❌ ${description} - FAILED<br>Status: ${response.status}<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                log(`❌ ${description} - ERROR<br>Error: ${error.message}`, 'error');
            }
        }

        async function testParsingResults() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/results?limit=10&hours_back=24`,
                'Parsing Results API'
            );
        }

        async function testParsingSummary() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/results/summary?days_back=7`,
                'Parsing Summary API'
            );
        }

        async function testHistoricalRates() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/historical-rates?currency_pair=RUB/THB&days_back=7&limit=50`,
                'Historical Rates API'
            );
        }

        async function testRateTrends() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/historical-rates/trends?currency_pair=RUB/THB&days_back=30&interval=daily`,
                'Rate Trends API'
            );
        }

        async function testCurrentSession() {
            await testEndpoint(
                `${API_BASE_URL}/parsing/current-session`,
                'Current Session API'
            );
        }

        async function testAllEndpoints() {
            log('🚀 Starting comprehensive API test...', 'info');
            
            await testParsingResults();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testParsingSummary();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testHistoricalRates();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testRateTrends();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testCurrentSession();
            
            log('🎉 Comprehensive API test completed!', 'success');
        }

        // Test database schema endpoints
        async function testDatabaseSchema() {
            log('🔍 Testing database schema...', 'info');
            
            // Test if tables exist by trying to query them
            const endpoints = [
                { url: `${API_BASE_URL}/parsing/results?limit=1`, table: 'parsing_results' },
                { url: `${API_BASE_URL}/parsing/historical-rates?limit=1`, table: 'historical_rates' }
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url);
                    const data = await response.json();
                    
                    if (response.ok) {
                        log(`✅ Table ${endpoint.table} exists and is accessible`, 'success');
                    } else {
                        log(`❌ Table ${endpoint.table} may not exist or is not accessible`, 'error');
                    }
                } catch (error) {
                    log(`❌ Error testing table ${endpoint.table}: ${error.message}`, 'error');
                }
            }
        }

        // Test admin panel integration
        function testAdminPanelIntegration() {
            log(`
                <h3>🎛️ Admin Panel Integration</h3>
                <p>The enhanced rate parsing functionality has been integrated into the admin panel with two new tabs:</p>
                <ul>
                    <li><strong>"Результаты парсинга"</strong> - Real-time parsing results monitoring</li>
                    <li><strong>"Исторические тренды"</strong> - Historical rate trend analysis</li>
                </ul>
                <p>To test the admin panel integration:</p>
                <ol>
                    <li>Navigate to the main application</li>
                    <li>Access the admin panel (requires admin authentication)</li>
                    <li>Check the new tabs in the admin navigation</li>
                    <li>Test the parsing results table and historical trends charts</li>
                </ol>
                <p><a href="/" target="_blank">Open Main Application</a></p>
            `, 'info');
        }

        // Initialize page
        window.addEventListener('load', () => {
            log('Enhanced Rate Parsing API Test Page Loaded', 'info');
            testDatabaseSchema();
            testAdminPanelIntegration();
        });
    </script>
</body>
</html>
