import React, { useEffect, useState } from 'react';
import { Shield, AlertCircle, LogIn } from 'lucide-react';
import authService from '../../services/authService';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'admin' | 'moderator' | 'user';
  requiredPermission?: string;
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredPermission,
  fallback
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    checkAuthentication();
  }, [requiredRole, requiredPermission]);

  const checkAuthentication = async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      
      if (!currentUser) {
        setIsAuthenticated(false);
        setHasAccess(false);
        return;
      }

      setIsAuthenticated(true);
      setUser(currentUser);

      // Check role requirement
      if (requiredRole && !authService.hasRole(requiredRole)) {
        setHasAccess(false);
        return;
      }

      // Check permission requirement
      if (requiredPermission && !authService.hasPermission(requiredPermission)) {
        setHasAccess(false);
        return;
      }

      setHasAccess(true);
    } catch (error) {
      console.error('Authentication check failed:', error);
      setIsAuthenticated(false);
      setHasAccess(false);
    }
  };

  // Loading state
  if (isAuthenticated === null || hasAccess === null) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white rounded-xl shadow-lg p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Проверка доступа...</p>
        </div>
      </div>
    );
  }

  // Not authenticated
  if (!isAuthenticated) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-lg max-w-md w-full p-8 text-center">
          <div className="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <LogIn className="w-8 h-8 text-red-600" />
          </div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">Требуется авторизация</h2>
          <p className="text-gray-600 mb-6">
            Для доступа к этой странице необходимо войти в систему
          </p>
          <div className="space-y-3">
            <a
              href="/admin"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors inline-block"
            >
              Войти в систему
            </a>
            <a
              href="/"
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 rounded-lg font-medium transition-colors inline-block"
            >
              На главную страницу
            </a>
          </div>
        </div>
      </div>
    );
  }

  // Authenticated but no access
  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-lg max-w-md w-full p-8 text-center">
          <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-orange-600" />
          </div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">Доступ запрещен</h2>
          <p className="text-gray-600 mb-4">
            У вас недостаточно прав для доступа к этой странице
          </p>
          
          {user && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-600">
                <strong>Пользователь:</strong> {user.email}
              </p>
              <p className="text-sm text-gray-600">
                <strong>Роль:</strong> {user.role}
              </p>
              {requiredRole && (
                <p className="text-sm text-red-600 mt-2">
                  <strong>Требуется роль:</strong> {requiredRole}
                </p>
              )}
              {requiredPermission && (
                <p className="text-sm text-red-600 mt-1">
                  <strong>Требуется разрешение:</strong> {requiredPermission}
                </p>
              )}
            </div>
          )}
          
          <div className="space-y-3">
            <button
              onClick={() => authService.logout()}
              className="w-full bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-medium transition-colors"
            >
              Выйти из системы
            </button>
            <a
              href="/"
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 rounded-lg font-medium transition-colors inline-block"
            >
              На главную страницу
            </a>
          </div>
        </div>
      </div>
    );
  }

  // Authenticated and has access
  return <>{children}</>;
};

export default ProtectedRoute;