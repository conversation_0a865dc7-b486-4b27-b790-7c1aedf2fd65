// API Service для работы с реальным бэкендом
import axios, { AxiosInstance, AxiosResponse } from 'axios';

// Конфигурация API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';
const API_TIMEOUT = 10000; // 10 секунд

// Создание экземпляра axios
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Интерцептор для добавления токена авторизации
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Интерцептор для обработки ответов
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    // Обработка ошибок авторизации
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    
    // Логирование ошибок в продакшне
    if (import.meta.env.PROD) {
      console.error('API Error:', {
        url: error.config?.url,
        method: error.config?.method,
        status: error.response?.status,
        message: error.message,
      });
    }
    
    return Promise.reject(error);
  }
);

// Типы для API ответов
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Основные API методы
export const api = {
  // Обменники
  exchangers: {
    getAll: (params?: {
      page?: number;
      limit?: number;
      district?: string;
      search?: string;
    }) => apiClient.get<ApiResponse<PaginatedResponse<Exchanger>>>('/exchangers', { params }),
    
    getById: (id: number) => apiClient.get<ApiResponse<Exchanger>>(`/exchangers/${id}`),
    
    getRates: (id: number) => apiClient.get<ApiResponse<ExchangeRate[]>>(`/exchangers/${id}/rates`),
    
    getTopRated: (limit = 10) => apiClient.get<ApiResponse<Exchanger[]>>(`/exchangers/top-rated?limit=${limit}`),
  },

  // Отзывы
  reviews: {
    getAll: (params?: {
      page?: number;
      limit?: number;
      exchangerId?: number;
      status?: 'pending' | 'approved' | 'rejected';
    }) => apiClient.get<ApiResponse<PaginatedResponse<Review>>>('/reviews', { params }),
    
    create: (review: Omit<Review, 'id' | 'date' | 'moderated'>) => 
      apiClient.post<ApiResponse<Review>>('/reviews', review),
    
    moderate: (id: number, action: 'approve' | 'reject') => 
      apiClient.patch<ApiResponse<Review>>(`/reviews/${id}/moderate`, { action }),
  },

  // Курсы валют
  rates: {
    getCurrent: () => apiClient.get<ApiResponse<ExchangeRate[]>>('/rates/current'),
    
    getHistory: (currency: string, days = 7) => 
      apiClient.get<ApiResponse<any[]>>(`/rates/history/${currency}?days=${days}`),
    
    updateFromSource: (source: 'google_sheets' | 'manual') => 
      apiClient.post<ApiResponse<any>>('/rates/update', { source }),
  },

  // Аутентификация
  auth: {
    login: (credentials: { email: string; password: string }) => 
      apiClient.post<ApiResponse<{ token: string; user: any }>>('/auth/login', credentials),
    
    register: (userData: { email: string; password: string; name: string }) => 
      apiClient.post<ApiResponse<{ token: string; user: any }>>('/auth/register', userData),
    
    logout: () => apiClient.post<ApiResponse<null>>('/auth/logout'),
    
    refreshToken: () => apiClient.post<ApiResponse<{ token: string }>>('/auth/refresh'),
    
    forgotPassword: (email: string) => 
      apiClient.post<ApiResponse<null>>('/auth/forgot-password', { email }),
  },

  // Административные функции
  admin: {
    getStats: () => apiClient.get<ApiResponse<DashboardStats>>('/admin/stats'),
    
    getUsers: (params?: {
      page?: number;
      limit?: number;
      role?: string;
      status?: string;
    }) => apiClient.get<ApiResponse<PaginatedResponse<User>>>('/admin/users', { params }),
    
    updateUser: (id: number, data: Partial<User>) => 
      apiClient.patch<ApiResponse<User>>(`/admin/users/${id}`, data),
    
    getSettings: () => apiClient.get<ApiResponse<SystemSettings[]>>('/admin/settings'),
    
    updateSetting: (key: string, value: string) => 
      apiClient.patch<ApiResponse<SystemSettings>>(`/admin/settings/${key}`, { value }),
    
    getActivityLogs: (params?: {
      page?: number;
      limit?: number;
      userId?: number;
      action?: string;
    }) => apiClient.get<ApiResponse<PaginatedResponse<ActivityLog>>>('/admin/activity', { params }),
  },

  // Геолокация и карты
  geo: {
    getDistricts: () => apiClient.get<ApiResponse<District[]>>('/geo/districts'),
    
    searchLocation: (query: string) => 
      apiClient.get<ApiResponse<any[]>>(`/geo/search?q=${encodeURIComponent(query)}`),
  },
};

// Утилиты для работы с API
export const apiUtils = {
  // Обработка ошибок API
  handleError: (error: any): string => {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    if (error.response?.data?.errors?.length > 0) {
      return error.response.data.errors.join(', ');
    }
    if (error.message) {
      return error.message;
    }
    return 'Произошла неизвестная ошибка';
  },

  // Проверка статуса ответа
  isSuccess: (response: any): boolean => {
    return response?.data?.success === true;
  },

  // Извлечение данных из ответа
  extractData: <T>(response: AxiosResponse<ApiResponse<T>>): T => {
    return response.data.data;
  },
};

export default api;