import React from 'react';
import { MapPin, Users, Clock, DollarSign, Globe, AlertCircle } from 'lucide-react';
import { cities, CityData, getAllRegions, getCitiesByRegion } from '../data/cities';

interface CitySelectorProps {
  selectedCity: string;
  onCityChange: (cityId: string) => void;
}

const CitySelector: React.FC<CitySelectorProps> = ({ selectedCity, onCityChange }) => {
  const currentCity = cities.find(city => city.id === selectedCity);
  const allRegions = getAllRegions();

  // Helper function to format exchanger count display
  const formatExchangerCount = (count: number) => {
    if (count === 0) {
      return {
        text: 'В процессе заполнения',
        isLoading: true,
        tooltip: 'Данные по обменникам в этом городе обновляются'
      };
    }
    return {
      text: `${count} обменников`,
      isLoading: false,
      tooltip: `Доступно ${count} обменных пунктов`
    };
  };

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100 mb-6 space-y-6">
      <div className="flex items-center space-x-3 mb-4">
        <div className="bg-gradient-to-br from-blue-500 to-indigo-500 p-2 rounded-lg">
          <Globe className="w-5 h-5 text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-800">Выберите город или курорт</h3>
          <p className="text-sm text-gray-600">Популярные туристические направления Таиланда</p>
        </div>
      </div>

      {/* All Cities by Region */}
      <div>
        <div className="flex items-center space-x-2 mb-3">
          <MapPin className="w-4 h-4 text-blue-500" />
          <h4 className="text-md font-semibold text-gray-800">Все направления по регионам</h4>
        </div>
        
        {allRegions.map((region) => {
          const regionCities = getCitiesByRegion(region);
          return (
            <div key={region} className="mb-4">
              <h5 className="text-sm font-semibold text-gray-700 mb-2 bg-gray-100 px-3 py-1 rounded-full inline-block">
                {region} ({regionCities.length} городов)
              </h5>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
                {regionCities.map((city) => {
                  const countInfo = formatExchangerCount(city.exchangerCount);
                  return (
                    <button
                      key={city.id}
                      onClick={() => onCityChange(city.id)}
                      className={`text-left p-3 rounded-lg border transition-all duration-200 ${
                        selectedCity === city.id
                          ? 'bg-blue-500 text-white border-blue-500 shadow-md'
                          : countInfo.isLoading
                            ? 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 hover:border-gray-300'
                            : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-50 hover:border-gray-300'
                      }`}
                    >
                      <div className="font-medium text-sm">{city.name}</div>
                      <div className={`text-xs mt-1 flex items-center ${
                        selectedCity === city.id
                          ? 'text-blue-100'
                          : countInfo.isLoading
                            ? 'text-amber-600'
                            : 'text-gray-500'
                      }`}>
                        {countInfo.isLoading && (
                          <AlertCircle className="w-3 h-3 mr-1" />
                        )}
                        {countInfo.text}
                      </div>
                      {city.isPopular && (
                        <div className="mt-1">
                          <span className={`text-xs px-1 py-0.5 rounded ${
                            selectedCity === city.id
                              ? 'bg-orange-400 text-white'
                              : 'bg-orange-100 text-orange-600'
                          }`}>
                            Популярный
                          </span>
                        </div>
                      )}
                    </button>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>

      {/* Compact Grid for Mobile */}
      <div className="md:hidden">
        <h4 className="text-md font-semibold text-gray-800 mb-3">Быстрый выбор</h4>
        <div className="grid grid-cols-2 gap-2">
          {cities.slice(0, 8).map((city) => {
            const countInfo = formatExchangerCount(city.exchangerCount);
            return (
              <button
                key={city.id}
                onClick={() => onCityChange(city.id)}
                className={`text-left p-2 rounded-lg border transition-all duration-200 ${
                  selectedCity === city.id
                    ? 'bg-blue-500 text-white border-blue-500'
                    : countInfo.isLoading
                      ? 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100'
                      : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className="font-medium text-sm">{city.name}</div>
                <div className={`text-xs flex items-center ${
                  selectedCity === city.id
                    ? 'text-blue-100'
                    : countInfo.isLoading
                      ? 'text-amber-600'
                      : 'text-gray-500'
                }`}>
                  {countInfo.isLoading && (
                    <AlertCircle className="w-3 h-3 mr-1" />
                  )}
                  {countInfo.text}
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Legacy Grid - Hidden on Mobile */}
      <div className="hidden">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {cities.slice(0, 6).map((city) => (
          <div
            key={city.id}
            onClick={() => onCityChange(city.id)}
            className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-300 transform hover:scale-105 ${
              selectedCity === city.id
                ? 'ring-2 ring-blue-500 shadow-lg'
                : 'hover:shadow-md'
            }`}
          >
            <div className="aspect-video bg-gradient-to-br from-blue-400 to-purple-500 relative">
              <div className="absolute inset-0 bg-black bg-opacity-20"></div>
              <div className="absolute top-3 left-3">
                <span className="bg-white bg-opacity-90 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                  {city.country}
                </span>
              </div>
              <div className="absolute bottom-3 left-3 right-3">
                <h4 className="text-white font-bold text-lg">{city.name}</h4>
                <p className="text-white text-sm opacity-90">{city.nameEn}</p>
              </div>
            </div>
            
            <div className="bg-white p-4">
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-blue-500" />
                  <span className={`text-gray-600 flex items-center ${
                    formatExchangerCount(city.exchangerCount).isLoading ? 'text-amber-600' : ''
                  }`}>
                    {formatExchangerCount(city.exchangerCount).isLoading && (
                      <AlertCircle className="w-3 h-3 mr-1" />
                    )}
                    {formatExchangerCount(city.exchangerCount).text}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4 text-green-500" />
                  <span className="text-gray-600">{city.currency}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-orange-500" />
                  <span className="text-gray-600">{city.timezone}</span>
                </div>
              </div>
              
              <p className="text-gray-500 text-xs mt-3 leading-relaxed">
                {city.description}
              </p>
              
              <div className="mt-3">
                <div className="text-xs text-gray-500 mb-1">Популярные районы:</div>
                <div className="flex flex-wrap gap-1">
                  {city.popularDistricts.slice(0, 2).map((district, index) => (
                    <span
                      key={index}
                      className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs"
                    >
                      {district}
                    </span>
                  ))}
                  {city.popularDistricts.length > 2 && (
                    <span className="text-gray-400 text-xs">
                      +{city.popularDistricts.length - 2}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
        </div>
      </div>

      {currentCity && (
        <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <MapPin className="w-4 h-4" />
            <span>Выбрано направление: <strong>{currentCity.name}</strong> ({currentCity.region})</span>
            <span className="text-gray-400">•</span>
            <span className={`flex items-center ${
              formatExchangerCount(currentCity.exchangerCount).isLoading ? 'text-amber-600' : ''
            }`}>
              {formatExchangerCount(currentCity.exchangerCount).isLoading && (
                <AlertCircle className="w-3 h-3 mr-1" />
              )}
              {formatExchangerCount(currentCity.exchangerCount).text} доступно
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default CitySelector;