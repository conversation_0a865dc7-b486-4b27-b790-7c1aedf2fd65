/**
 * Data Persistence Debugger
 * Utility to help debug and verify data persistence issues
 */

export interface PersistenceDebugInfo {
  localStorageSize: number;
  exchangerCount: number;
  activeExchangerCount: number;
  lastSaveTime: string | null;
  backupExists: boolean;
  sessionFlags: string[];
}

export class DataPersistenceDebugger {
  
  /**
   * Get comprehensive debug information about data persistence
   */
  public static getDebugInfo(): PersistenceDebugInfo {
    const exchangerData = localStorage.getItem('exchangerManagement');
    const backupData = localStorage.getItem('exchangerManagement_backup');
    
    let exchangerCount = 0;
    let activeExchangerCount = 0;
    let lastSaveTime: string | null = null;
    
    if (exchangerData) {
      try {
        const parsed = JSON.parse(exchangerData);
        if (Array.isArray(parsed)) {
          exchangerCount = parsed.length;
          activeExchangerCount = parsed.filter(e => e && e.status === 'active' && !e.isDeleted).length;
        }
      } catch (error) {
        console.error('Error parsing exchanger data:', error);
      }
    }
    
    if (backupData) {
      try {
        const backup = JSON.parse(backupData);
        lastSaveTime = backup.timestamp;
      } catch (error) {
        console.error('Error parsing backup data:', error);
      }
    }
    
    // Check session storage flags
    const sessionFlags: string[] = [];
    if (sessionStorage.getItem('refreshExchangers')) {
      sessionFlags.push('refreshExchangers');
    }
    if (sessionStorage.getItem('forceExchangerRefresh')) {
      sessionFlags.push('forceExchangerRefresh');
    }
    
    return {
      localStorageSize: exchangerData ? exchangerData.length : 0,
      exchangerCount,
      activeExchangerCount,
      lastSaveTime,
      backupExists: !!backupData,
      sessionFlags
    };
  }
  
  /**
   * Log detailed debug information
   */
  public static logDebugInfo(): void {
    const info = this.getDebugInfo();
    console.group('📊 Data Persistence Debug Info');
    console.log('Local Storage Size:', info.localStorageSize, 'bytes');
    console.log('Total Exchangers:', info.exchangerCount);
    console.log('Active Exchangers:', info.activeExchangerCount);
    console.log('Last Save Time:', info.lastSaveTime);
    console.log('Backup Exists:', info.backupExists);
    console.log('Session Flags:', info.sessionFlags);
    console.groupEnd();
  }
  
  /**
   * Verify data integrity
   */
  public static verifyDataIntegrity(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    try {
      const exchangerData = localStorage.getItem('exchangerManagement');
      if (!exchangerData) {
        errors.push('No exchanger data found in localStorage');
        return { isValid: false, errors };
      }
      
      const parsed = JSON.parse(exchangerData);
      if (!Array.isArray(parsed)) {
        errors.push('Exchanger data is not an array');
        return { isValid: false, errors };
      }
      
      // Check each exchanger
      parsed.forEach((exchanger, index) => {
        if (!exchanger) {
          errors.push(`Exchanger at index ${index} is null/undefined`);
          return;
        }
        
        if (!exchanger.id) {
          errors.push(`Exchanger at index ${index} has no ID`);
        }
        
        if (!exchanger.name) {
          errors.push(`Exchanger at index ${index} has no name`);
        }
        
        if (exchanger.rates && !Array.isArray(exchanger.rates)) {
          errors.push(`Exchanger ${exchanger.name} has invalid rates format`);
        }
        
        if (exchanger.rates && Array.isArray(exchanger.rates)) {
          exchanger.rates.forEach((rate: any, rateIndex: number) => {
            if (!rate.currency) {
              errors.push(`Rate ${rateIndex} for ${exchanger.name} has no currency`);
            }
            if (typeof rate.buy !== 'number' || typeof rate.sell !== 'number') {
              errors.push(`Rate ${rate.currency} for ${exchanger.name} has invalid buy/sell values`);
            }
          });
        }
      });
      
    } catch (error) {
      errors.push(`JSON parsing error: ${error}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Force data refresh from localStorage
   */
  public static forceDataRefresh(): void {
    console.log('🔄 Forcing data refresh...');
    sessionStorage.setItem('forceExchangerRefresh', 'true');
    window.dispatchEvent(new CustomEvent('exchangerDataUpdated', {
      detail: {
        source: 'debug_force_refresh',
        timestamp: Date.now()
      }
    }));
  }
  
  /**
   * Clear all persistence flags
   */
  public static clearPersistenceFlags(): void {
    sessionStorage.removeItem('refreshExchangers');
    sessionStorage.removeItem('forceExchangerRefresh');
    console.log('🧹 Cleared all persistence flags');
  }
  
  /**
   * Export data for debugging
   */
  public static exportDebugData(): any {
    const exchangerData = localStorage.getItem('exchangerManagement');
    const backupData = localStorage.getItem('exchangerManagement_backup');
    
    return {
      timestamp: new Date().toISOString(),
      exchangerData: exchangerData ? JSON.parse(exchangerData) : null,
      backupData: backupData ? JSON.parse(backupData) : null,
      debugInfo: this.getDebugInfo(),
      integrity: this.verifyDataIntegrity()
    };
  }
}

// Global debug functions for console access
(window as any).debugPersistence = {
  info: () => DataPersistenceDebugger.logDebugInfo(),
  verify: () => DataPersistenceDebugger.verifyDataIntegrity(),
  refresh: () => DataPersistenceDebugger.forceDataRefresh(),
  clear: () => DataPersistenceDebugger.clearPersistenceFlags(),
  export: () => DataPersistenceDebugger.exportDebugData()
};

export default DataPersistenceDebugger;
