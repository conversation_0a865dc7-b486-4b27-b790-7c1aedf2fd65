import React, { useState, useEffect } from 'react';
import { Star, MessageCircle, Calendar, User, Send, Filter, CheckCircle, AlertCircle, X, MapPin, Phone, Clock, Globe } from 'lucide-react';
import { allExchangers, getExchangeServices } from '../data/mockData';
import { useReviews, useCreateReview, useExchangers } from '../hooks/useExchangeData';
import { Review } from '../types';

interface ReviewsProps {
  searchQuery: string;
  selectedExchangerId?: number | null;
  onClearSelection?: () => void;
}

const Reviews: React.FC<ReviewsProps> = ({ searchQuery, selectedExchangerId, onClearSelection }) => {
  const [selectedExchanger, setSelectedExchanger] = useState<number | 'all'>(selectedExchangerId || 'all');
  const [sortBy, setSortBy] = useState<'date' | 'rating'>('date');
  const [showAddReview, setShowAddReview] = useState(false);
  const [exchangeServices, setExchangeServices] = useState(getExchangeServices());
  const [isLoadingExchangers, setIsLoadingExchangers] = useState(false);
  const [exchangerLoadError, setExchangerLoadError] = useState<string | null>(null);
  const [targetExchanger, setTargetExchanger] = useState<Exchanger | null>(null);
  const [newReview, setNewReview] = useState({
    exchangerId: allExchangers[0]?.id || 1,
    author: '',
    email: '',
    rating: 5,
    comment: ''
  });
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
  } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch reviews and exchangers from API
  const { 
    data: reviewsData, 
    isLoading: reviewsLoading,
    refetch: refetchReviews 
  } = useReviews({
    exchangerId: selectedExchanger === 'all' ? undefined : selectedExchanger as number
  });

  const { 
    data: exchangersData,
    isLoading: exchangersLoading 
  } = useExchangers({ limit: 100 });

  const createReviewMutation = useCreateReview();

  const reviews = reviewsData?.reviews || [];
  const availableExchangers = exchangersData?.exchangers || allExchangers;

  // Load and refresh exchange services data
  useEffect(() => {
    // loadExchangeServices(); // Now handled by React Query
    
    // Если передан ID конкретного обменника, устанавливаем его как выбранный
    if (selectedExchangerId) {
      const exchanger = allExchangers.find(e => e.id === selectedExchangerId);
      if (exchanger) {
        setSelectedExchanger(selectedExchangerId);
        setTargetExchanger(exchanger);
      } else {
        // Если обменник не найден, показываем общий список
        setSelectedExchanger('all');
        setTargetExchanger(null);
      }
    }
  }, []);
  
  // Обновляем выбранный обменник при изменении selectedExchangerId
  useEffect(() => {
    if (selectedExchangerId) {
      const exchanger = allExchangers.find(e => e.id === selectedExchangerId);
      if (exchanger) {
        setSelectedExchanger(selectedExchangerId);
        setTargetExchanger(exchanger);
      }
    }
  }, [selectedExchangerId]);

  const loadExchangeServices = async () => {
    setIsLoadingExchangers(true);
    setExchangerLoadError(null);
    
    try {
      // Simulate API call with delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Get fresh data
      const freshExchangers = allExchangers;
      const freshServices = getExchangeServices();
      
      if (freshExchangers.length === 0) {
        throw new Error('Не удалось загрузить список обменников');
      }
      
      // Sort exchangers alphabetically by name for better UX
      const sortedExchangers = [...freshExchangers].sort((a, b) => 
        a.name.localeCompare(b.name, 'ru')
      );
      
      setAvailableExchangers(sortedExchangers);
      setExchangeServices(freshServices);
      
      // Update default selection if current selection is invalid
      if (!freshExchangers.find(e => e.id === newReview.exchangerId)) {
        setNewReview(prev => ({
          ...prev,
          exchangerId: freshExchangers[0]?.id || 1
        }));
      }
      
    } catch (error) {
      console.error('Error loading exchange services:', error);
      setExchangerLoadError(error instanceof Error ? error.message : 'Ошибка загрузки данных');
    } finally {
      setIsLoadingExchangers(false);
    }
  };

  const filteredReviews = reviews
    .filter(review => {
      const exchanger = availableExchangers.find(e => e.id === review.exchangerId);
      const matchesSearch = exchanger?.name.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesExchanger = selectedExchanger === 'all' || review.exchangerId === selectedExchanger;
      return matchesSearch && matchesExchanger && review.moderated;
    })
    .sort((a, b) => {
      if (sortBy === 'date') {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      }
      return b.rating - a.rating;
    });

  const handleSubmitReview = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Ensure form doesn't cause page reload
    e.stopPropagation();
    
    // Расширенная валидация данных
    const validationResult = validateReviewData(newReview);
    if (!validationResult.isValid) {
      showNotification('error', validationResult.message);
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Use React Query mutation
      await createReviewMutation.mutateAsync(newReview);
      
      showNotification('success', 'Отзыв успешно отправлен на модерацию! Вы получите уведомление о статусе в течение 24 часов.');

      // Сброс формы
      setNewReview({
        exchangerId: 1,
        author: '',
        email: '',
        rating: 5,
        comment: ''
      });
      
      setShowAddReview(false);
      
      // Refetch reviews
      refetchReviews();
      
    } catch (error) {
      console.error('Review submission error:', error);
      showNotification('error', 'Произошла ошибка при отправке отзыва. Попробуйте еще раз.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Helper functions
  const isValidEmail = (email: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  
  const validateReviewData = (review: typeof newReview) => {
    if (!review.author.trim()) return { isValid: false, message: 'Пожалуйста, введите ваше имя' };
    if (review.author.length < 2) return { isValid: false, message: 'Имя должно содержать минимум 2 символа' };
    if (!review.email.trim() || !isValidEmail(review.email)) return { isValid: false, message: 'Пожалуйста, введите корректный email' };
    if (!review.comment.trim()) return { isValid: false, message: 'Пожалуйста, введите комментарий' };
    if (review.comment.length < 10) return { isValid: false, message: 'Комментарий должен содержать минимум 10 символов' };
    if (review.comment.length > 500) return { isValid: false, message: 'Комментарий не должен превышать 500 символов' };
    if (review.rating < 1 || review.rating > 5) return { isValid: false, message: 'Рейтинг должен быть от 1 до 5 звезд' };
    return { isValid: true, message: '' };
  };
  
  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  const renderStars = (rating: number, interactive = false, onRatingChange?: (rating: number) => void) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-5 h-5 cursor-pointer transition-colors ${
              i < rating 
                ? 'text-yellow-400 fill-current' 
                : 'text-gray-300 hover:text-yellow-300'
            }`}
            onClick={() => interactive && onRatingChange && onRatingChange(i + 1)}
          />
        ))}
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getExchangerName = (exchangerId: number) => {
    return availableExchangers.find(e => e.id === exchangerId)?.name || 'Неизвестный обменник';
  };
  
  const getExchangerReviewsCount = (exchangerId: number) => {
    return filteredReviews.filter(review => review.exchangerId === exchangerId).length;
  };
  
  const hasExchangerReviews = (exchangerId: number) => {
    return getExchangerReviewsCount(exchangerId) > 0;
  };

  const getExchangersByService = () => {
    const grouped = new Map();
    
    exchangeServices.forEach(service => {
      if (!grouped.has(service.serviceId)) {
        grouped.set(service.serviceId, {
          serviceName: service.serviceName,
          offices: []
        });
      }
      
      // Add main office
      if (availableExchangers.find(e => e.id === service.mainOffice.id)) {
        grouped.get(service.serviceId).offices.push({
          ...service.mainOffice,
          displayName: service.totalOffices > 1 ? 
            `${service.serviceName} - Главный офис` : 
            service.serviceName
        });
      }
      
      // Add branches
      service.branches.forEach(branch => {
        if (availableExchangers.find(e => e.id === branch.id)) {
          grouped.get(service.serviceId).offices.push({
            ...branch,
            displayName: `${service.serviceName} - ${branch.district}`
          });
        }
      });
    });
    
    return Array.from(grouped.values());
  };
  
  const handleBackToAllReviews = () => {
    setSelectedExchanger('all');
    setTargetExchanger(null);
    if (onClearSelection) {
      onClearSelection();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-100">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-3">
            <div className="bg-gradient-to-br from-green-500 to-emerald-500 p-3 rounded-lg">
              <MessageCircle className="w-6 h-6 text-white" />
            </div>
            <div>
              {targetExchanger ? (
                <>
                  <h2 className="text-2xl font-bold text-gray-800">Отзывы о {targetExchanger.name}</h2>
                  <p className="text-gray-600">
                    {hasExchangerReviews(targetExchanger.id) 
                      ? `${getExchangerReviewsCount(targetExchanger.id)} отзывов об этом обменнике`
                      : 'У этого обменника пока нет отзывов'
                    }
                  </p>
                  <button
                    onClick={handleBackToAllReviews}
                    className="mt-2 text-sm text-blue-600 hover:text-blue-800 underline flex items-center space-x-1"
                  >
                    <span>← Вернуться ко всем отзывам</span>
                  </button>
                </>
              ) : (
                <>
                  <h2 className="text-2xl font-bold text-gray-800">Отзывы клиентов</h2>
                  <p className="text-gray-600">Реальные отзывы о работе обменных пунктов</p>
                </>
              )}
            </div>
          </div>
          <button
            onClick={() => setShowAddReview(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2"
          >
            <Send className="w-4 h-4" />
            <span>Оставить отзыв</span>
          </button>
        </div>
      </div>

      {/* Exchanger Info Card (if specific exchanger selected) */}
      {targetExchanger && (
        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-gradient-to-br from-blue-500 to-purple-500 w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg">
                {targetExchanger.name.charAt(0)}
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-800">{targetExchanger.name}</h3>
                <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-4 h-4" />
                    <span>{targetExchanger.address}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Phone className="w-4 h-4" />
                    <span>{targetExchanger.phone}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{targetExchanger.hours}</span>
                  </div>
                </div>
                <div className="flex items-center mt-2">
                  {renderStars(targetExchanger.rating)}
                  <span className="ml-2 text-sm text-gray-500">
                    ({targetExchanger.reviewCount} отзывов)
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {targetExchanger.website && (
                <button
                  onClick={() => window.open(targetExchanger.website, '_blank')}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm flex items-center space-x-2"
                >
                  <Globe className="w-4 h-4" />
                  <span>Сайт</span>
                </button>
              )}
              {targetExchanger.telegramBot && (
                <button
                  onClick={() => window.open(targetExchanger.telegramBot, '_blank')}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex items-center space-x-2"
                >
                  <MessageCircle className="w-4 h-4" />
                  <span>Telegram</span>
                </button>
              )}
            </div>
          </div>
          
          {!hasExchangerReviews(targetExchanger.id) && (
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center space-x-2 text-yellow-800">
                <AlertCircle className="w-5 h-5" />
                <span className="font-medium">У этого обменника пока нет отзывов</span>
              </div>
              <p className="text-yellow-700 text-sm mt-1">
                Станьте первым, кто оставит отзыв об этом обменнике!
              </p>
            </div>
          )}
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
        <div className="flex items-center space-x-4 mb-4">
          <Filter className="w-5 h-5 text-gray-500" />
          <h3 className="text-lg font-semibold text-gray-800">Фильтры</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Обменник</label>
            <select
              value={selectedExchanger}
              onChange={(e) => setSelectedExchanger(e.target.value === 'all' ? 'all' : Number(e.target.value))}
              className={`w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors ${
                targetExchanger ? 'bg-blue-50 border-blue-300' : ''
              }`}
              disabled={isLoadingExchangers}
            >
              <option value="all">Все обменники</option>
              {availableExchangers.map(exchanger => (
                <option key={exchanger.id} value={exchanger.id}>{exchanger.name}</option>
              ))}
            </select>
            {isLoadingExchangers && (
              <div className="text-sm text-gray-500 mt-1">Загрузка обменников...</div>
            )}
            {exchangerLoadError && (
              <div className="text-sm text-red-600 mt-1 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {exchangerLoadError}
                <button 
                  onClick={loadExchangeServices}
                  className="ml-2 text-blue-600 hover:text-blue-800 underline"
                >
                  Повторить
                </button>
              </div>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Сортировка</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'date' | 'rating')}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
            >
              <option value="date">По дате</option>
              <option value="rating">По рейтингу</option>
            </select>
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {filteredReviews.map((review) => (
          <div
            key={review.id}
            className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow"
          >
            <div className="flex flex-col md:flex-row md:items-start md:justify-between space-y-4 md:space-y-0">
              <div className="flex-1">
                <div className="flex items-center space-x-4 mb-3">
                  <div className="bg-gradient-to-br from-blue-500 to-purple-500 w-10 h-10 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">{review.author}</h4>
                    <p className="text-sm text-gray-500">{getExchangerName(review.exchangerId)}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4 mb-3">
                  {renderStars(review.rating)}
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="w-4 h-4 mr-1" />
                    {formatDate(review.date)}
                  </div>
                </div>
                
                <p className="text-gray-700 leading-relaxed">{review.comment}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredReviews.length === 0 && (
        <div className="text-center py-12 bg-white rounded-xl shadow-lg border border-gray-100">
          <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <div className="text-gray-500 text-lg">
            {targetExchanger && !hasExchangerReviews(targetExchanger.id) 
              ? `У обменника "${targetExchanger.name}" пока нет отзывов`
              : 'Отзывы не найдены'
            }
          </div>
          <p className="text-gray-400 mt-2">
            {targetExchanger && !hasExchangerReviews(targetExchanger.id)
              ? 'Станьте первым, кто оставит отзыв!'
              : 'Попробуйте изменить параметры поиска или фильтры'
            }
          </p>
        </div>
      )}

      {/* Add Review Modal */}
      {showAddReview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 backdrop-blur-sm">
          <div className="bg-white rounded-xl shadow-2xl max-w-lg w-full p-6 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-bold text-gray-800 mb-4">Добавить отзыв</h3>
              <button
                onClick={() => setShowAddReview(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                disabled={isSubmitting}
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <form onSubmit={handleSubmitReview} className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Обменник
                  </label>
                </div>
                
                {exchangersLoading ? (
                  <div className="w-full p-3 border border-gray-300 rounded-lg bg-gray-50">
                    <div className="flex items-center text-gray-600">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                      <span className="text-sm">Загрузка обменников...</span>
                    </div>
                  </div>
                ) : exchangerLoadError ? (
                  <div className="w-full p-3 border border-red-300 rounded-lg bg-red-50">
                    <div className="flex items-center text-red-700">
                      <AlertCircle className="w-4 h-4 mr-2" />
                      <span className="text-sm">{exchangerLoadError}</span>
                    </div>
                  </div>
                ) : (
                  <select
                    value={newReview.exchangerId}
                    onChange={(e) => {
                      const exchangerId = Number(e.target.value);
                      setNewReview({...newReview, exchangerId});
                      // Если выбран тот же обменник, что и в targetExchanger, подсвечиваем
                    }}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                    required
                    disabled={exchangersLoading || availableExchangers.length === 0}
                  >
                    {availableExchangers.length === 0 ? (
                      <option value="">Обменники не найдены</option>
                    ) : (
                      <>
                        {/* Если есть targetExchanger, показываем его первым */}
                        {targetExchanger && (
                          <option 
                            value={targetExchanger.id} 
                            className="font-bold bg-blue-50"
                          >
                            ⭐ {targetExchanger.name} (выбранный обменник)
                          </option>
                        )}
                        {/* Group by services for better organization */}
                        {getExchangersByService().map(service => (
                          <optgroup key={service.serviceName} label={service.serviceName}>
                            {service.offices
                              .sort((a, b) => a.displayName.localeCompare(b.displayName, 'ru'))
                              .filter(office => !targetExchanger || office.id !== targetExchanger.id) // Исключаем уже показанный
                              .map(office => (
                                <option key={office.id} value={office.id}>
                                  {office.displayName}
                                  {office.rating && ` (★${office.rating.toFixed(1)})`}
                                </option>
                              ))
                            }
                          </optgroup>
                        ))}
                        
                        {/* Single exchangers not part of any service */}
                        {availableExchangers
                          .filter(exchanger => !exchanger.serviceId)
                          .filter(exchanger => !targetExchanger || exchanger.id !== targetExchanger.id) // Исключаем уже показанный
                          .sort((a, b) => a.name.localeCompare(b.name, 'ru'))
                          .map(exchanger => (
                            <option key={exchanger.id} value={exchanger.id}>
                              {exchanger.name}
                              {exchanger.rating && ` (★${exchanger.rating.toFixed(1)})`}
                            </option>
                          ))
                        }
                      </>
                    )}
                  </select>
                )}
                
                <div className="mt-2 text-xs text-gray-500">
                  Всего доступно: {availableExchangers.length} обменников из {exchangeServices.length} сетей
                  {targetExchanger && (
                    <div className="mt-1 text-blue-600 font-medium">
                      Выбран обменник: {targetExchanger.name}
                    </div>
                  )}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Ваше имя</label>
                <input
                  type="text"
                  value={newReview.author}
                  onChange={(e) => setNewReview({...newReview, author: e.target.value})}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                  placeholder="Введите ваше имя"
                  disabled={isSubmitting}
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                <input
                  type="email"
                  value={newReview.email}
                  onChange={(e) => setNewReview({...newReview, email: e.target.value})}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                  placeholder="Введите ваш email"
                  disabled={isSubmitting}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Email не будет опубликован</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Оценка</label>
                <div className="flex items-center space-x-2">
                  {renderStars(newReview.rating, !isSubmitting, (rating) => setNewReview({...newReview, rating}))}
                  <span className="text-sm text-gray-600">({newReview.rating} из 5)</span>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Комментарий</label>
                <textarea
                  value={newReview.comment}
                  onChange={(e) => setNewReview({...newReview, comment: e.target.value})}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 h-32 resize-none transition-colors"
                  placeholder="Расскажите о вашем опыте..."
                  disabled={isSubmitting}
                  minLength={10}
                  maxLength={500}
                  required
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Минимум 10 символов</span>
                  <span>{newReview.comment.length}/500</span>
                </div>
              </div>
              
              <div className="flex space-x-3">
                <button
                  type="submit"
                  disabled={isSubmitting || createReviewMutation.isPending}
                  className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                >
                  {(isSubmitting || createReviewMutation.isPending) ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Отправка...</span>
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4" />
                      <span>Отправить отзыв</span>
                    </>
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => setShowAddReview(false)}
                  disabled={isSubmitting || createReviewMutation.isPending}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 disabled:cursor-not-allowed text-gray-700 py-3 rounded-lg font-medium transition-colors"
                >
                  Отмена
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      
      {/* Notification Toast */}
      {notification && (
        <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-right duration-300">
          <div className={`rounded-lg shadow-lg p-4 max-w-sm ${
            notification.type === 'success' ? 'bg-green-500 text-white' :
            notification.type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
          }`}>
            <div className="flex items-center space-x-3">
              {notification.type === 'success' && <CheckCircle className="w-5 h-5" />}
              {notification.type === 'error' && <AlertCircle className="w-5 h-5" />}
              {notification.type === 'info' && <MessageCircle className="w-5 h-5" />}
              <div>
                <p className="font-medium text-sm">{notification.message}</p>
              </div>
              <button
                onClick={() => setNotification(null)}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Reviews;