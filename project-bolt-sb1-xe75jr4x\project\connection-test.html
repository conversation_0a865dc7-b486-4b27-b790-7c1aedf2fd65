<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Тест подключения к API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .warning { border-left: 4px solid #ffc107; background: #fff3cd; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .status { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status.ok { background: #28a745; color: white; }
        .status.error { background: #dc3545; color: white; }
        .status.loading { background: #ffc107; color: black; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Диагностика подключения к API</h1>
        
        <div class="card info">
            <h2>📋 Информация о тестировании</h2>
            <p>Эта страница поможет диагностировать проблемы с подключением к backend API.</p>
            <p><strong>Цель:</strong> Исправить ошибку "Failed to fetch" в разделе исторических курсов</p>
        </div>

        <div class="card">
            <h2>🧪 Тесты подключения</h2>
            <div class="grid">
                <button onclick="testBasicConnection()">Базовое подключение</button>
                <button onclick="testHealthEndpoint()">Health Check</button>
                <button onclick="testHistoricalRates()">Исторические курсы</button>
                <button onclick="testCORS()">Тест CORS</button>
                <button onclick="runAllTests()">Запустить все тесты</button>
                <button onclick="clearResults()">Очистить результаты</button>
            </div>
        </div>

        <div id="test-results"></div>

        <div class="card">
            <h2>🔍 Диагностическая информация</h2>
            <div id="diagnostic-info">
                <p><strong>Текущий URL:</strong> <span id="current-url">-</span></p>
                <p><strong>API Base URL:</strong> <span id="api-base-url">-</span></p>
                <p><strong>Время последнего теста:</strong> <span id="last-test-time">-</span></p>
                <p><strong>Статус браузера:</strong> <span id="browser-status">-</span></p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        let testCount = 0;

        // Обновляем диагностическую информацию
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('api-base-url').textContent = API_BASE_URL;
        document.getElementById('browser-status').textContent = navigator.userAgent.split(' ')[0];

        function logResult(test, status, message, details = null) {
            testCount++;
            const timestamp = new Date().toLocaleTimeString();
            document.getElementById('last-test-time').textContent = timestamp;
            
            const resultsDiv = document.getElementById('test-results');
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'warning';
            
            const resultHtml = `
                <div class="card ${statusClass}">
                    <h3>Тест #${testCount}: ${test}</h3>
                    <p><strong>[${timestamp}]</strong> ${message}</p>
                    ${details ? `<details><summary>Подробности</summary><pre>${details}</pre></details>` : ''}
                </div>
            `;
            
            resultsDiv.innerHTML += resultHtml;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            console.log(`${status.toUpperCase()}: ${test} - ${message}`);
            if (details) console.log('Details:', details);
        }

        async function testBasicConnection() {
            try {
                logResult('Базовое подключение', 'info', 'Проверка доступности сервера...');
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000);
                
                const response = await fetch(`${API_BASE_URL}/`, {
                    method: 'GET',
                    signal: controller.signal,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                clearTimeout(timeoutId);
                
                if (response.ok) {
                    const data = await response.json();
                    logResult('Базовое подключение', 'success', 
                        `✅ Сервер доступен! Статус: ${response.status}`, 
                        JSON.stringify(data, null, 2));
                } else {
                    logResult('Базовое подключение', 'error', 
                        `❌ Сервер вернул ошибку: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                if (error.name === 'AbortError') {
                    logResult('Базовое подключение', 'error', '❌ Таймаут подключения (5 секунд)');
                } else {
                    logResult('Базовое подключение', 'error', 
                        `❌ Ошибка подключения: ${error.message}`, 
                        `Тип ошибки: ${error.name}\nСтек: ${error.stack}`);
                }
            }
        }

        async function testHealthEndpoint() {
            try {
                logResult('Health Check', 'info', 'Проверка health endpoint...');
                
                const response = await fetch(`${API_BASE_URL}/health`, {
                    method: 'GET',
                    headers: { 'Accept': 'application/json' }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    logResult('Health Check', 'success', 
                        `✅ Health check успешен! Статус: ${data.status}`, 
                        JSON.stringify(data, null, 2));
                } else {
                    logResult('Health Check', 'error', 
                        `❌ Health check неудачен: ${response.status}`);
                }
            } catch (error) {
                logResult('Health Check', 'error', 
                    `❌ Ошибка health check: ${error.message}`);
            }
        }

        async function testHistoricalRates() {
            try {
                logResult('Исторические курсы', 'info', 'Тестирование API исторических курсов...');
                
                const url = `${API_BASE_URL}/api/v1/parsing/historical-rates?limit=3&days_back=7`;
                const response = await fetch(url, {
                    method: 'GET',
                    headers: { 'Accept': 'application/json' }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const ratesCount = data.data?.rates?.length || 0;
                    logResult('Исторические курсы', 'success', 
                        `✅ API работает! Получено ${ratesCount} записей`, 
                        JSON.stringify(data, null, 2));
                } else {
                    const errorText = await response.text();
                    logResult('Исторические курсы', 'error', 
                        `❌ API ошибка: ${response.status} ${response.statusText}`, 
                        errorText);
                }
            } catch (error) {
                logResult('Исторические курсы', 'error', 
                    `❌ Ошибка API: ${error.message}`);
            }
        }

        async function testCORS() {
            try {
                logResult('Тест CORS', 'info', 'Проверка CORS настроек...');
                
                // Отправляем preflight запрос
                const response = await fetch(`${API_BASE_URL}/api/v1/parsing/historical-rates`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'GET',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                if (response.ok) {
                    logResult('Тест CORS', 'success', 
                        '✅ CORS настроен правильно', 
                        JSON.stringify(corsHeaders, null, 2));
                } else {
                    logResult('Тест CORS', 'warning', 
                        `⚠️ CORS preflight вернул: ${response.status}`, 
                        JSON.stringify(corsHeaders, null, 2));
                }
            } catch (error) {
                logResult('Тест CORS', 'error', 
                    `❌ Ошибка CORS: ${error.message}`);
            }
        }

        async function runAllTests() {
            clearResults();
            logResult('Комплексное тестирование', 'info', '🚀 Запуск всех тестов...');
            
            await testBasicConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testHealthEndpoint();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testHistoricalRates();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testCORS();
            
            logResult('Комплексное тестирование', 'success', '🎉 Все тесты завершены!');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            testCount = 0;
        }

        // Автоматический запуск базового теста при загрузке
        window.addEventListener('load', () => {
            setTimeout(() => {
                logResult('Инициализация', 'info', '🔧 Страница диагностики загружена');
                testBasicConnection();
            }, 500);
        });

        // Обновление времени каждую секунду
        setInterval(() => {
            const now = new Date().toLocaleTimeString();
            document.getElementById('last-test-time').textContent = now;
        }, 1000);
    </script>
</body>
</html>
