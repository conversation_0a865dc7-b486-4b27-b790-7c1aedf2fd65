/**
 * Settings Test Runner
 * Comprehensive testing for settings functionality
 */

import { adminAPI } from '../data/adminData';
import settingsAPI from '../data/settingsData';
import settingsIntegration from '../services/settingsIntegration';

export interface SettingsTestResult {
  testName: string;
  success: boolean;
  message: string;
  details?: any;
  duration: number;
}

export class SettingsTestRunner {
  private results: SettingsTestResult[] = [];

  /**
   * Run all settings tests
   */
  public async runAllTests(): Promise<SettingsTestResult[]> {
    this.results = [];
    console.log('🧪 Starting comprehensive settings tests...');

    await this.testSettingsLoading();
    await this.testSettingsUpdate();
    await this.testSettingsValidation();
    await this.testSettingsPersistence();
    await this.testSettingsIntegration();
    await this.testSettingsCRUD();
    await this.testRealTimeUpdates();

    const summary = {
      total: this.results.length,
      passed: this.results.filter(r => r.success).length,
      failed: this.results.filter(r => !r.success).length
    };

    console.log('🧪 Settings tests completed:', summary);
    return this.results;
  }

  /**
   * Test 1: Settings Loading
   */
  private async testSettingsLoading(): Promise<void> {
    const startTime = Date.now();
    try {
      // Test loading all settings
      const allSettings = await adminAPI.getSettings();
      const hasSettings = allSettings && allSettings.length > 0;

      // Test loading by category
      const generalSettings = await adminAPI.getSettings('general');
      const ratesSettings = await adminAPI.getSettings('rates');
      const emailSettings = await adminAPI.getSettings('email');
      const securitySettings = await adminAPI.getSettings('security');

      const categoriesWork = generalSettings.length > 0 && ratesSettings.length > 0;

      this.results.push({
        testName: 'Settings Loading',
        success: hasSettings && categoriesWork,
        message: hasSettings && categoriesWork ? 'Settings loading works correctly' : 'Settings loading failed',
        details: {
          totalSettings: allSettings.length,
          generalCount: generalSettings.length,
          ratesCount: ratesSettings.length,
          emailCount: emailSettings.length,
          securityCount: securitySettings.length
        },
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Settings Loading',
        success: false,
        message: `Settings loading failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Test 2: Settings Update
   */
  private async testSettingsUpdate(): Promise<void> {
    const startTime = Date.now();
    try {
      const testKey = 'debug_mode';
      const originalValue = settingsAPI.getSettingValue(testKey) || 'false';
      const newValue = originalValue === 'true' ? 'false' : 'true';

      // Update setting
      await adminAPI.updateSetting(testKey, newValue);

      // Verify update
      const updatedValue = settingsAPI.getSettingValue(testKey);
      const updateWorked = updatedValue === newValue;

      // Restore original value
      await adminAPI.updateSetting(testKey, originalValue);

      this.results.push({
        testName: 'Settings Update',
        success: updateWorked,
        message: updateWorked ? 'Settings update works correctly' : 'Settings update failed',
        details: {
          testKey,
          originalValue,
          newValue,
          updatedValue,
          restored: settingsAPI.getSettingValue(testKey) === originalValue
        },
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Settings Update',
        success: false,
        message: `Settings update failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Test 3: Settings Validation
   */
  private async testSettingsValidation(): Promise<void> {
    const startTime = Date.now();
    try {
      let validationTests = 0;
      let validationPassed = 0;

      // Test invalid number
      try {
        await adminAPI.updateSetting('rates_update_interval', 'invalid');
        // Should not reach here
      } catch {
        validationPassed++;
      }
      validationTests++;

      // Test invalid JSON
      try {
        await adminAPI.updateSetting('mandatory_currency_pairs', '{invalid json}');
        // Should not reach here
      } catch {
        validationPassed++;
      }
      validationTests++;

      // Test invalid boolean
      try {
        await adminAPI.updateSetting('debug_mode', 'maybe');
        // Should not reach here
      } catch {
        validationPassed++;
      }
      validationTests++;

      const allValidationsPassed = validationTests === validationPassed;

      this.results.push({
        testName: 'Settings Validation',
        success: allValidationsPassed,
        message: allValidationsPassed ? 'Settings validation works correctly' : 'Settings validation issues detected',
        details: {
          totalTests: validationTests,
          passed: validationPassed
        },
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Settings Validation',
        success: false,
        message: `Settings validation test failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Test 4: Settings Persistence
   */
  private async testSettingsPersistence(): Promise<void> {
    const startTime = Date.now();
    try {
      const testKey = 'network_sync_cooldown';
      const originalValue = settingsAPI.getSettingValue(testKey) || '5';
      const testValue = '10';

      // Update setting
      await adminAPI.updateSetting(testKey, testValue);

      // Simulate page reload by reloading settings
      const reloadedSettings = await adminAPI.getSettings();
      const persistedSetting = reloadedSettings.find(s => s.key === testKey);
      const persistenceWorked = persistedSetting?.value === testValue;

      // Check localStorage
      const localStorageData = localStorage.getItem('systemSettings');
      const hasLocalStorage = localStorageData && localStorageData.includes(testKey);

      // Restore original value
      await adminAPI.updateSetting(testKey, originalValue);

      this.results.push({
        testName: 'Settings Persistence',
        success: persistenceWorked && hasLocalStorage,
        message: persistenceWorked && hasLocalStorage ? 'Settings persistence works correctly' : 'Settings persistence failed',
        details: {
          testKey,
          testValue,
          persistedValue: persistedSetting?.value,
          hasLocalStorage,
          originalRestored: settingsAPI.getSettingValue(testKey) === originalValue
        },
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Settings Persistence',
        success: false,
        message: `Settings persistence test failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Test 5: Settings Integration
   */
  private async testSettingsIntegration(): Promise<void> {
    const startTime = Date.now();
    try {
      // Test that settings changes are applied to the system
      const originalDebugMode = settingsAPI.getSettingValue('debug_mode') || 'false';
      
      // Enable debug mode
      await settingsIntegration.updateSetting('debug_mode', 'true');
      
      // Check if it was applied to window object
      const debugModeApplied = (window as any).debugMode === true;
      
      // Disable debug mode
      await settingsIntegration.updateSetting('debug_mode', 'false');
      
      // Check if it was disabled
      const debugModeDisabled = (window as any).debugMode === false;
      
      // Restore original value
      await settingsIntegration.updateSetting('debug_mode', originalDebugMode);

      const integrationWorked = debugModeApplied && debugModeDisabled;

      this.results.push({
        testName: 'Settings Integration',
        success: integrationWorked,
        message: integrationWorked ? 'Settings integration works correctly' : 'Settings integration failed',
        details: {
          debugModeApplied,
          debugModeDisabled,
          originalRestored: settingsAPI.getSettingValue('debug_mode') === originalDebugMode
        },
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Settings Integration',
        success: false,
        message: `Settings integration test failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Test 6: Settings CRUD Operations
   */
  private async testSettingsCRUD(): Promise<void> {
    const startTime = Date.now();
    try {
      const testSetting = {
        category: 'general',
        key: `test_crud_${Date.now()}`,
        value: 'test_value',
        type: 'string' as const,
        description: 'Test CRUD setting'
      };

      // Create
      const created = await adminAPI.createSetting(testSetting);
      const createWorked = created && created.key === testSetting.key;

      // Read
      const allSettings = await adminAPI.getSettings();
      const readWorked = allSettings.some(s => s.key === testSetting.key);

      // Update
      const updatedValue = 'updated_value';
      await adminAPI.updateSetting(testSetting.key, updatedValue);
      const updatedSetting = settingsAPI.getSettingValue(testSetting.key);
      const updateWorked = updatedSetting === updatedValue;

      // Delete
      await adminAPI.deleteSetting(testSetting.key);
      const settingsAfterDelete = await adminAPI.getSettings();
      const deleteWorked = !settingsAfterDelete.some(s => s.key === testSetting.key);

      const crudWorked = createWorked && readWorked && updateWorked && deleteWorked;

      this.results.push({
        testName: 'Settings CRUD Operations',
        success: crudWorked,
        message: crudWorked ? 'Settings CRUD operations work correctly' : 'Settings CRUD operations failed',
        details: {
          createWorked,
          readWorked,
          updateWorked,
          deleteWorked
        },
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Settings CRUD Operations',
        success: false,
        message: `Settings CRUD test failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Test 7: Real-time Updates
   */
  private async testRealTimeUpdates(): Promise<void> {
    const startTime = Date.now();
    try {
      let eventReceived = false;
      
      // Listen for settings update event
      const eventListener = () => {
        eventReceived = true;
      };
      
      window.addEventListener('settingsUpdated', eventListener);
      
      // Update a setting to trigger event
      await adminAPI.updateSetting('debug_mode', 'true');
      
      // Wait a bit for event propagation
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Clean up
      window.removeEventListener('settingsUpdated', eventListener);
      await adminAPI.updateSetting('debug_mode', 'false');

      this.results.push({
        testName: 'Real-time Updates',
        success: eventReceived,
        message: eventReceived ? 'Real-time updates work correctly' : 'Real-time updates failed',
        details: {
          eventReceived
        },
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Real-time Updates',
        success: false,
        message: `Real-time updates test failed: ${error}`,
        duration: Date.now() - startTime
      });
    }
  }

  /**
   * Generate a detailed test report
   */
  public generateReport(results: SettingsTestResult[]): string {
    const summary = {
      total: results.length,
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      totalDuration: results.reduce((sum, r) => sum + r.duration, 0)
    };

    let report = '\n=== SETTINGS FUNCTIONALITY TEST REPORT ===\n';
    report += `Total Tests: ${summary.total}\n`;
    report += `Passed: ${summary.passed}\n`;
    report += `Failed: ${summary.failed}\n`;
    report += `Total Duration: ${summary.totalDuration}ms\n`;
    report += `Success Rate: ${((summary.passed / summary.total) * 100).toFixed(1)}%\n\n`;

    results.forEach((result, index) => {
      report += `${index + 1}. ${result.testName}\n`;
      report += `   Status: ${result.success ? '✅ PASSED' : '❌ FAILED'}\n`;
      report += `   Message: ${result.message}\n`;
      report += `   Duration: ${result.duration}ms\n`;
      
      if (result.details) {
        report += `   Details: ${JSON.stringify(result.details, null, 4)}\n`;
      }
      report += '\n';
    });

    return report;
  }
}

// Global access for console testing
(window as any).runSettingsTests = async () => {
  const tester = new SettingsTestRunner();
  const results = await tester.runAllTests();
  console.log(tester.generateReport(results));
  return results;
};

export default SettingsTestRunner;
