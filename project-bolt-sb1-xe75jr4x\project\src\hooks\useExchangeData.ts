// React hooks for exchange data with React Query
import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { exchangeApi } from '../services/exchangeApi';
import { Exchanger, Review } from '../types';

// Query keys for React Query
export const queryKeys = {
  exchangers: (params?: any) => ['exchangers', params],
  topExchangers: (limit?: number) => ['exchangers', 'top', limit],
  exchanger: (id: number) => ['exchanger', id],
  exchangerRates: (id: number) => ['exchanger', id, 'rates'],
  reviews: (params?: any) => ['reviews', params],
  systemStatus: () => ['admin', 'status'],
  cacheInfo: () => ['admin', 'cache'],
};

// Exchangers hooks
export function useExchangers(params?: {
  district?: string;
  search?: string;
  limit?: number;
  forceRefresh?: boolean;
}) {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Listen for data updates from admin panel
  useEffect(() => {
    const handleDataUpdate = () => {
      setRefreshTrigger(prev => prev + 1);
    };

    window.addEventListener('exchangerDataUpdated', handleDataUpdate);
    
    // Check for session storage refresh signal
    const checkRefreshSignal = () => {
      const shouldRefresh = sessionStorage.getItem('refreshExchangers');
      if (shouldRefresh) {
        sessionStorage.removeItem('refreshExchangers');
        setRefreshTrigger(prev => prev + 1);
      }
    };

    // Check immediately and set up interval
    checkRefreshSignal();
    const interval = setInterval(checkRefreshSignal, 1000);

    return () => {
      window.removeEventListener('exchangerDataUpdated', handleDataUpdate);
      clearInterval(interval);
    };
  }, []);

  return useQuery({
    queryKey: [...queryKeys.exchangers(params), refreshTrigger],
    queryFn: async () => {
      try {
        return await exchangeApi.getExchangers({
          ...params,
          force_refresh: params?.forceRefresh || false
        });
      } catch (error) {
        // Fallback to mock data if API fails
        console.warn('API failed, using mock data');
        // Load fresh data from localStorage if available
        const savedData = localStorage.getItem('exchangerManagement');
        if (savedData) {
          try {
            const parsedData = JSON.parse(savedData);
            // Use the enhanced function to get properly structured data
            const { getActiveExchangersForMainPage } = await import('../data/adminData');
            let activeExchangers = getActiveExchangersForMainPage();
            
            // Log synchronization for debugging
            console.log('Syncing data from admin to main page:', {
              totalExchangers: activeExchangers.length,
              timestamp: new Date().toISOString(),
              refreshTrigger,
              hasRates: activeExchangers.filter(e => e.rates && e.rates.length > 0).length
            });
            
            // Apply filters
            if (params?.district) {
              activeExchangers = activeExchangers.filter((ex: any) => ex.district === params.district);
            }
            if (params?.search) {
              const searchLower = params.search.toLowerCase();
              activeExchangers = activeExchangers.filter((ex: any) => 
                ex.name.toLowerCase().includes(searchLower) ||
                ex.address.toLowerCase().includes(searchLower)
              );
            }
            
            return {
              exchangers: activeExchangers.slice(0, params?.limit || 50),
              total: activeExchangers.length,
              syncedAt: new Date().toISOString(),
              source: 'admin_data'
            };
          } catch (parseError) {
            console.error('Error parsing saved exchanger data:', parseError);
          }
        }
        
        // Final fallback to mock data
        const { allExchangers } = await import('../data/mockData');
        return {
          exchangers: allExchangers.slice(0, params?.limit || 50),
          total: allExchangers.length,
          source: 'mock_data'
        };
      }
    },
    staleTime: params?.forceRefresh ? 0 : 2 * 60 * 1000, // 2 minutes to reduce load
    refetchInterval: false, // Disable automatic refetch to prevent reload loops
    refetchOnWindowFocus: true, // Refetch when user returns to tab
    refetchOnMount: true, // Always refetch on component mount
    retry: (failureCount, error) => {
      // Limit retries to prevent infinite loops
      if (failureCount >= 3) return false;
      
      // Don't retry on certain errors
      if (error?.message?.includes('Network Error')) return false;
      
      return true;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

export function useTopExchangers(limit = 10) {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Listen for data updates from admin panel
  useEffect(() => {
    const handleDataUpdate = () => {
      setRefreshTrigger(prev => prev + 1);
    };

    window.addEventListener('exchangerDataUpdated', handleDataUpdate);
    
    return () => {
      window.removeEventListener('exchangerDataUpdated', handleDataUpdate);
    };
  }, []);

  return useQuery({
    queryKey: [...queryKeys.topExchangers(limit), refreshTrigger],
    queryFn: async () => {
      try {
        return await exchangeApi.getTopExchangers(limit);
      } catch (error) {
        console.warn('API failed, using fallback data');
        
        try {
          // Load fresh data from localStorage if available
          const { getActiveExchangersForMainPage } = await import('../data/adminData');
          const activeExchangers = getActiveExchangersForMainPage();
          
          if (activeExchangers && activeExchangers.length > 0) {
            // Enhanced sorting with proper null handling
            const sortedExchangers = activeExchangers
              .filter(exchanger => exchanger && typeof exchanger.rating === 'number')
              .sort((a, b) => {
                // Primary sort by rating
                const ratingDiff = (b.rating || 0) - (a.rating || 0);
                if (Math.abs(ratingDiff) > 0.1) return ratingDiff;
                
                // Secondary sort by review count
                const reviewDiff = (b.reviewCount || 0) - (a.reviewCount || 0);
                if (reviewDiff !== 0) return reviewDiff;
                
                // Tertiary sort by name for consistency
                return a.name.localeCompare(b.name, 'ru');
              })
              .slice(0, limit);
            
            console.log(`Loaded ${sortedExchangers.length} top exchangers from admin data:`, {
              total: activeExchangers.length,
              filtered: sortedExchangers.length,
              hasRatings: sortedExchangers.filter(e => e.rating >= 4.0).length,
              timestamp: new Date().toISOString()
            });
            
            return {
              exchangers: sortedExchangers,
              total: activeExchangers.length,
              source: 'admin_data',
              loadedAt: new Date().toISOString()
            };
          }
        } catch (importError) {
          console.error('Error importing admin data:', importError);
        }
        
        // Final fallback to mock data
        try {
          const { allExchangers } = await import('../data/mockData');
          const sortedMockExchangers = allExchangers
            .filter(exchanger => exchanger && typeof exchanger.rating === 'number')
            .sort((a, b) => {
              const ratingDiff = (b.rating || 0) - (a.rating || 0);
              if (Math.abs(ratingDiff) > 0.1) return ratingDiff;
              return (b.reviewCount || 0) - (a.reviewCount || 0);
            })
            .slice(0, limit);
          
          console.log(`Using mock data: ${sortedMockExchangers.length} exchangers`);
          
          return {
            exchangers: sortedMockExchangers,
            total: allExchangers.length,
            source: 'mock_data',
            loadedAt: new Date().toISOString()
          };
        } catch (mockError) {
          console.error('Error loading mock data:', mockError);
          throw new Error('Не удалось загрузить данные обменников');
        }
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes to reduce load
    gcTime: 10 * 60 * 1000, // 10 minutes cache time
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    retry: (failureCount, error) => {
      // Limit retries to prevent infinite loops
      if (failureCount >= 2) return false;
      
      // Don't retry on certain errors
      if (error?.message?.includes('Network Error')) return false;
      
      return true;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

export function useExchanger(id: number) {
  return useQuery({
    queryKey: queryKeys.exchanger(id),
    queryFn: () => exchangeApi.getExchangerById(id),
    enabled: !!id,
  });
}

export function useExchangerRates(id: number) {
  return useQuery({
    queryKey: queryKeys.exchangerRates(id),
    queryFn: () => exchangeApi.getExchangerRates(id),
    enabled: !!id,
    refetchInterval: 5 * 60 * 1000, // 5 minutes
  });
}

// Reviews hooks
export function useReviews(params?: {
  exchangerId?: number;
  page?: number;
  limit?: number;
}) {
  return useQuery({
    queryKey: queryKeys.reviews(params),
    queryFn: () => exchangeApi.getReviews(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useCreateReview() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: exchangeApi.createReview,
    onSuccess: () => {
      // Invalidate reviews queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['reviews'] });
    },
  });
}

// New hook for manual data refresh
export function useRefreshExchangers() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async () => {
      // Force refresh all exchanger data
      await queryClient.invalidateQueries({ queryKey: ['exchangers'] });
      await queryClient.refetchQueries({ queryKey: ['exchangers'] });
      return { success: true };
    },
  });
}
// Admin hooks
export function useSystemStatus() {
  return useQuery({
    queryKey: queryKeys.systemStatus(),
    queryFn: () => exchangeApi.getSystemStatus(),
    refetchInterval: 30 * 1000, // 30 seconds
  });
}

export function useCacheInfo() {
  return useQuery({
    queryKey: queryKeys.cacheInfo(),
    queryFn: () => exchangeApi.getCacheInfo(),
    refetchInterval: 10 * 1000, // 10 seconds
  });
}

export function useForceUpdateRates() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => exchangeApi.forceUpdateRates(),
    onSuccess: () => {
      // Invalidate all exchanger-related queries
      queryClient.invalidateQueries({ queryKey: ['exchangers'] });
      queryClient.invalidateQueries({ queryKey: ['admin'] });
    },
  });
}

export function useTestGoogleSheets() {
  return useMutation({
    mutationFn: () => exchangeApi.testGoogleSheetsConnection(),
  });
}

export function useClearCache() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => exchangeApi.clearCache(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['exchangers'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'cache'] });
    },
  });
}