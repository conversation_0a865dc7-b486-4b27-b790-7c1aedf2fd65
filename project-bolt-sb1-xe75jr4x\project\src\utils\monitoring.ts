// Система мониторинга и аналитики для продакшна
import { config } from '../config/environment';

// Типы для метрик
interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: Date;
  tags?: Record<string, string>;
}

interface UserEvent {
  event: string;
  properties: Record<string, any>;
  userId?: string;
  timestamp: Date;
}

interface PageView {
  page: string;
  title: string;
  userId?: string;
  timestamp: Date;
  referrer?: string;
  userAgent: string;
}

// Класс для мониторинга производительности
class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private observer?: PerformanceObserver;

  private constructor() {
    this.setupPerformanceObserver();
    this.setupPageLoadMetrics();
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Настройка Performance Observer
  private setupPerformanceObserver(): void {
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric({
            name: `performance.${entry.entryType}`,
            value: entry.duration || entry.startTime,
            timestamp: new Date(),
            tags: {
              name: entry.name,
              type: entry.entryType,
            },
          });
        }
      });

      try {
        this.observer.observe({ entryTypes: ['navigation', 'resource', 'measure', 'paint'] });
      } catch (error) {
        console.warn('Performance Observer not supported:', error);
      }
    }
  }

  // Метрики загрузки страницы
  private setupPageLoadMetrics(): void {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          // Время до первого байта
          this.recordMetric({
            name: 'performance.ttfb',
            value: navigation.responseStart - navigation.fetchStart,
            timestamp: new Date(),
          });

          // Время загрузки DOM
          this.recordMetric({
            name: 'performance.dom_load',
            value: navigation.domContentLoadedEventEnd - navigation.fetchStart,
            timestamp: new Date(),
          });

          // Полное время загрузки
          this.recordMetric({
            name: 'performance.page_load',
            value: navigation.loadEventEnd - navigation.fetchStart,
            timestamp: new Date(),
          });
        }

        // Largest Contentful Paint
        const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
        if (lcpEntries.length > 0) {
          this.recordMetric({
            name: 'performance.lcp',
            value: lcpEntries[lcpEntries.length - 1].startTime,
            timestamp: new Date(),
          });
        }

        // First Input Delay (если доступен)
        if ('PerformanceEventTiming' in window) {
          new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (entry.name === 'first-input') {
                this.recordMetric({
                  name: 'performance.fid',
                  value: entry.processingStart - entry.startTime,
                  timestamp: new Date(),
                });
              }
            }
          }).observe({ type: 'first-input', buffered: true });
        }
      }, 0);
    });
  }

  // Запись метрики
  public recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Отправка в аналитику (если включена)
    if (config.ENABLE_ANALYTICS) {
      this.sendMetricToAnalytics(metric);
    }

    // Ограничиваем размер массива метрик
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-500);
    }
  }

  // Отправка метрики в систему аналитики
  private sendMetricToAnalytics(metric: PerformanceMetric): void {
    // Google Analytics 4
    if (window.gtag && config.GOOGLE_ANALYTICS_ID) {
      window.gtag('event', metric.name, {
        custom_parameter_value: metric.value,
        custom_parameter_tags: JSON.stringify(metric.tags || {}),
      });
    }

    // Отправка в собственную систему аналитики
    this.sendToAnalyticsAPI(metric).catch(console.error);
  }

  // Отправка в API аналитики
  private async sendToAnalyticsAPI(metric: PerformanceMetric): Promise<void> {
    try {
      await fetch(`${config.API_BASE_URL}/analytics/metrics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(metric),
      });
    } catch (error) {
      // Игнорируем ошибки отправки аналитики
    }
  }

  // Получение метрик
  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  // Очистка метрик
  public clearMetrics(): void {
    this.metrics = [];
  }
}

// Класс для отслеживания пользовательских событий
class EventTracker {
  private static instance: EventTracker;
  private events: UserEvent[] = [];

  private constructor() {}

  public static getInstance(): EventTracker {
    if (!EventTracker.instance) {
      EventTracker.instance = new EventTracker();
    }
    return EventTracker.instance;
  }

  // Отслеживание события
  public track(event: string, properties: Record<string, any> = {}): void {
    const userEvent: UserEvent = {
      event,
      properties: {
        ...properties,
        url: window.location.href,
        referrer: document.referrer,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
      },
      userId: this.getCurrentUserId(),
      timestamp: new Date(),
    };

    this.events.push(userEvent);

    // Отправка в аналитику
    if (config.ENABLE_ANALYTICS) {
      this.sendEventToAnalytics(userEvent);
    }

    // Ограничиваем размер массива событий
    if (this.events.length > 500) {
      this.events = this.events.slice(-250);
    }
  }

  // Отслеживание просмотра страницы
  public trackPageView(page: string, title: string): void {
    const pageView: PageView = {
      page,
      title,
      userId: this.getCurrentUserId(),
      timestamp: new Date(),
      referrer: document.referrer,
      userAgent: navigator.userAgent,
    };

    // Google Analytics
    if (window.gtag && config.GOOGLE_ANALYTICS_ID) {
      window.gtag('config', config.GOOGLE_ANALYTICS_ID, {
        page_title: title,
        page_location: window.location.href,
      });
    }

    // Собственная аналитика
    this.sendPageViewToAnalytics(pageView);
  }

  // Отправка события в аналитику
  private sendEventToAnalytics(event: UserEvent): void {
    // Google Analytics
    if (window.gtag && config.GOOGLE_ANALYTICS_ID) {
      window.gtag('event', event.event, event.properties);
    }

    // Собственная система
    this.sendToAnalyticsAPI('/analytics/events', event).catch(console.error);
  }

  // Отправка просмотра страницы
  private sendPageViewToAnalytics(pageView: PageView): void {
    this.sendToAnalyticsAPI('/analytics/pageviews', pageView).catch(console.error);
  }

  // Отправка в API
  private async sendToAnalyticsAPI(endpoint: string, data: any): Promise<void> {
    try {
      await fetch(`${config.API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
    } catch (error) {
      // Игнорируем ошибки отправки аналитики
    }
  }

  // Получение текущего ID пользователя
  private getCurrentUserId(): string | undefined {
    try {
      const token = localStorage.getItem('authToken');
      if (token) {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.userId;
      }
    } catch (error) {
      // Игнорируем ошибки парсинга токена
    }
    return undefined;
  }

  // Получение событий
  public getEvents(): UserEvent[] {
    return [...this.events];
  }
}

// Инициализация мониторинга
export const initializeMonitoring = (): void => {
  // Инициализация Google Analytics
  if (config.GOOGLE_ANALYTICS_ID && config.ENABLE_ANALYTICS) {
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${config.GOOGLE_ANALYTICS_ID}`;
    document.head.appendChild(script);

    window.dataLayer = window.dataLayer || [];
    window.gtag = function() {
      window.dataLayer.push(arguments);
    };
    window.gtag('js', new Date());
    window.gtag('config', config.GOOGLE_ANALYTICS_ID, {
      send_page_view: false, // Отправляем вручную
    });
  }

  // Инициализация Sentry
  if (config.SENTRY_DSN && config.ENABLE_ERROR_REPORTING) {
    import('@sentry/browser').then((Sentry) => {
      Sentry.init({
        dsn: config.SENTRY_DSN,
        environment: config.APP_ENV,
        release: config.APP_VERSION,
        integrations: [
          new Sentry.BrowserTracing(),
        ],
        tracesSampleRate: config.APP_ENV === 'production' ? 0.1 : 1.0,
      });
      
      window.Sentry = Sentry;
    });
  }

  // Инициализация мониторов
  PerformanceMonitor.getInstance();
  EventTracker.getInstance();
};

// Экспорт singleton instances
export const performanceMonitor = PerformanceMonitor.getInstance();
export const eventTracker = EventTracker.getInstance();

// Утилитарные функции
export const trackEvent = (event: string, properties?: Record<string, any>) => {
  eventTracker.track(event, properties);
};

export const trackPageView = (page: string, title: string) => {
  eventTracker.trackPageView(page, title);
};

export const recordPerformanceMetric = (name: string, value: number, tags?: Record<string, string>) => {
  performanceMonitor.recordMetric({
    name,
    value,
    timestamp: new Date(),
    tags,
  });
};

// Декоратор для измерения времени выполнения функций
export const measurePerformance = <T extends (...args: any[]) => any>(
  fn: T,
  metricName: string
): T => {
  return ((...args: any[]) => {
    const startTime = performance.now();
    const result = fn(...args);
    
    if (result instanceof Promise) {
      return result.finally(() => {
        const endTime = performance.now();
        recordPerformanceMetric(metricName, endTime - startTime);
      });
    } else {
      const endTime = performance.now();
      recordPerformanceMetric(metricName, endTime - startTime);
      return result;
    }
  }) as T;
};