"""
Сервис автоматического парсинга курсов валют с сайтов обменников
"""
import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import aiohttp
from bs4 import BeautifulSoup
import re
from decimal import Decimal, InvalidOperation
import json
from dataclasses import dataclass
# urlparse imported locally where needed

from services.database_service import database_service

logger = logging.getLogger(__name__)


@dataclass
class ParsingResult:
    """Data class for parsing operation results"""
    exchanger_id: str
    exchanger_name: str
    status: str  # 'success', 'failed', 'error', 'partial'
    execution_time_ms: int
    source_url: str
    parsed_pairs_count: int
    total_pairs_expected: int
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    raw_data: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    parsing_timestamp: Optional[datetime] = None


@dataclass
class HistoricalRate:
    """Data class for historical rate data"""
    exchanger_id: str
    exchanger_name: str
    currency_pair: str
    buy_rate: Decimal
    sell_rate: Decimal
    parsing_result_id: Optional[int] = None
    source_url: Optional[str] = None
    data_quality_score: int = 100
    validation_status: str = 'valid'  # 'valid', 'warning', 'invalid'
    validation_notes: Optional[str] = None
    parsed_at: Optional[datetime] = None


class RateParserService:
    """Сервис для парсинга курсов валют с сайтов обменников"""

    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.parsing_active = False
        self._logged_events = set()  # Track logged events to prevent duplicates
        self._network_sync_cache = {}  # Cache for network synchronization

        # Validation ranges for currency pairs
        self.RATE_VALIDATION_RANGES = {
            'THB/RUB': {'min': 0.25, 'max': 0.50, 'decimals': 3},
            'RUB/THB': {'min': 2.0, 'max': 4.0, 'decimals': 2},
            'USDT/THB': {'min': 30.0, 'max': 40.0, 'decimals': 2}
        }

        # Mandatory currency pairs that must be present
        self.MANDATORY_PAIRS = ['THB/RUB', 'RUB/THB', 'USDT/THB']

        # Storage for current parsing session results
        self.current_parsing_results: List[ParsingResult] = []
        self.current_historical_rates: List[HistoricalRate] = []

    async def initialize(self):
        """Инициализация HTTP сессии"""
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(
            timeout=timeout,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        )
        logger.info("Rate parser service initialized")
    
    async def close(self):
        """Закрытие HTTP сессии"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def parse_exchanger_rates(self, exchanger_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Парсинг курсов для конкретного обменника

        Args:
            exchanger_id: ID обменника
            config: Конфигурация парсинга с селекторами и настройками

        Returns:
            Результат парсинга с курсами и статистикой
        """
        start_time = time.time()
        parsing_timestamp = datetime.now()
        exchanger_name = config.get('name', exchanger_id)
        website_url = config.get('website_url', '')

        result = {
            'success': False,
            'rates': {},
            'errors': [],
            'execution_time_ms': 0,
            'parsed_count': 0,
            'parsing_result_id': None
        }
        
        try:
            if not self.session:
                await self.initialize()
            
            website_url = config.get('website_url')
            selectors = config.get('selectors', {})
            
            if not website_url:
                raise ValueError("URL сайта не указан")
            
            # Загрузка страницы
            async with self.session.get(website_url) as response:
                if response.status != 200:
                    raise aiohttp.ClientError(f"HTTP {response.status}: {response.reason}")
                
                html_content = await response.text()
            
            # Парсинг HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Парсинг курсов по селекторам
            rates = {}
            
            # THB/RUB курсы
            if selectors.get('thbRubBuy'):
                buy_rate = self._extract_rate(soup, selectors['thbRubBuy'])
                if buy_rate:
                    rates['THB/RUB'] = rates.get('THB/RUB', {})
                    rates['THB/RUB']['buy'] = buy_rate
            
            if selectors.get('thbRubSell'):
                sell_rate = self._extract_rate(soup, selectors['thbRubSell'])
                if sell_rate:
                    rates['THB/RUB'] = rates.get('THB/RUB', {})
                    rates['THB/RUB']['sell'] = sell_rate
            
            # USDT/BAHT курсы
            if selectors.get('usdtBahtBuy'):
                buy_rate = self._extract_rate(soup, selectors['usdtBahtBuy'])
                if buy_rate:
                    rates['USDT/BAHT'] = rates.get('USDT/BAHT', {})
                    rates['USDT/BAHT']['buy'] = buy_rate
            
            if selectors.get('usdtBahtSell'):
                sell_rate = self._extract_rate(soup, selectors['usdtBahtSell'])
                if sell_rate:
                    rates['USDT/BAHT'] = rates.get('USDT/BAHT', {})
                    rates['USDT/BAHT']['sell'] = sell_rate
            
            # Apply automatic rate processing and validation
            processed_rates = await self._process_and_validate_rates(rates, exchanger_id)

            # Ensure mandatory currency pairs are present
            complete_rates = await self._ensure_mandatory_pairs(processed_rates, exchanger_id)

            # Create parsing result record
            execution_time_ms = int((time.time() - start_time) * 1000)
            parsing_result = ParsingResult(
                exchanger_id=exchanger_id,
                exchanger_name=exchanger_name,
                status='success',
                execution_time_ms=execution_time_ms,
                source_url=website_url,
                parsed_pairs_count=len(complete_rates),
                total_pairs_expected=len(self.MANDATORY_PAIRS),
                raw_data={'original_rates': rates, 'processed_rates': complete_rates},
                metadata={'processing_steps': ['extraction', 'validation', 'completion']},
                parsing_timestamp=parsing_timestamp
            )

            # Save parsing result to database
            parsing_result_id = await self.save_parsing_result(parsing_result)

            # Сохранение курсов в базу данных (existing method)
            saved_count = 0
            for currency_pair, rate_data in complete_rates.items():
                if 'buy' in rate_data and 'sell' in rate_data:
                    await self._save_exchange_rate(
                        exchanger_id=exchanger_id,
                        currency_pair=currency_pair,
                        buy_rate=rate_data['buy'],
                        sell_rate=rate_data['sell'],
                        source_url=website_url
                    )

                    # Save to historical rates table
                    historical_rate = HistoricalRate(
                        exchanger_id=exchanger_id,
                        exchanger_name=exchanger_name,
                        currency_pair=currency_pair,
                        buy_rate=rate_data['buy'],
                        sell_rate=rate_data['sell'],
                        parsing_result_id=parsing_result_id,
                        source_url=website_url,
                        data_quality_score=100,  # Default high quality for successful parsing
                        validation_status='valid',
                        parsed_at=parsing_timestamp
                    )

                    await self.save_historical_rate(historical_rate)
                    self.current_historical_rates.append(historical_rate)
                    saved_count += 1

            # Trigger network synchronization
            await self._synchronize_network_rates(exchanger_id, complete_rates)

            # Store parsing result in current session
            self.current_parsing_results.append(parsing_result)

            result.update({
                'success': True,
                'rates': complete_rates,
                'parsed_count': saved_count,
                'original_rates': rates,
                'processed_pairs': list(complete_rates.keys()),
                'parsing_result_id': parsing_result_id
            })

            # Логирование успешного парсинга
            await self._log_parsing_result(
                exchanger_id=exchanger_id,
                status='success',
                message=f'Успешно обновлено {saved_count} курсов валют',
                parsed_rates_count=saved_count,
                execution_time_ms=execution_time_ms
            )
            
        except Exception as e:
            error_msg = str(e)
            result['errors'].append(error_msg)
            execution_time_ms = int((time.time() - start_time) * 1000)

            # Create error parsing result record
            parsing_result = ParsingResult(
                exchanger_id=exchanger_id,
                exchanger_name=exchanger_name,
                status='error',
                execution_time_ms=execution_time_ms,
                source_url=website_url,
                parsed_pairs_count=0,
                total_pairs_expected=len(self.MANDATORY_PAIRS),
                error_message=error_msg,
                error_details={'error': error_msg, 'config': config},
                parsing_timestamp=parsing_timestamp
            )

            # Save error result to database
            parsing_result_id = await self.save_parsing_result(parsing_result)
            result['parsing_result_id'] = parsing_result_id

            # Store parsing result in current session
            self.current_parsing_results.append(parsing_result)

            # Логирование ошибки
            await self._log_parsing_result(
                exchanger_id=exchanger_id,
                status='error',
                message=f'Ошибка парсинга: {error_msg}',
                error_details={'error': error_msg, 'config': config},
                execution_time_ms=execution_time_ms
            )

            logger.error(f"Parsing error for exchanger {exchanger_id}: {error_msg}")

        result['execution_time_ms'] = int((time.time() - start_time) * 1000)
        return result
    
    def _extract_rate(self, soup: BeautifulSoup, selector: str) -> Optional[Decimal]:
        """
        Извлечение курса валюты по CSS селектору
        
        Args:
            soup: Объект BeautifulSoup
            selector: CSS селектор
            
        Returns:
            Курс валюты или None если не найден
        """
        try:
            element = soup.select_one(selector)
            if not element:
                return None
            
            # Извлечение числового значения
            text = element.get_text(strip=True)
            
            # Поиск числа в тексте (поддержка различных форматов)
            number_pattern = r'[\d,]+\.?\d*'
            match = re.search(number_pattern, text.replace(',', ''))
            
            if match:
                rate_str = match.group().replace(',', '')
                return Decimal(rate_str)
            
            return None
            
        except (InvalidOperation, AttributeError, ValueError) as e:
            logger.warning(f"Error extracting rate with selector '{selector}': {e}")
            return None
    
    async def _save_exchange_rate(self, exchanger_id: str, currency_pair: str, 
                                 buy_rate: Decimal, sell_rate: Decimal, source_url: str):
        """Сохранение курса валюты в базу данных"""
        try:
            # Деактивация старых курсов
            await database_service.execute(
                """
                UPDATE exchange_rates 
                SET is_active = false 
                WHERE exchanger_id = $1 AND currency_pair = $2
                """,
                exchanger_id, currency_pair
            )
            
            # Вставка нового курса
            await database_service.execute(
                """
                INSERT INTO exchange_rates 
                (exchanger_id, currency_pair, buy_rate, sell_rate, source_url, parsed_at, is_active)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                exchanger_id, currency_pair, buy_rate, sell_rate, source_url, datetime.now(), True
            )
            
        except Exception as e:
            logger.error(f"Error saving exchange rate: {e}")
            raise
    
    def _create_log_key(self, exchanger_id: str, status: str, message: str) -> str:
        """Create a unique key for log deduplication"""
        import hashlib
        key_data = f"{exchanger_id}:{status}:{message}:{int(time.time() // 60)}"  # Group by minute
        return hashlib.md5(key_data.encode()).hexdigest()

    async def _log_parsing_result(self, exchanger_id: str, status: str, message: str,
                                 error_details: Optional[Dict] = None,
                                 parsed_rates_count: int = 0,
                                 execution_time_ms: int = 0):
        """Логирование результата парсинга с предотвращением дубликатов"""
        # Create unique key for this log entry
        log_key = self._create_log_key(exchanger_id or 'system', status, message)

        # Skip if we've already logged this event recently
        if log_key in self._logged_events:
            return

        try:
            await database_service.execute(
                """
                INSERT INTO parsing_logs
                (exchanger_id, status, message, error_details, parsed_rates_count, execution_time_ms)
                VALUES ($1, $2, $3, $4, $5, $6)
                """,
                exchanger_id, status, message,
                json.dumps(error_details) if error_details else None,
                parsed_rates_count, execution_time_ms
            )

            # Mark this event as logged
            self._logged_events.add(log_key)

            # Clean up old log keys (keep only last 1000)
            if len(self._logged_events) > 1000:
                # Remove oldest half
                old_keys = list(self._logged_events)[:500]
                for key in old_keys:
                    self._logged_events.discard(key)

        except Exception as e:
            logger.error(f"Error logging parsing result: {e}")
    
    async def parse_all_enabled_exchangers(self) -> Dict[str, Any]:
        """
        Парсинг курсов для всех обменников с включенным парсингом
        
        Returns:
            Сводная статистика парсинга
        """
        if self.parsing_active:
            logger.warning("Parsing already in progress, skipping")
            return {'error': 'Parsing already in progress'}
        
        self.parsing_active = True
        start_time = time.time()
        
        try:
            # Получение списка обменников с включенным парсингом
            exchangers = await database_service.fetch_all(
                """
                SELECT id, name, website_url, parsing_config 
                FROM exchangers 
                WHERE parsing_enabled = true AND status = 'active' AND website_url IS NOT NULL
                """
            )
            
            if not exchangers:
                logger.info("No exchangers with parsing enabled found")
                return {'message': 'No exchangers to parse', 'total': 0}
            
            results = {
                'total_exchangers': len(exchangers),
                'successful': 0,
                'failed': 0,
                'total_rates_updated': 0,
                'execution_time_ms': 0,
                'details': []
            }
            
            # Парсинг каждого обменника
            for exchanger in exchangers:
                try:
                    config = {
                        'website_url': exchanger['website_url'],
                        'selectors': exchanger['parsing_config'].get('selectors', {}),
                        'timeout': exchanger['parsing_config'].get('timeout', 30),
                        'retry_attempts': exchanger['parsing_config'].get('retryAttempts', 3)
                    }
                    
                    result = await self.parse_exchanger_rates(exchanger['id'], config)
                    
                    if result['success']:
                        results['successful'] += 1
                        results['total_rates_updated'] += result['parsed_count']
                    else:
                        results['failed'] += 1
                    
                    results['details'].append({
                        'exchanger_id': exchanger['id'],
                        'exchanger_name': exchanger['name'],
                        'success': result['success'],
                        'rates_count': result['parsed_count'],
                        'errors': result['errors']
                    })
                    
                    # Обновление времени последнего парсинга
                    await database_service.execute(
                        "UPDATE exchangers SET last_parsed_at = $1 WHERE id = $2",
                        datetime.now(), exchanger['id']
                    )
                    
                    # Небольшая задержка между запросами
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.error(f"Error parsing exchanger {exchanger['name']}: {e}")
                    results['failed'] += 1
                    
                    await self._log_parsing_result(
                        exchanger_id=exchanger['id'],
                        status='error',
                        message=f'Критическая ошибка парсинга: {str(e)}',
                        error_details={'error': str(e)}
                    )
            
            results['execution_time_ms'] = int((time.time() - start_time) * 1000)
            
            # Общее логирование
            await self._log_parsing_result(
                exchanger_id=None,
                status='success' if results['failed'] == 0 else 'warning',
                message=f"Парсинг завершен: {results['successful']} успешно, {results['failed']} ошибок",
                parsed_rates_count=results['total_rates_updated'],
                execution_time_ms=results['execution_time_ms']
            )
            
            logger.info(f"Parsing completed: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Critical error in parse_all_enabled_exchangers: {e}")
            return {'error': str(e)}
        finally:
            self.parsing_active = False
    
    async def get_latest_rates(self, exchanger_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Получение последних курсов валют"""
        try:
            query = """
                SELECT er.*, e.name as exchanger_name
                FROM exchange_rates er
                JOIN exchangers e ON er.exchanger_id = e.id
                WHERE er.is_active = true
            """
            params = []
            
            if exchanger_id:
                query += " AND er.exchanger_id = $1"
                params.append(exchanger_id)
            
            query += " ORDER BY er.parsed_at DESC"
            
            rates = await database_service.fetch_all(query, *params)
            return [dict(rate) for rate in rates]
            
        except Exception as e:
            logger.error(f"Error getting latest rates: {e}")
            return []
    
    async def get_parsing_logs(self, exchanger_id: Optional[str] = None, 
                              limit: int = 50) -> List[Dict[str, Any]]:
        """Получение логов парсинга"""
        try:
            query = """
                SELECT pl.*, e.name as exchanger_name
                FROM parsing_logs pl
                LEFT JOIN exchangers e ON pl.exchanger_id = e.id
            """
            params = []
            
            if exchanger_id:
                query += " WHERE pl.exchanger_id = $1"
                params.append(exchanger_id)
            
            query += f" ORDER BY pl.created_at DESC LIMIT {limit}"
            
            logs = await database_service.fetch_all(query, *params)
            return [dict(log) for log in logs]
            
        except Exception as e:
            logger.error(f"Error getting parsing logs: {e}")
            return []
    
    async def test_parsing_config(self, website_url: str, selectors: Dict[str, str]) -> Dict[str, Any]:
        """
        Тестирование конфигурации парсинга
        
        Args:
            website_url: URL сайта для тестирования
            selectors: CSS селекторы для курсов
            
        Returns:
            Результат тестирования
        """
        try:
            if not self.session:
                await self.initialize()
            
            async with self.session.get(website_url) as response:
                if response.status != 200:
                    return {
                        'success': False,
                        'error': f"HTTP {response.status}: {response.reason}"
                    }
                
                html_content = await response.text()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            test_results = {}
            for currency, selector in selectors.items():
                if selector:
                    rate = self._extract_rate(soup, selector)
                    test_results[currency] = {
                        'selector': selector,
                        'found': rate is not None,
                        'value': float(rate) if rate else None
                    }
            
            return {
                'success': True,
                'results': test_results,
                'total_found': sum(1 for r in test_results.values() if r['found'])
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def schedule_parsing_task(self):
        """Планировщик задач парсинга (запускается периодически)"""
        try:
            # Получение обменников, которые нужно обновить
            exchangers = await database_service.fetch_all(
                """
                SELECT id, name, parsing_config
                FROM exchangers 
                WHERE parsing_enabled = true 
                AND status = 'active'
                AND (
                    last_parsed_at IS NULL 
                    OR last_parsed_at < NOW() - INTERVAL '1 hour' * (parsing_config->>'updateInterval')::int / 60
                )
                """
            )
            
            if not exchangers:
                logger.info("No exchangers need rate updates")
                return
            
            logger.info(f"Starting scheduled parsing for {len(exchangers)} exchangers")
            
            # Запуск парсинга
            result = await self.parse_all_enabled_exchangers()
            
            # Отправка уведомлений об ошибках (если настроено)
            if result.get('failed', 0) > 0:
                await self._send_parsing_error_notification(result)
            
        except Exception as e:
            logger.error(f"Error in scheduled parsing task: {e}")
    
    async def _send_parsing_error_notification(self, result: Dict[str, Any]):
        """Отправка уведомлений об ошибках парсинга"""
        try:
            # Здесь можно интегрировать с email сервисом, Telegram ботом и т.д.
            error_details = [
                detail for detail in result.get('details', [])
                if not detail['success']
            ]
            
            if error_details:
                logger.warning(f"Parsing errors detected: {len(error_details)} exchangers failed")
                
                # В продакшне здесь был бы вызов сервиса уведомлений
                notification_message = f"""
                Обнаружены ошибки парсинга курсов валют:
                
                Всего обменников: {result['total_exchangers']}
                Успешно: {result['successful']}
                Ошибок: {result['failed']}
                
                Обменники с ошибками:
                {chr(10).join([f"- {detail['exchanger_name']}: {', '.join(detail['errors'])}" for detail in error_details])}
                """
                
                logger.error(notification_message)
                
        except Exception as e:
            logger.error(f"Error sending parsing error notification: {e}")

    async def _process_and_validate_rates(self, rates: Dict[str, Any], exchanger_id: str) -> Dict[str, Any]:
        """
        Process and validate parsed rates, including automatic inverse calculation

        Args:
            rates: Raw parsed rates
            exchanger_id: ID of the exchanger

        Returns:
            Processed and validated rates with inverse calculations
        """
        processed_rates = {}

        try:
            # Process each currency pair
            for pair, rate_data in rates.items():
                if not isinstance(rate_data, dict) or 'buy' not in rate_data or 'sell' not in rate_data:
                    continue

                buy_rate = rate_data['buy']
                sell_rate = rate_data['sell']

                # Validate rate values
                if not self._validate_rate_values(buy_rate, sell_rate):
                    logger.warning(f"Invalid rate values for {pair}: buy={buy_rate}, sell={sell_rate}")
                    continue

                # Validate rate ranges
                validation_result = self._validate_rate_range(pair, buy_rate, sell_rate)
                if not validation_result['valid']:
                    logger.warning(f"Rate out of range for {pair}: {validation_result['message']}")
                    # Still include the rate but flag it
                    rate_data['flagged'] = True
                    rate_data['flag_reason'] = validation_result['message']

                processed_rates[pair] = rate_data

                # Calculate inverse rates for THB/RUB
                if pair == 'THB/RUB':
                    inverse_rates = self._calculate_inverse_rates(buy_rate, sell_rate)
                    if inverse_rates:
                        processed_rates['RUB/THB'] = inverse_rates
                        logger.info(f"Calculated inverse RUB/THB rates: buy={inverse_rates['buy']}, sell={inverse_rates['sell']}")

            logger.info(f"Processed {len(processed_rates)} currency pairs for exchanger {exchanger_id}")
            return processed_rates

        except Exception as e:
            logger.error(f"Error processing rates for exchanger {exchanger_id}: {e}")
            return rates  # Return original rates if processing fails

    def _validate_rate_values(self, buy_rate: Decimal, sell_rate: Decimal) -> bool:
        """Validate that rate values are positive and sell >= buy"""
        try:
            if buy_rate <= 0 or sell_rate <= 0:
                return False
            if sell_rate < buy_rate:
                return False
            return True
        except (TypeError, InvalidOperation):
            return False

    def _validate_rate_range(self, pair: str, buy_rate: Decimal, sell_rate: Decimal) -> Dict[str, Any]:
        """Validate that rates fall within expected ranges"""
        if pair not in self.RATE_VALIDATION_RANGES:
            return {'valid': True, 'message': 'No validation range defined'}

        range_config = self.RATE_VALIDATION_RANGES[pair]
        min_rate = range_config['min']
        max_rate = range_config['max']

        if buy_rate < min_rate or buy_rate > max_rate:
            return {
                'valid': False,
                'message': f'Buy rate {buy_rate} outside range [{min_rate}, {max_rate}]'
            }

        if sell_rate < min_rate or sell_rate > max_rate:
            return {
                'valid': False,
                'message': f'Sell rate {sell_rate} outside range [{min_rate}, {max_rate}]'
            }

        return {'valid': True, 'message': 'Rate within valid range'}

    def _calculate_inverse_rates(self, thb_rub_buy: Decimal, thb_rub_sell: Decimal) -> Optional[Dict[str, Any]]:
        """
        Calculate inverse RUB/THB rates from THB/RUB rates

        Args:
            thb_rub_buy: THB/RUB buy rate
            thb_rub_sell: THB/RUB sell rate

        Returns:
            Dictionary with RUB/THB buy and sell rates
        """
        try:
            # For inverse calculation:
            # RUB/THB buy = 1 / THB/RUB sell (customer buys RUB, sells THB)
            # RUB/THB sell = 1 / THB/RUB buy (customer sells RUB, buys THB)

            rub_thb_buy = Decimal('1') / thb_rub_sell
            rub_thb_sell = Decimal('1') / thb_rub_buy

            # Round to appropriate decimal places
            rub_thb_buy = rub_thb_buy.quantize(Decimal('0.01'))  # 2 decimal places
            rub_thb_sell = rub_thb_sell.quantize(Decimal('0.01'))  # 2 decimal places

            return {
                'buy': rub_thb_buy,
                'sell': rub_thb_sell,
                'calculated': True,
                'source_pair': 'THB/RUB'
            }

        except (InvalidOperation, ZeroDivisionError) as e:
            logger.error(f"Error calculating inverse rates: {e}")
            return None

    async def _ensure_mandatory_pairs(self, rates: Dict[str, Any], exchanger_id: str) -> Dict[str, Any]:
        """
        Ensure all mandatory currency pairs are present, calculating missing ones if possible

        Args:
            rates: Processed rates
            exchanger_id: ID of the exchanger

        Returns:
            Rates with all mandatory pairs present
        """
        complete_rates = rates.copy()
        missing_pairs = []

        for pair in self.MANDATORY_PAIRS:
            if pair not in complete_rates:
                missing_pairs.append(pair)

        if missing_pairs:
            logger.info(f"Missing mandatory pairs for exchanger {exchanger_id}: {missing_pairs}")

            # Try to derive missing pairs from available rates
            for pair in missing_pairs:
                derived_rate = await self._derive_missing_rate(pair, complete_rates, exchanger_id)
                if derived_rate:
                    complete_rates[pair] = derived_rate
                    logger.info(f"Derived missing rate for {pair}: {derived_rate}")
                else:
                    # Mark as unavailable but still include the pair
                    complete_rates[pair] = {
                        'buy': None,
                        'sell': None,
                        'unavailable': True,
                        'reason': 'Could not derive from available rates'
                    }
                    logger.warning(f"Could not derive rate for {pair}, marked as unavailable")

        return complete_rates

    async def _derive_missing_rate(self, pair: str, available_rates: Dict[str, Any], exchanger_id: str = None) -> Optional[Dict[str, Any]]:
        """
        Try to derive a missing currency pair from available rates

        Args:
            pair: Missing currency pair
            available_rates: Available rates to derive from
            exchanger_id: ID of the exchanger (for logging)

        Returns:
            Derived rate or None if cannot be derived
        """
        # Log the derivation attempt
        if exchanger_id:
            logger.debug(f"Attempting to derive {pair} for exchanger {exchanger_id}")
        try:
            # THB/RUB can be derived from RUB/THB
            if pair == 'THB/RUB' and 'RUB/THB' in available_rates:
                rub_thb = available_rates['RUB/THB']
                if rub_thb.get('buy') and rub_thb.get('sell'):
                    thb_rub_buy = Decimal('1') / rub_thb['sell']
                    thb_rub_sell = Decimal('1') / rub_thb['buy']

                    return {
                        'buy': thb_rub_buy.quantize(Decimal('0.001')),
                        'sell': thb_rub_sell.quantize(Decimal('0.001')),
                        'calculated': True,
                        'source_pair': 'RUB/THB'
                    }

            # RUB/THB can be derived from THB/RUB (already handled in processing)
            if pair == 'RUB/THB' and 'THB/RUB' in available_rates:
                thb_rub = available_rates['THB/RUB']
                if thb_rub.get('buy') and thb_rub.get('sell'):
                    return self._calculate_inverse_rates(thb_rub['buy'], thb_rub['sell'])

            # USDT/THB might be derived from other USD rates if available
            # This would require more complex logic and market data

            return None

        except Exception as e:
            logger.error(f"Error deriving rate for {pair}: {e}")
            return None

    async def _synchronize_network_rates(self, source_exchanger_id: str, rates: Dict[str, Any]):
        """
        Synchronize rates across all exchangers in the same network

        Args:
            source_exchanger_id: ID of the exchanger that was just parsed
            rates: Rates to synchronize across the network
        """
        try:
            # Get exchanger details to identify network
            source_exchanger = await self._get_exchanger_details(source_exchanger_id)
            if not source_exchanger:
                return

            # Identify network members
            network_members = await self._identify_network_members(source_exchanger)

            if len(network_members) <= 1:
                logger.debug(f"No network members found for exchanger {source_exchanger_id}")
                return

            logger.info(f"Synchronizing rates across {len(network_members)} network members")

            # Synchronize rates to all network members
            sync_count = 0
            for member_id in network_members:
                if member_id != source_exchanger_id:  # Don't sync to source
                    success = await self._sync_rates_to_exchanger(member_id, rates, source_exchanger_id)
                    if success:
                        sync_count += 1

            logger.info(f"Successfully synchronized rates to {sync_count} network members")

            # Update sync cache
            self._network_sync_cache[source_exchanger_id] = {
                'timestamp': datetime.now(),
                'synced_to': network_members,
                'rates_count': len(rates)
            }

        except Exception as e:
            logger.error(f"Error synchronizing network rates: {e}")

    async def _get_exchanger_details(self, exchanger_id: str) -> Optional[Dict[str, Any]]:
        """Get exchanger details from database"""
        try:
            result = await database_service.fetch_one(
                "SELECT * FROM exchangers WHERE id = %s AND is_active = true",
                (exchanger_id,)
            )
            return dict(result) if result else None
        except Exception as e:
            logger.error(f"Error getting exchanger details: {e}")
            return None

    async def _identify_network_members(self, exchanger: Dict[str, Any]) -> List[str]:
        """
        Identify all exchangers belonging to the same network

        Args:
            exchanger: Source exchanger details

        Returns:
            List of exchanger IDs in the same network
        """
        try:
            network_members = []

            # Method 1: Match by exact name (for chains like "Bangkok Bank Exchange")
            if exchanger.get('name'):
                base_name = self._extract_base_name(exchanger['name'])
                name_matches = await database_service.fetch_all(
                    "SELECT id FROM exchangers WHERE name ILIKE %s AND is_active = true",
                    (f"%{base_name}%",)
                )
                network_members.extend([row['id'] for row in name_matches])

            # Method 2: Match by website domain
            if exchanger.get('website_url'):
                domain = self._extract_domain(exchanger['website_url'])
                if domain:
                    domain_matches = await database_service.fetch_all(
                        "SELECT id FROM exchangers WHERE website_url ILIKE %s AND is_active = true",
                        (f"%{domain}%",)
                    )
                    network_members.extend([row['id'] for row in domain_matches])

            # Method 3: Match by explicit network identifier (if available)
            if exchanger.get('network_id'):
                network_matches = await database_service.fetch_all(
                    "SELECT id FROM exchangers WHERE network_id = %s AND is_active = true",
                    (exchanger['network_id'],)
                )
                network_members.extend([row['id'] for row in network_matches])

            # Remove duplicates and return
            return list(set(network_members))

        except Exception as e:
            logger.error(f"Error identifying network members: {e}")
            return [exchanger['id']]  # Return only source exchanger

    def _extract_base_name(self, name: str) -> str:
        """Extract base name for network matching"""
        # Remove common suffixes and location indicators
        base_name = re.sub(r'\s*(branch|филиал|отделение|\d+|центр|center)\s*$', '', name, flags=re.IGNORECASE)
        base_name = re.sub(r'\s*\([^)]*\)\s*', '', base_name)  # Remove parentheses
        return base_name.strip()

    def _extract_domain(self, url: str) -> Optional[str]:
        """Extract domain from URL for network matching"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc.lower()
        except Exception:
            return None

    async def _sync_rates_to_exchanger(self, target_exchanger_id: str, rates: Dict[str, Any], source_exchanger_id: str) -> bool:
        """
        Synchronize rates to a specific exchanger

        Args:
            target_exchanger_id: ID of target exchanger
            rates: Rates to synchronize
            source_exchanger_id: ID of source exchanger

        Returns:
            True if synchronization was successful
        """
        try:
            # Check if target exchanger was recently updated (avoid ping-pong)
            recent_update = await self._check_recent_update(target_exchanger_id)
            if recent_update:
                logger.debug(f"Skipping sync to {target_exchanger_id} - recently updated")
                return False

            # Save synchronized rates
            sync_count = 0
            for currency_pair, rate_data in rates.items():
                if rate_data.get('buy') and rate_data.get('sell'):
                    await self._save_exchange_rate(
                        exchanger_id=target_exchanger_id,
                        currency_pair=currency_pair,
                        buy_rate=rate_data['buy'],
                        sell_rate=rate_data['sell'],
                        source_url=f"network_sync_from_{source_exchanger_id}"
                    )
                    sync_count += 1

            logger.info(f"Synchronized {sync_count} rates to exchanger {target_exchanger_id}")
            return True

        except Exception as e:
            logger.error(f"Error syncing rates to exchanger {target_exchanger_id}: {e}")
            return False

    async def _check_recent_update(self, exchanger_id: str) -> bool:
        """Check if exchanger was updated recently (within 5 minutes)"""
        try:
            recent_time = datetime.now() - timedelta(minutes=5)
            result = await database_service.fetch_one(
                """
                SELECT COUNT(*) as count FROM exchange_rates
                WHERE exchanger_id = %s AND created_at > %s
                """,
                (exchanger_id, recent_time)
            )
            return result['count'] > 0 if result else False
        except Exception as e:
            logger.error(f"Error checking recent update: {e}")
            return False

    async def save_parsing_result(self, parsing_result: ParsingResult) -> Optional[int]:
        """
        Save parsing operation result to database

        Args:
            parsing_result: ParsingResult object with operation details

        Returns:
            ID of saved parsing result record or None if failed
        """
        try:
            query = """
                INSERT INTO parsing_results (
                    exchanger_id, exchanger_name, parsing_timestamp, status,
                    execution_time_ms, source_url, parsed_pairs_count, total_pairs_expected,
                    error_message, error_details, raw_data, metadata
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                RETURNING id
            """

            result = await database_service.fetch_one(
                query,
                parsing_result.exchanger_id,
                parsing_result.exchanger_name,
                parsing_result.parsing_timestamp or datetime.now(),
                parsing_result.status,
                parsing_result.execution_time_ms,
                parsing_result.source_url,
                parsing_result.parsed_pairs_count,
                parsing_result.total_pairs_expected,
                parsing_result.error_message,
                json.dumps(parsing_result.error_details) if parsing_result.error_details else None,
                json.dumps(parsing_result.raw_data) if parsing_result.raw_data else None,
                json.dumps(parsing_result.metadata) if parsing_result.metadata else None
            )

            if result:
                parsing_result_id = result['id']
                logger.info(f"Saved parsing result {parsing_result_id} for exchanger {parsing_result.exchanger_id}")
                return parsing_result_id

        except Exception as e:
            logger.error(f"Error saving parsing result for {parsing_result.exchanger_id}: {e}")

        return None

    async def save_historical_rate(self, historical_rate: HistoricalRate) -> bool:
        """
        Save historical rate data to database

        Args:
            historical_rate: HistoricalRate object with rate data

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            query = """
                INSERT INTO historical_rates (
                    exchanger_id, exchanger_name, currency_pair, buy_rate, sell_rate,
                    parsing_result_id, source_url, data_quality_score, validation_status,
                    validation_notes, parsed_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            """

            await database_service.execute(
                query,
                historical_rate.exchanger_id,
                historical_rate.exchanger_name,
                historical_rate.currency_pair,
                float(historical_rate.buy_rate),
                float(historical_rate.sell_rate),
                historical_rate.parsing_result_id,
                historical_rate.source_url,
                historical_rate.data_quality_score,
                historical_rate.validation_status,
                historical_rate.validation_notes,
                historical_rate.parsed_at or datetime.now()
            )

            logger.debug(f"Saved historical rate for {historical_rate.exchanger_id} - {historical_rate.currency_pair}")
            return True

        except Exception as e:
            logger.error(f"Error saving historical rate for {historical_rate.exchanger_id}: {e}")
            return False

    async def get_parsing_results(self,
                                 limit: int = 100,
                                 offset: int = 0,
                                 exchanger_id: Optional[str] = None,
                                 status: Optional[str] = None,
                                 hours_back: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Retrieve parsing results from database

        Args:
            limit: Maximum number of results to return
            offset: Number of results to skip
            exchanger_id: Filter by specific exchanger
            status: Filter by parsing status
            hours_back: Only return results from last N hours

        Returns:
            List of parsing result dictionaries
        """
        try:
            conditions = []
            params = []
            param_count = 0

            if exchanger_id:
                param_count += 1
                conditions.append(f"exchanger_id = ${param_count}")
                params.append(exchanger_id)

            if status:
                param_count += 1
                conditions.append(f"status = ${param_count}")
                params.append(status)

            if hours_back:
                param_count += 1
                conditions.append(f"parsing_timestamp >= NOW() - INTERVAL '{hours_back} hours'")

            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

            query = f"""
                SELECT
                    id, exchanger_id, exchanger_name, parsing_timestamp, status,
                    execution_time_ms, source_url, parsed_pairs_count, total_pairs_expected,
                    error_message, error_details, raw_data, metadata, created_at
                FROM parsing_results
                {where_clause}
                ORDER BY parsing_timestamp DESC
                LIMIT ${param_count + 1} OFFSET ${param_count + 2}
            """

            params.extend([limit, offset])

            results = await database_service.fetch_all(query, *params)
            return [dict(row) for row in results] if results else []

        except Exception as e:
            logger.error(f"Error retrieving parsing results: {e}")
            return []

    async def get_historical_rates(self,
                                  currency_pair: Optional[str] = None,
                                  exchanger_id: Optional[str] = None,
                                  days_back: int = 7,
                                  limit: int = 1000) -> List[Dict[str, Any]]:
        """
        Retrieve historical rate data from database

        Args:
            currency_pair: Filter by specific currency pair
            exchanger_id: Filter by specific exchanger
            days_back: Number of days to look back
            limit: Maximum number of results

        Returns:
            List of historical rate dictionaries
        """
        try:
            conditions = ["parsed_at >= NOW() - INTERVAL '%s days'"]
            params = [days_back]
            param_count = 1

            if currency_pair:
                param_count += 1
                conditions.append(f"currency_pair = ${param_count}")
                params.append(currency_pair)

            if exchanger_id:
                param_count += 1
                conditions.append(f"exchanger_id = ${param_count}")
                params.append(exchanger_id)

            where_clause = " WHERE " + " AND ".join(conditions)

            query = f"""
                SELECT
                    id, exchanger_id, exchanger_name, currency_pair, buy_rate, sell_rate,
                    spread, spread_percentage, parsing_result_id, source_url,
                    data_quality_score, validation_status, validation_notes, parsed_at
                FROM historical_rates
                {where_clause}
                ORDER BY parsed_at DESC
                LIMIT ${param_count + 1}
            """

            params.append(limit)

            results = await database_service.fetch_all(query, *params)
            return [dict(row) for row in results] if results else []

        except Exception as e:
            logger.error(f"Error retrieving historical rates: {e}")
            return []

    def clear_current_session_data(self):
        """Clear current parsing session data"""
        self.current_parsing_results.clear()
        self.current_historical_rates.clear()

    def get_current_session_results(self) -> Tuple[List[ParsingResult], List[HistoricalRate]]:
        """Get current parsing session results"""
        return self.current_parsing_results.copy(), self.current_historical_rates.copy()


# Глобальный экземпляр сервиса
rate_parser_service = RateParserService()