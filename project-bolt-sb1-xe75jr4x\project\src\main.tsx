import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { validateEnvironment } from './config/environment';
import { initializeMonitoring } from './utils/monitoring';
import { errorHandler } from './utils/errorHandling';
import { initializePageStabilityMonitoring } from './utils/pageStabilityMonitor';

// Валидация переменных окружения
try {
  validateEnvironment();
} catch (error) {
  console.error('Environment validation failed:', error);
  // В продакшне можно показать страницу с ошибкой конфигурации
}

// Инициализация мониторинга и аналитики
initializeMonitoring();

// Инициализация мониторинга стабильности страницы
initializePageStabilityMonitoring();

// Глобальная обработка ошибок React
const handleReactError = (error: Error, errorInfo: any) => {
  errorHandler.handleError(error, { errorInfo, component: 'React' });
};

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
