# Backend Environment Variables

# Application
APP_NAME="Thailand Exchange Platform"
APP_VERSION="1.0.0"
DEBUG=false

# Database
DATABASE_URL="postgresql://user:password@localhost:5432/thailand_exchange"
REDIS_URL="redis://localhost:6379"

# Google Sheets API
GOOGLE_SHEETS_CREDENTIALS_FILE="./credentials/google-sheets-credentials.json"
GOOGLE_SHEETS_SPREADSHEET_ID="1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
GOOGLE_SHEETS_WORKSHEET_NAME="Exchange Rates"

# Security
SECRET_KEY="your-super-secret-key-here-change-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
BACKEND_CORS_ORIGINS="http://localhost:3000,http://localhost:5173,https://yourdomain.com"

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Cache Settings
CACHE_TTL_SECONDS=300
RATES_UPDATE_INTERVAL_MINUTES=15

# Monitoring
SENTRY_DSN="https://<EMAIL>/project-id"
LOG_LEVEL="INFO"