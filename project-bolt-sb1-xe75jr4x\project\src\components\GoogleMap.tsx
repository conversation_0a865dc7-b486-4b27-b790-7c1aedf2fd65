import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Loader } from '@googlemaps/js-api-loader';
import { MapPin, Navigation, Phone, Clock, Star, Filter, AlertCircle, RefreshCw } from 'lucide-react';
import { districts } from '../data/mockData';
import { useExchangers } from '../hooks/useExchangeData';
import { phuketDistricts } from '../data/mapData';
import { config } from '../config/environment';
import { Exchanger } from '../types';
import MapFallback from './MapFallback';

interface GoogleMapProps {
  searchQuery: string;
  onViewReviews?: (exchangerId: number) => void;
}

// Thailand bounds for map centering
const THAILAND_BOUNDS = {
  north: 20.4648,
  south: 5.6108,
  west: 97.3438,
  east: 105.6396,
};

// Phuket center coordinates
const PHUKET_CENTER = {
  lat: 7.8804,
  lng: 98.3923,
};

const GoogleMap: React.FC<GoogleMapProps> = ({ searchQuery, onViewReviews }) => {
  const [selectedDistrict, setSelectedDistrict] = useState<string | 'all'>('all');
  const [selectedExchanger, setSelectedExchanger] = useState<number | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [mapError, setMapError] = useState<string | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const [useFallback, setUseFallback] = useState(false);

  // Fetch exchangers from API
  const { 
    data: exchangersData, 
    isLoading: exchangersLoading,
    error: exchangersError,
    refetch: refetchExchangers
  } = useExchangers({
    district: selectedDistrict === 'all' ? undefined : selectedDistrict,
    search: searchQuery || undefined,
    limit: 100
  });

  const allExchangers = exchangersData?.exchangers || [];

  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<google.maps.Map | null>(null);
  const markersRef = useRef<google.maps.Marker[]>([]);
  const infoWindowRef = useRef<google.maps.InfoWindow | null>(null);

  // Filter exchangers based on search and district
  const filteredExchangers = allExchangers.filter(exchanger => {
    const matchesSearch = exchanger.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         exchanger.address.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesDistrict = selectedDistrict === 'all' || exchanger.district === selectedDistrict;
    return matchesSearch && matchesDistrict;
  });

  // Initialize Google Maps
  const initializeMap = useCallback(async () => {
    if (!config.GOOGLE_MAPS_API_KEY) {
      setMapError('Google Maps API key not configured');
      setUseFallback(true);
      return;
    }

    if (!mapRef.current) {
      setMapError('Map container not available');
      setUseFallback(true);
      return;
    }

    try {
      setMapError(null);
      setIsRetrying(true);
      setUseFallback(false);

      const loader = new Loader({
        apiKey: config.GOOGLE_MAPS_API_KEY,
        version: 'weekly',
        libraries: ['places', 'geometry'],
      });

      await loader.load();

      const map = new google.maps.Map(mapRef.current, {
        center: PHUKET_CENTER,
        zoom: 11,
        restriction: {
          latLngBounds: THAILAND_BOUNDS,
          strictBounds: false,
        },
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }],
          },
          {
            featureType: 'transit',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }],
          },
        ],
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
      });

      googleMapRef.current = map;
      infoWindowRef.current = new google.maps.InfoWindow();

      setMapLoaded(true);
      setIsRetrying(false);
    } catch (error) {
      console.error('Error loading Google Maps:', error);
      setMapError('Failed to load Google Maps. Please check your internet connection.');
      setUseFallback(true);
      setIsRetrying(false);
    }
  }, []);

  // Create marker for exchanger
  const createExchangerMarker = useCallback((exchanger: Exchanger) => {
    if (!googleMapRef.current) return null;

    const marker = new google.maps.Marker({
      position: { lat: exchanger.coordinates.lat, lng: exchanger.coordinates.lng },
      map: googleMapRef.current,
      title: exchanger.name,
      icon: {
        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
          <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
            <circle cx="16" cy="16" r="14" fill="#3B82F6" stroke="#1E40AF" stroke-width="2"/>
            <text x="16" y="20" text-anchor="middle" fill="white" font-size="16" font-weight="bold">₿</text>
          </svg>
        `),
        scaledSize: new google.maps.Size(32, 32),
        anchor: new google.maps.Point(16, 16),
      },
    });

    // Create info window content
    const rubRate = exchanger.rates.find(r => r.currency === 'RUB');
    const infoContent = `
      <div style="max-width: 300px; padding: 12px; font-family: system-ui, -apple-system, sans-serif;">
        <h4 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600; color: #1f2937;">${exchanger.name}</h4>
        
        <div style="display: flex; align-items: center; margin-bottom: 6px; color: #6b7280; font-size: 14px;">
          <span style="margin-right: 8px;">📍</span>
          <span>${exchanger.address}</span>
        </div>
        
        <div style="display: flex; align-items: center; margin-bottom: 6px; color: #6b7280; font-size: 14px;">
          <span style="margin-right: 8px;">📞</span>
          <span>${exchanger.phone}</span>
        </div>
        
        <div style="display: flex; align-items: center; margin-bottom: 8px; color: #6b7280; font-size: 14px;">
          <span style="margin-right: 8px;">🕒</span>
          <span>${exchanger.hours}</span>
        </div>
        
        ${rubRate ? `
          <div style="background: #f9fafb; border-radius: 6px; padding: 8px; margin-top: 8px;">
            <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">Курс RUB</div>
            <div style="display: flex; justify-content: space-between; font-size: 14px;">
              <span style="color: #059669; font-weight: 600;">
                Покупка: ${rubRate.buy.toFixed(2)}
              </span>
              <span style="color: #dc2626; font-weight: 600;">
                Продажа: ${rubRate.sell.toFixed(2)}
              </span>
            </div>
          </div>
        ` : ''}
        
        <div style="margin-top: 8px;">
          ${renderStarsHTML(exchanger.rating)}
        </div>
      </div>
    `;

    marker.addListener('click', () => {
      if (infoWindowRef.current) {
        infoWindowRef.current.setContent(infoContent);
        infoWindowRef.current.open(googleMapRef.current, marker);
      }
      setSelectedExchanger(exchanger.id);
    });

    return marker;
  }, []);

  // Render stars as HTML string
  const renderStarsHTML = (rating: number) => {
    const stars = Array.from({ length: 5 }, (_, i) => {
      const filled = i < Math.floor(rating);
      return `<span style="color: ${filled ? '#fbbf24' : '#d1d5db'};">★</span>`;
    }).join('');
    
    return `
      <div style="display: flex; align-items: center; font-size: 14px;">
        ${stars}
        <span style="margin-left: 4px; color: #6b7280;">${rating.toFixed(1)}</span>
      </div>
    `;
  };

  // Update markers when exchangers change
  useEffect(() => {
    if (!googleMapRef.current || !mapLoaded) return;

    // Clear existing markers
    markersRef.current.forEach(marker => marker.setMap(null));
    markersRef.current = [];

    // Add new markers
    filteredExchangers.forEach(exchanger => {
      const marker = createExchangerMarker(exchanger);
      if (marker) {
        markersRef.current.push(marker);
      }
    });

    // Adjust map bounds to show all markers
    if (filteredExchangers.length > 0) {
      const bounds = new google.maps.LatLngBounds();
      filteredExchangers.forEach(exchanger => {
        bounds.extend({ lat: exchanger.coordinates.lat, lng: exchanger.coordinates.lng });
      });
      googleMapRef.current.fitBounds(bounds);
      
      // Ensure minimum zoom level
      const listener = google.maps.event.addListener(googleMapRef.current, 'bounds_changed', () => {
        if (googleMapRef.current && googleMapRef.current.getZoom() && googleMapRef.current.getZoom()! > 15) {
          googleMapRef.current.setZoom(15);
        }
        google.maps.event.removeListener(listener);
      });
    }
  }, [filteredExchangers, mapLoaded, createExchangerMarker]);

  // Initialize map on component mount
  useEffect(() => {
    initializeMap();
  }, [initializeMap]);

  const getDistrictName = (districtId: string) => {
    return districts.find(d => d.id === districtId)?.name || districtId;
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-3 h-3 ${
              i < Math.floor(rating) 
                ? 'text-yellow-400 fill-current' 
                : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-xs text-gray-600">
          {rating.toFixed(1)}
        </span>
      </div>
    );
  };

  const handleRetry = () => {
    setMapError(null);
    setMapLoaded(false);
    setUseFallback(false);
    initializeMap();
  };

  // Use fallback if Google Maps failed to load
  if (useFallback) {
    return (
      <MapFallback
        exchangers={filteredExchangers}
        searchQuery={searchQuery}
        selectedExchanger={selectedExchanger}
        onExchangerSelect={setSelectedExchanger}
        onViewReviews={onViewReviews}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-xl border border-indigo-100">
        <div className="flex items-center space-x-3 mb-4">
          <div className="bg-gradient-to-br from-indigo-500 to-purple-500 p-3 rounded-lg">
            <Navigation className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Карта обменников Google Maps</h2>
            <p className="text-gray-600">
              {exchangersLoading ? 'Загружаем данные обменников...' : 'Найдите ближайший обменный пункт на карте Пхукета'}
            </p>
          </div>
        </div>

        {/* District Filter */}
        <div className="flex items-center space-x-4">
          <Filter className="w-5 h-5 text-gray-500" />
          <div className="flex flex-wrap gap-4">
            <select
              value={selectedDistrict}
              onChange={(e) => setSelectedDistrict(e.target.value)}
              className="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors cursor-pointer"
              title="Выберите район для фильтрации обменников"
            >
              <option value="all">Все районы</option>
              {phuketDistricts.map(district => (
                <option key={district.id} value={district.id}>{district.name}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Error State */}
      {mapError && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-6">
          <div className="flex items-center space-x-3 mb-4">
            <AlertCircle className="w-6 h-6 text-red-500" />
            <h3 className="text-lg font-semibold text-red-800">Ошибка загрузки карты</h3>
          </div>
          <p className="text-red-700 mb-4">{mapError}</p>
          <button
            onClick={handleRetry}
            disabled={isRetrying}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isRetrying ? 'animate-spin' : ''}`} />
            <span>{isRetrying ? 'Повторная загрузка...' : 'Попробовать снова'}</span>
          </button>
        </div>
      )}

      {/* Loading State */}
      {exchangersLoading && (
        <div className="text-center py-12 bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <div className="text-gray-600 text-lg">Загрузка обменников на карту...</div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Map Area */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden relative">
            {/* Google Map Container */}
            <div 
              ref={mapRef}
              className="h-96 lg:h-[600px] w-full"
              style={{ minHeight: '400px' }}
            />
            
            {/* Loading State */}
            {!mapLoaded && !mapError && (
              <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-green-100 flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Загрузка Google Maps...</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Exchangers List */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800">
            Обменники на карте ({exchangersLoading ? '...' : filteredExchangers.length})
          </h3>
          
          {exchangersLoading ? (
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="bg-white p-4 rounded-lg shadow border border-gray-100 animate-pulse">
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-3 bg-gray-200 rounded w-full"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-3 max-h-[500px] overflow-y-auto">
              {filteredExchangers.map(exchanger => (
                <div
                  key={exchanger.id}
                  className={`bg-white p-4 rounded-lg shadow border transition-all duration-200 cursor-pointer ${
                    selectedExchanger === exchanger.id
                      ? 'border-blue-500 shadow-lg'
                      : 'border-gray-100 hover:shadow-md'
                  }`}
                  onClick={() => {
                    setSelectedExchanger(exchanger.id === selectedExchanger ? null : exchanger.id);
                    
                    // Pan to exchanger on map
                    if (googleMapRef.current) {
                      googleMapRef.current.panTo({ 
                        lat: exchanger.coordinates.lat, 
                        lng: exchanger.coordinates.lng 
                      });
                      googleMapRef.current.setZoom(15);
                    }
                  }}
                >
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <h4 className="font-semibold text-gray-800 text-sm">{exchanger.name}</h4>
                      <MapPin className="w-4 h-4 text-blue-500 flex-shrink-0" />
                    </div>
                    
                    {renderStars(exchanger.rating)}
                    
                    <div className="text-xs text-gray-600 space-y-1">
                      <div className="font-medium">{getDistrictName(exchanger.district)}</div>
                      <div>{exchanger.address}</div>
                      <div>{exchanger.hours}</div>
                    </div>
                    
                    <div className="bg-gray-50 rounded p-2">
                      <div className="flex justify-between text-xs">
                        <span className="text-green-600">
                          {exchanger.rates.find(r => r.currency === 'RUB')?.buy.toFixed(2)}
                        </span>
                        <span className="text-red-600">
                          {exchanger.rates.find(r => r.currency === 'RUB')?.sell.toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {!exchangersLoading && filteredExchangers.length === 0 && (
        <div className="text-center py-12 bg-white rounded-xl shadow-lg border border-gray-100">
          <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <div className="text-gray-500 text-lg">
            Обменники не найдены
          </div>
          <p className="text-gray-400 mt-2">
            Попробуйте изменить параметры поиска или фильтры
          </p>
        </div>
      )}
    </div>
  );
};

export default GoogleMap;
