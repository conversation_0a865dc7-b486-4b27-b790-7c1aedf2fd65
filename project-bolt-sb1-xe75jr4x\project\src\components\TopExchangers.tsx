import React, { useState, useEffect } from 'react';
import { Award, Star, AlertCircle, RefreshCw, TrendingUp, MapPin, Phone, Clock, Globe, MessageCircle, Shield, CheckCircle } from 'lucide-react';
import { districts } from '../data/mockData';
import { useTopExchangers } from '../hooks/useExchangeData';
import { Exchanger } from '../types';

interface TopExchangersProps {
  searchQuery: string;
  onViewReviews?: (exchangerId: number) => void;
}

const TopExchangers: React.FC<TopExchangersProps> = ({ searchQuery, onViewReviews }) => {
  const [sortBy, setSortBy] = useState<'rating' | 'reviews' | 'rates'>('rating');
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch top exchangers from API with enhanced error handling
  const { 
    data: topExchangersData, 
    isLoading, 
    error,
    refetch,
    dataUpdatedAt
  } = useTopExchangers(50); // Increase limit to get more data

  // Force refresh data when component mounts or when coming from admin
  useEffect(() => {
    const shouldRefresh = sessionStorage.getItem('refreshExchangers');
    if (shouldRefresh) {
      sessionStorage.removeItem('refreshExchangers');
      refetch();
    }

    // Listen for real-time updates from admin panel
    const handleDataUpdate = (event: CustomEvent) => {
      console.log('Top exchangers received data update event:', event.detail);
      refetch();
    };

    window.addEventListener('exchangerDataUpdated', handleDataUpdate as EventListener);
    
    return () => {
      window.removeEventListener('exchangerDataUpdated', handleDataUpdate as EventListener);
    };
  }, [refetch]);

  // Manual refresh function
  const handleManualRefresh = async () => {
    setRefreshKey(prev => prev + 1);
    await refetch();
  };

  // Auto-refresh every 30 seconds to catch any updates
  useEffect(() => {
    const interval = setInterval(() => {
      const shouldRefresh = sessionStorage.getItem('refreshExchangers');
      if (shouldRefresh) {
        sessionStorage.removeItem('refreshExchangers');
        refetch();
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [refetch]);

  const allExchangers = topExchangersData?.exchangers || [];

  // Enhanced filtering and sorting with proper error handling
  const filteredExchangers = React.useMemo(() => {
    if (!allExchangers || allExchangers.length === 0) {
      return [];
    }

    return allExchangers
      .filter(exchanger => {
        if (!exchanger) return false;
        
        const nameMatch = exchanger.name?.toLowerCase().includes(searchQuery.toLowerCase()) || false;
        const addressMatch = exchanger.address?.toLowerCase().includes(searchQuery.toLowerCase()) || false;
        
        return nameMatch || addressMatch;
      })
      .sort((a, b) => {
        // Ensure both exchangers have required properties
        if (!a || !b) return 0;
        
        switch (sortBy) {
          case 'rating':
            const aRating = a.rating || 0;
            const bRating = b.rating || 0;
            if (Math.abs(bRating - aRating) > 0.1) {
              return bRating - aRating;
            }
            // If ratings are close, sort by review count
            return (b.reviewCount || 0) - (a.reviewCount || 0);
          case 'reviews':
            const aReviews = a.reviewCount || 0;
            const bReviews = b.reviewCount || 0;
            return bReviews - aReviews;
          case 'rates':
            const aRubRate = a.rates?.find(r => r.currency === 'THB/RUB');
            const bRubRate = b.rates?.find(r => r.currency === 'THB/RUB');
            const aBuy = aRubRate?.buy || 0;
            const bBuy = bRubRate?.buy || 0;
            return bBuy - aBuy;
          default:
            return 0;
        }
      })
      .slice(0, 20); // Limit to top 20
  }, [allExchangers, searchQuery, sortBy]);

  const getDistrictName = (districtId: string) => {
    return districts.find(d => d.id === districtId)?.name || districtId;
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-4 h-4 ${
              i < Math.floor(rating) 
                ? 'text-yellow-400 fill-current' 
                : i < rating 
                  ? 'text-yellow-400 fill-current opacity-50'
                  : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-2 text-sm font-semibold text-gray-700">
          {rating.toFixed(1)}
        </span>
      </div>
    );
  };

  const getBadgeColor = (position: number) => {
    if (position === 1) return 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-white';
    if (position === 2) return 'bg-gradient-to-r from-gray-400 to-gray-500 text-white';
    if (position === 3) return 'bg-gradient-to-r from-orange-400 to-orange-500 text-white';
    return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white';
  };

  const formatCurrencyRate = (rate: number, currency: string) => {
    if (currency.includes('THB/RUB')) {
      return rate.toFixed(3);
    } else if (currency.includes('THB/USDT')) {
      return rate.toFixed(4);
    }
    return rate.toFixed(2);
  };

  const handleExternalClick = (exchanger: Exchanger) => {
    if (exchanger.website) {
      console.log('Website click:', exchanger.name, exchanger.website);
      window.open(exchanger.website, '_blank', 'noopener,noreferrer');
    } else {
      console.warn('Website URL not available for:', exchanger.name);
    }
  };

  const handleTelegramClick = (exchanger: Exchanger) => {
    if (exchanger.telegramBot) {
      console.log('Telegram click:', exchanger.name, exchanger.telegramBot);
      window.open(exchanger.telegramBot, '_blank', 'noopener,noreferrer');
    } else {
      console.warn('Telegram bot URL not available for:', exchanger.name);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-100">
          <div className="flex items-center space-x-3 mb-4">
            <div className="bg-gradient-to-br from-purple-500 to-pink-500 p-3 rounded-lg">
              <Award className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-800">Лучшие обменники валют</h2>
              <p className="text-gray-600">
                Загружаем рейтинг лучших обменных пунктов...
                {allExchangers.length > 0 && (
                  <span className="ml-2 text-sm">({allExchangers.length} найдено)</span>
                )}
              </p>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="bg-white rounded-xl shadow-md border border-gray-200 p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-100">
          <div className="flex items-center space-x-3 mb-4">
            <div className="bg-gradient-to-br from-purple-500 to-pink-500 p-3 rounded-lg">
              <Award className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-800">Лучшие обменники валют</h2>
              <p className="text-gray-600">
                Ошибка загрузки данных: {error.message || 'Неизвестная ошибка'}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl shadow-lg border border-red-100 p-8">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <div className="text-red-600 text-lg mb-4">Не удалось загрузить рейтинг обменников</div>
            <p className="text-gray-600 mb-6">
              Возможные причины: проблемы с сетью, недоступность API, или отсутствие данных
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={() => refetch()}
                disabled={isLoading}
                className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                <span>{isLoading ? 'Загрузка...' : 'Попробовать снова'}</span>
              </button>
              <a
                href="/"
                className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors text-center"
              >
                Вернуться на главную
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-100">
        <div className="flex items-center space-x-3 mb-4">
          <div className="bg-gradient-to-br from-purple-500 to-pink-500 p-3 rounded-lg">
            <Award className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-800">🏆 Лучшие обменники валют</h2>
            <p className="text-gray-600">
              {filteredExchangers.length > 0 
                ? `Рейтинг ${filteredExchangers.length} лучших обменных пунктов с актуальными курсами THB`
                : 'Загружаем данные о лучших обменниках...'
              }
            </p>
            {dataUpdatedAt && (
              <p className="text-sm text-gray-500 mt-1">
                Данные обновлены: {new Date(dataUpdatedAt).toLocaleString('ru-RU')}
              </p>
            )}
          </div>
        </div>

        {/* Sorting and Refresh */}
        <div className="flex flex-wrap gap-3 items-center justify-between">
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => setSortBy('rating')}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                sortBy === 'rating'
                  ? 'bg-purple-600 text-white shadow-md'
                  : 'bg-white text-purple-600 border border-purple-200 hover:bg-purple-50'
              }`}
            >
              По рейтингу ⭐
            </button>
            <button
              onClick={() => setSortBy('reviews')}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                sortBy === 'reviews'
                  ? 'bg-purple-600 text-white shadow-md'
                  : 'bg-white text-purple-600 border border-purple-200 hover:bg-purple-50'
              }`}
            >
              По отзывам 💬
            </button>
            <button
              onClick={() => setSortBy('rates')}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                sortBy === 'rates'
                  ? 'bg-purple-600 text-white shadow-md'
                  : 'bg-white text-purple-600 border border-purple-200 hover:bg-purple-50'
              }`}
            >
              По курсу THB 💱
            </button>
          </div>
          
          {/* Manual refresh button */}
          <button
            onClick={handleManualRefresh}
            disabled={isLoading}
            className="bg-purple-100 hover:bg-purple-200 disabled:bg-gray-100 text-purple-700 px-4 py-2 rounded-lg text-sm flex items-center space-x-2 transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>{isLoading ? 'Обновление...' : 'Обновить'}</span>
          </button>
        </div>
      </div>

      {/* Data Status Indicator */}
      {allExchangers.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-green-800">
              <CheckCircle className="w-5 h-5" />
              <span className="font-medium">
                Загружено {allExchangers.length} обменников
              </span>
            </div>
            <div className="text-sm text-green-700">
              Показано топ-{filteredExchangers.length} по критерию "{
                sortBy === 'rating' ? 'рейтинг' :
                sortBy === 'reviews' ? 'количество отзывов' :
                'курс THB'
              }"
            </div>
          </div>
        </div>
      )}

      {/* Top Exchangers Grid */}
      {filteredExchangers.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredExchangers.map((exchanger, index) => {
            const thbRubRate = exchanger.rates?.find(r => r.currency === 'THB/RUB');
            const usdtBahtRate = exchanger.rates?.find(r => r.currency === 'USDT/BAHT');
            
            return (
              <div
                key={exchanger.id}
                className="relative bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                {/* Position Badge */}
                <div className="absolute top-4 left-4 z-10">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg ${getBadgeColor(index + 1)}`}>
                    {index + 1}
                  </div>
                </div>

                {/* Trust Badge */}
                <div className="absolute top-4 right-4 z-10">
                  {exchanger.rating >= 4.5 && (
                    <div className="bg-gradient-to-r from-amber-400 to-orange-400 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center">
                      <Shield className="w-3 h-3 mr-1" />
                      ТОП
                    </div>
                  )}
                </div>

                {/* Header */}
                <div className="bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 p-4 text-white">
                  <div className="mt-6"> {/* Add margin to avoid overlap with badges */}
                    <h3 className="text-lg font-bold mb-2 leading-tight">{exchanger.name}</h3>
                    <div className="flex items-center space-x-2 text-sm opacity-90">
                      <MapPin className="w-4 h-4" />
                      <span>{getDistrictName(exchanger.district)}</span>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  {/* Rating and Reviews */}
                  <div className="flex items-center justify-between mb-4">
                    {renderStars(exchanger.rating)}
                    <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                      {exchanger.reviewCount} отзывов
                    </span>
                  </div>

                  {/* Address and Contact */}
                  <div className="space-y-2 mb-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4 text-gray-500" />
                      <span className="truncate">{exchanger.address}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4 text-gray-500" />
                      <span>{exchanger.phone}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-gray-500" />
                      <span>{exchanger.hours}</span>
                    </div>
                  </div>

                  {/* Currency Rates */}
                  <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg p-4 mb-4 border border-emerald-200">
                    <h4 className="text-sm font-semibold text-gray-700 mb-3 text-center">Курсы валют</h4>
                    
                    <div className="space-y-3">
                      {/* THB/RUB */}
                      {thbRubRate ? (
                        <div className="bg-white rounded-lg p-3 border border-gray-200">
                          <div className="text-xs font-semibold text-gray-700 mb-2 text-center">THB/RUB</div>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="text-center">
                              <div className="text-sm font-bold text-emerald-600">
                                {formatCurrencyRate(thbRubRate.buy, 'THB/RUB')}
                              </div>
                              <div className="text-xs text-gray-500">Покупка</div>
                            </div>
                            <div className="text-center">
                              <div className="text-sm font-bold text-rose-600">
                                {formatCurrencyRate(thbRubRate.sell, 'THB/RUB')}
                              </div>
                              <div className="text-xs text-gray-500">Продажа</div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="bg-gray-100 rounded-lg p-3 text-center">
                          <div className="text-xs text-gray-500">THB/RUB курс недоступен</div>
                        </div>
                      )}
                      
                      {/* USDT/BAHT */}
                      {usdtBahtRate ? (
                        <div className="bg-white rounded-lg p-3 border border-gray-200">
                          <div className="text-xs font-semibold text-gray-700 mb-2 text-center">USDT/BAHT</div>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="text-center">
                              <div className="text-sm font-bold text-emerald-600">
                                {formatCurrencyRate(usdtBahtRate.buy, 'USDT/BAHT')}
                              </div>
                              <div className="text-xs text-gray-500">Покупка</div>
                            </div>
                            <div className="text-center">
                              <div className="text-sm font-bold text-rose-600">
                                {formatCurrencyRate(usdtBahtRate.sell, 'USDT/BAHT')}
                              </div>
                              <div className="text-xs text-gray-500">Продажа</div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="bg-gray-100 rounded-lg p-3 text-center">
                          <div className="text-xs text-gray-500">USDT/BAHT курс недоступен</div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="grid grid-cols-2 gap-3">
                    {exchanger.website && (
                      <button
                        onClick={() => handleExternalClick(exchanger)}
                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm flex items-center justify-center space-x-1"
                      >
                        <Globe className="w-4 h-4" />
                        <span>Сайт</span>
                      </button>
                    )}
                    {exchanger.telegramBot && (
                      <button
                        onClick={() => handleTelegramClick(exchanger)}
                        className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm flex items-center justify-center space-x-1"
                      >
                        <MessageCircle className="w-4 h-4" />
                        <span>Telegram</span>
                      </button>
                    )}
                    <button
                      onClick={() => onViewReviews?.(exchanger.id)}
                      className="bg-emerald-100 hover:bg-emerald-200 text-emerald-700 px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm flex items-center justify-center space-x-1 cursor-pointer"
                      title="Посмотреть отзывы об этом обменнике"
                    >
                      <MessageCircle className="w-4 h-4" />
                      <span>Отзывы</span>
                    </button>
                    <button
                      onClick={() => window.open(`tel:${exchanger.phone}`, '_self')}
                      className="bg-emerald-100 hover:bg-emerald-200 text-emerald-700 px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm flex items-center justify-center space-x-1"
                    >
                      <Phone className="w-4 h-4" />
                      <span>Позвонить</span>
                    </button>
                  </div>

                  {/* Trust Indicators */}
                  <div className="mt-4 pt-3 border-t border-gray-100">
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Shield className="w-3 h-3 text-green-500" />
                        <span>Проверен</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="w-3 h-3 text-blue-500" />
                        <span>Лицензия</span>
                      </div>
                      <div className="text-gray-400">
                        #{index + 1} в рейтинге
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12 bg-white rounded-xl shadow-lg border border-gray-100">
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <div className="text-gray-600 text-lg">Загрузка рейтинга обменников...</div>
            </>
          ) : allExchangers.length === 0 ? (
            <>
              <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <div className="text-gray-500 text-lg mb-2">Данные о рейтинге временно недоступны</div>
              <p className="text-gray-400 mb-6">
                Обменники могут быть недоступны из-за технических работ или отсутствия данных
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  onClick={handleManualRefresh}
                  disabled={isLoading}
                  className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                >
                  <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                  <span>Обновить данные</span>
                </button>
                <a
                  href="/"
                  className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors text-center"
                >
                  Посмотреть все обменники
                </a>
              </div>
            </>
          ) : (
            <>
              <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <div className="text-gray-500 text-lg">Обменники не найдены</div>
              <p className="text-gray-400 mt-2">
                Попробуйте изменить параметры поиска или{' '}
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setSortBy('rating');
                  }}
                  className="text-purple-600 hover:text-purple-800 underline"
                >
                  сбросить фильтры
                </button>
              </p>
            </>
          )}
        </div>
      )}

      {/* Debug Information (only in development) */}
      {import.meta.env.DEV && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-sm text-gray-600">
          <details>
            <summary className="cursor-pointer font-medium">Debug Information</summary>
            <div className="mt-2 space-y-1">
              <div>Total exchangers from API: {allExchangers.length}</div>
              <div>Filtered exchangers: {filteredExchangers.length}</div>
              <div>Search query: "{searchQuery}"</div>
              <div>Sort by: {sortBy}</div>
              <div>Last data update: {dataUpdatedAt ? new Date(dataUpdatedAt).toLocaleString() : 'Never'}</div>
              <div>Refresh key: {refreshKey}</div>
              <div>Data source: {topExchangersData?.source || 'unknown'}</div>
            </div>
          </details>
        </div>
      )}
    </div>
  );
};

export default TopExchangers;