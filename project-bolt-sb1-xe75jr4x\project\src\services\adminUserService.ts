// Admin user management service - handles users from all sources
import { config } from '../config/environment';
import { supabaseService } from './supabaseClient';

export interface AdminUser {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  role: 'admin' | 'moderator' | 'user';
  isActive: boolean;
  createdAt: string;
  lastLogin?: string;
  registrationSource: 'supabase' | 'backend' | 'fallback';
  username?: string; // For compatibility with existing admin panel
}

export interface UserFilters {
  search?: string;
  role?: string;
  status?: string;
  page?: number;
  limit?: number;
}

export interface UserListResponse {
  users: AdminUser[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  usersByRole: {
    admin: number;
    moderator: number;
    user: number;
  };
  recentRegistrations: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
  registrationSources: {
    supabase: number;
    backend: number;
    fallback: number;
  };
}

class AdminUserService {
  private static instance: AdminUserService;

  public static getInstance(): AdminUserService {
    if (!AdminUserService.instance) {
      AdminUserService.instance = new AdminUserService();
    }
    return AdminUserService.instance;
  }

  // Get users from localStorage (fallback storage)
  private getFallbackUsers(): AdminUser[] {
    try {
      const registeredUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
      const fallbackUsers = JSON.parse(localStorage.getItem('fallback_users') || '[]');
      
      // Convert registered users to admin format
      const convertedRegistered = registeredUsers.map((user: any) => ({
        id: user.id || `fallback-${Date.now()}-${Math.random()}`,
        email: user.email,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        phone: user.phone || '',
        role: user.role || 'user',
        isActive: user.isActive !== false,
        createdAt: user.createdAt || new Date().toISOString(),
        lastLogin: user.lastLogin,
        registrationSource: 'fallback' as const,
        username: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email.split('@')[0]
      }));

      // Convert fallback users to admin format
      const convertedFallback = fallbackUsers.map((user: any) => ({
        id: user.id || `fallback-${Date.now()}-${Math.random()}`,
        email: user.email,
        firstName: user.firstName || user.name?.split(' ')[0] || '',
        lastName: user.lastName || user.name?.split(' ').slice(1).join(' ') || '',
        phone: user.phone || '',
        role: user.role || 'user',
        isActive: user.isActive !== false,
        createdAt: user.createdAt || new Date().toISOString(),
        lastLogin: user.lastLogin,
        registrationSource: 'fallback' as const,
        username: user.name || `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email.split('@')[0]
      }));

      // Combine and deduplicate by email
      const allUsers = [...convertedRegistered, ...convertedFallback];
      const uniqueUsers = allUsers.filter((user, index, self) => 
        index === self.findIndex(u => u.email === user.email)
      );

      return uniqueUsers;
    } catch (error) {
      console.error('Error getting fallback users:', error);
      return [];
    }
  }

  // Get users from Supabase
  private async getSupabaseUsers(): Promise<AdminUser[]> {
    try {
      if (!supabaseService.isConnected || !supabaseService.client) {
        return [];
      }

      const { data: users, error } = await supabaseService.client
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching Supabase users:', error);
        return [];
      }

      return (users || []).map(user => ({
        id: user.id,
        email: user.email,
        firstName: user.first_name || '',
        lastName: user.last_name || '',
        phone: user.phone || '',
        role: user.role || 'user',
        isActive: user.is_active !== false,
        createdAt: user.created_at,
        lastLogin: user.last_login,
        registrationSource: 'supabase' as const,
        username: `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.email.split('@')[0]
      }));
    } catch (error) {
      console.error('Error getting Supabase users:', error);
      return [];
    }
  }

  // Get users from backend API
  private async getBackendUsers(): Promise<AdminUser[]> {
    try {
      const response = await fetch(`${config.API_BASE_URL}/users?limit=1000`);
      if (!response.ok) {
        return [];
      }

      const result = await response.json();
      if (!result.success || !result.data?.users) {
        return [];
      }

      return result.data.users.map((user: any) => ({
        id: user.id,
        email: user.email,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        phone: user.phone || '',
        role: user.role || 'user',
        isActive: user.isActive !== false,
        createdAt: user.createdAt,
        lastLogin: user.lastLogin,
        registrationSource: 'backend' as const,
        username: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email.split('@')[0]
      }));
    } catch (error) {
      console.error('Error getting backend users:', error);
      return [];
    }
  }

  // Get all users from all sources
  public async getAllUsers(): Promise<AdminUser[]> {
    try {
      const [supabaseUsers, backendUsers, fallbackUsers] = await Promise.allSettled([
        this.getSupabaseUsers(),
        this.getBackendUsers(),
        Promise.resolve(this.getFallbackUsers())
      ]);

      const allUsers: AdminUser[] = [];

      // Add users from each source
      if (supabaseUsers.status === 'fulfilled') {
        allUsers.push(...supabaseUsers.value);
      }
      if (backendUsers.status === 'fulfilled') {
        allUsers.push(...backendUsers.value);
      }
      if (fallbackUsers.status === 'fulfilled') {
        allUsers.push(...fallbackUsers.value);
      }

      // Deduplicate by email (prefer Supabase > Backend > Fallback)
      const uniqueUsers = allUsers.reduce((acc: AdminUser[], user) => {
        const existingIndex = acc.findIndex(u => u.email === user.email);
        if (existingIndex === -1) {
          acc.push(user);
        } else {
          // Keep the user from the preferred source
          const existing = acc[existingIndex];
          const sourcePriority = { supabase: 3, backend: 2, fallback: 1 };
          if (sourcePriority[user.registrationSource] > sourcePriority[existing.registrationSource]) {
            acc[existingIndex] = user;
          }
        }
        return acc;
      }, []);

      // Sort by creation date (newest first)
      return uniqueUsers.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    } catch (error) {
      console.error('Error getting all users:', error);
      return [];
    }
  }

  // Get users with filtering and pagination
  public async getUsers(filters: UserFilters = {}): Promise<UserListResponse> {
    try {
      const allUsers = await this.getAllUsers();
      let filteredUsers = allUsers;

      // Apply search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredUsers = filteredUsers.filter(user =>
          user.email.toLowerCase().includes(searchLower) ||
          user.firstName?.toLowerCase().includes(searchLower) ||
          user.lastName?.toLowerCase().includes(searchLower) ||
          user.username?.toLowerCase().includes(searchLower)
        );
      }

      // Apply role filter
      if (filters.role && filters.role !== 'all') {
        filteredUsers = filteredUsers.filter(user => user.role === filters.role);
      }

      // Apply status filter
      if (filters.status && filters.status !== 'all') {
        const isActive = filters.status === 'active';
        filteredUsers = filteredUsers.filter(user => user.isActive === isActive);
      }

      // Apply pagination
      const page = filters.page || 1;
      const limit = filters.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

      return {
        users: paginatedUsers,
        totalCount: filteredUsers.length,
        totalPages: Math.ceil(filteredUsers.length / limit),
        currentPage: page
      };
    } catch (error) {
      console.error('Error getting filtered users:', error);
      return {
        users: [],
        totalCount: 0,
        totalPages: 1,
        currentPage: 1
      };
    }
  }

  // Update user in localStorage (fallback storage)
  public async updateUser(userId: string, updates: Partial<AdminUser>): Promise<boolean> {
    try {
      // Update in registered_users
      const registeredUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
      const registeredIndex = registeredUsers.findIndex((user: any) => user.id === userId);
      
      if (registeredIndex !== -1) {
        registeredUsers[registeredIndex] = { ...registeredUsers[registeredIndex], ...updates };
        localStorage.setItem('registered_users', JSON.stringify(registeredUsers));
        return true;
      }

      // Update in fallback_users
      const fallbackUsers = JSON.parse(localStorage.getItem('fallback_users') || '[]');
      const fallbackIndex = fallbackUsers.findIndex((user: any) => user.id === userId);
      
      if (fallbackIndex !== -1) {
        fallbackUsers[fallbackIndex] = { ...fallbackUsers[fallbackIndex], ...updates };
        localStorage.setItem('fallback_users', JSON.stringify(fallbackUsers));
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error updating user:', error);
      return false;
    }
  }

  // Delete user from localStorage (fallback storage)
  public async deleteUser(userId: string): Promise<boolean> {
    try {
      // Remove from registered_users
      const registeredUsers = JSON.parse(localStorage.getItem('registered_users') || '[]');
      const filteredRegistered = registeredUsers.filter((user: any) => user.id !== userId);
      localStorage.setItem('registered_users', JSON.stringify(filteredRegistered));

      // Remove from fallback_users
      const fallbackUsers = JSON.parse(localStorage.getItem('fallback_users') || '[]');
      const filteredFallback = fallbackUsers.filter((user: any) => user.id !== userId);
      localStorage.setItem('fallback_users', JSON.stringify(filteredFallback));

      return true;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }

  // Get user statistics
  public async getUserStats(): Promise<UserStats> {
    try {
      const allUsers = await this.getAllUsers();
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const stats: UserStats = {
        totalUsers: allUsers.length,
        activeUsers: allUsers.filter(u => u.isActive).length,
        inactiveUsers: allUsers.filter(u => !u.isActive).length,
        usersByRole: {
          admin: allUsers.filter(u => u.role === 'admin').length,
          moderator: allUsers.filter(u => u.role === 'moderator').length,
          user: allUsers.filter(u => u.role === 'user').length,
        },
        recentRegistrations: {
          today: allUsers.filter(u => new Date(u.createdAt) >= today).length,
          thisWeek: allUsers.filter(u => new Date(u.createdAt) >= thisWeek).length,
          thisMonth: allUsers.filter(u => new Date(u.createdAt) >= thisMonth).length,
        },
        registrationSources: {
          supabase: allUsers.filter(u => u.registrationSource === 'supabase').length,
          backend: allUsers.filter(u => u.registrationSource === 'backend').length,
          fallback: allUsers.filter(u => u.registrationSource === 'fallback').length,
        }
      };

      return stats;
    } catch (error) {
      console.error('Error getting user stats:', error);
      return {
        totalUsers: 0,
        activeUsers: 0,
        inactiveUsers: 0,
        usersByRole: { admin: 0, moderator: 0, user: 0 },
        recentRegistrations: { today: 0, thisWeek: 0, thisMonth: 0 },
        registrationSources: { supabase: 0, backend: 0, fallback: 0 }
      };
    }
  }
}

export const adminUserService = AdminUserService.getInstance();
export default adminUserService;
