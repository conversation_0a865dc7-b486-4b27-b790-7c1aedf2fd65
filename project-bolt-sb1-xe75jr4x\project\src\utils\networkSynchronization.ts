/**
 * Network Synchronization Utility
 * Handles automatic rate synchronization across exchanger networks
 */

export interface NetworkSyncResult {
  success: boolean;
  syncedCount: number;
  networkMembers: string[];
  errors: string[];
}

export interface ExchangerData {
  id: string;
  name: string;
  websiteUrl?: string;
  networkId?: string;
  rates?: any[];
  [key: string]: any;
}

export class NetworkSynchronizer {
  private syncCache = new Map<string, { timestamp: Date; members: string[] }>();
  private readonly SYNC_COOLDOWN_MS = 5 * 60 * 1000; // 5 minutes (configurable)

  /**
   * Get sync cooldown from settings
   */
  private getSyncCooldownMs(): number {
    try {
      // Try to get from settings
      const settingsAPI = (window as any).systemSettings;
      if (settingsAPI) {
        const cooldownMinutes = settingsAPI.getTyped('network_sync_cooldown', 5);
        return cooldownMinutes * 60 * 1000;
      }
    } catch (error) {
      console.warn('Could not load sync cooldown from settings, using default:', error);
    }
    return this.SYNC_COOLDOWN_MS;
  }

  /**
   * Check if network sync is enabled
   */
  private isNetworkSyncEnabled(): boolean {
    try {
      const settingsAPI = (window as any).systemSettings;
      if (settingsAPI) {
        return settingsAPI.getTyped('auto_sync_networks', true);
      }
    } catch (error) {
      console.warn('Could not load network sync setting, using default:', error);
    }
    return true; // Default to enabled
  }

  /**
   * Synchronize rates across all network members
   */
  public async synchronizeNetworkRates(
    sourceExchanger: ExchangerData,
    rates: any[],
    allExchangers: ExchangerData[],
    updateFunction: (id: string, data: any) => Promise<any>
  ): Promise<NetworkSyncResult> {
    const result: NetworkSyncResult = {
      success: false,
      syncedCount: 0,
      networkMembers: [],
      errors: []
    };

    try {
      // Check if network sync is enabled
      if (!this.isNetworkSyncEnabled()) {
        console.log('Network synchronization is disabled in settings');
        return { ...result, success: true };
      }

      // Check sync cooldown
      if (this.isInSyncCooldown(sourceExchanger.id)) {
        console.log(`Skipping network sync for ${sourceExchanger.name} - in cooldown period`);
        return { ...result, success: true };
      }

      // Identify network members
      const networkMembers = this.identifyNetworkMembers(sourceExchanger, allExchangers);
      result.networkMembers = networkMembers.map(e => e.id);

      if (networkMembers.length <= 1) {
        console.log(`No network members found for ${sourceExchanger.name}`);
        return { ...result, success: true };
      }

      console.log(`Synchronizing rates across ${networkMembers.length} network members for ${sourceExchanger.name}`);

      // Sync rates to all network members
      for (const member of networkMembers) {
        if (member.id !== sourceExchanger.id) {
          try {
            await updateFunction(member.id, {
              rates: rates,
              lastUpdated: new Date().toISOString(),
              syncedFrom: sourceExchanger.id,
              syncedAt: new Date().toISOString()
            });
            result.syncedCount++;
            console.log(`Synchronized rates to ${member.name}`);
          } catch (error) {
            const errorMsg = `Failed to sync rates to ${member.name}: ${error}`;
            result.errors.push(errorMsg);
            console.error(errorMsg);
          }
        }
      }

      // Update sync cache
      this.updateSyncCache(sourceExchanger.id, result.networkMembers);

      result.success = true;
      console.log(`Successfully synchronized rates to ${result.syncedCount} network members`);

    } catch (error) {
      const errorMsg = `Error in network synchronization: ${error}`;
      result.errors.push(errorMsg);
      console.error(errorMsg);
    }

    return result;
  }

  /**
   * Identify all exchangers belonging to the same network
   */
  private identifyNetworkMembers(sourceExchanger: ExchangerData, allExchangers: ExchangerData[]): ExchangerData[] {
    const networkMembers: ExchangerData[] = [];
    const seenIds = new Set<string>();

    // Method 1: Match by base company name
    if (sourceExchanger.name) {
      const baseName = this.extractBaseName(sourceExchanger.name);
      const nameMatches = allExchangers.filter(exchanger => {
        if (!exchanger.name || seenIds.has(exchanger.id)) return false;
        const exchangerBaseName = this.extractBaseName(exchanger.name);
        return exchangerBaseName.toLowerCase().includes(baseName.toLowerCase()) ||
               baseName.toLowerCase().includes(exchangerBaseName.toLowerCase());
      });
      
      nameMatches.forEach(match => {
        if (!seenIds.has(match.id)) {
          networkMembers.push(match);
          seenIds.add(match.id);
        }
      });
    }

    // Method 2: Match by website domain
    if (sourceExchanger.websiteUrl) {
      const domain = this.extractDomain(sourceExchanger.websiteUrl);
      if (domain) {
        const domainMatches = allExchangers.filter(exchanger => {
          if (!exchanger.websiteUrl || seenIds.has(exchanger.id)) return false;
          const exchangerDomain = this.extractDomain(exchanger.websiteUrl);
          return exchangerDomain === domain;
        });

        domainMatches.forEach(match => {
          if (!seenIds.has(match.id)) {
            networkMembers.push(match);
            seenIds.add(match.id);
          }
        });
      }
    }

    // Method 3: Match by explicit network identifier
    if (sourceExchanger.networkId) {
      const networkMatches = allExchangers.filter(exchanger => {
        return exchanger.networkId === sourceExchanger.networkId && !seenIds.has(exchanger.id);
      });

      networkMatches.forEach(match => {
        if (!seenIds.has(match.id)) {
          networkMembers.push(match);
          seenIds.add(match.id);
        }
      });
    }

    // Always include the source exchanger
    if (!seenIds.has(sourceExchanger.id)) {
      networkMembers.push(sourceExchanger);
    }

    return networkMembers;
  }

  /**
   * Extract base name for network matching
   */
  private extractBaseName(name: string): string {
    // Remove common suffixes and location indicators
    let baseName = name.replace(/\s*(branch|филиал|отделение|\d+|центр|center|офис|office)\s*$/gi, '');
    baseName = baseName.replace(/\s*\([^)]*\)\s*/g, ''); // Remove parentheses
    baseName = baseName.replace(/\s*-\s*[^-]*$/g, ''); // Remove location after dash
    return baseName.trim();
  }

  /**
   * Extract domain from URL for network matching
   */
  private extractDomain(url: string): string | null {
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
      return urlObj.hostname.toLowerCase();
    } catch {
      return null;
    }
  }

  /**
   * Check if exchanger is in sync cooldown period
   */
  private isInSyncCooldown(exchangerId: string): boolean {
    const cacheEntry = this.syncCache.get(exchangerId);
    if (!cacheEntry) return false;

    const timeSinceLastSync = Date.now() - cacheEntry.timestamp.getTime();
    const cooldownMs = this.getSyncCooldownMs();
    return timeSinceLastSync < cooldownMs;
  }

  /**
   * Update sync cache
   */
  private updateSyncCache(exchangerId: string, networkMembers: string[]): void {
    this.syncCache.set(exchangerId, {
      timestamp: new Date(),
      members: networkMembers
    });

    // Clean up old cache entries (older than 1 hour)
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    for (const [id, entry] of this.syncCache.entries()) {
      if (entry.timestamp.getTime() < oneHourAgo) {
        this.syncCache.delete(id);
      }
    }
  }

  /**
   * Get network members for an exchanger (for debugging/display)
   */
  public getNetworkMembers(exchanger: ExchangerData, allExchangers: ExchangerData[]): ExchangerData[] {
    return this.identifyNetworkMembers(exchanger, allExchangers);
  }
}

// Global instance
export const networkSynchronizer = new NetworkSynchronizer();
