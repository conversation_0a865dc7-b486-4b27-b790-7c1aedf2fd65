<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Диагностика исторических курсов</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .warning { border-left: 4px solid #ffc107; background: #fff3cd; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .status { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status.ok { background: #28a745; color: white; }
        .status.error { background: #dc3545; color: white; }
        .status.loading { background: #ffc107; color: black; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .metric { text-align: center; padding: 15px; background: #f8f9fa; border-radius: 4px; margin: 10px 0; }
        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .metric-label { font-size: 14px; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Диагностика исторических курсов валют</h1>
        
        <div class="card info">
            <h2>📋 Статус системы</h2>
            <div class="grid">
                <div class="metric">
                    <div class="metric-value" id="backend-status">Проверка...</div>
                    <div class="metric-label">Backend API</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="frontend-status">Проверка...</div>
                    <div class="metric-label">Frontend</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="api-response-time">-</div>
                    <div class="metric-label">Время ответа API (мс)</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="data-count">-</div>
                    <div class="metric-label">Записей в API</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>🧪 Тесты API</h2>
            <div style="margin-bottom: 15px;">
                <button onclick="testBasicAPI()">Базовый тест API</button>
                <button onclick="testWithFilters()">Тест с фильтрами</button>
                <button onclick="testCurrencyPairs()">Тест валютных пар</button>
                <button onclick="testExchangers()">Тест обменников</button>
                <button onclick="runAllTests()">Запустить все тесты</button>
                <button onclick="clearResults()">Очистить результаты</button>
            </div>
        </div>

        <div id="test-results"></div>

        <div class="card">
            <h2>🔍 Детальная информация</h2>
            <div id="detailed-info">
                <p>Нажмите на любой тест выше, чтобы увидеть детальную информацию...</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        let testResults = [];

        // Проверка статуса при загрузке
        window.addEventListener('load', async () => {
            await checkSystemStatus();
        });

        async function checkSystemStatus() {
            // Проверка backend
            try {
                const start = Date.now();
                const response = await fetch(`${API_BASE_URL}/parsing/historical-rates?limit=1`);
                const responseTime = Date.now() - start;
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('backend-status').textContent = '✅ Работает';
                    document.getElementById('backend-status').className = 'metric-value';
                    document.getElementById('api-response-time').textContent = responseTime;
                    document.getElementById('data-count').textContent = data.data?.total_count || 0;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('backend-status').textContent = '❌ Ошибка';
                document.getElementById('backend-status').style.color = '#dc3545';
                console.error('Backend error:', error);
            }

            // Проверка frontend
            document.getElementById('frontend-status').textContent = '✅ Работает';
        }

        function logResult(test, status, message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const result = { test, status, message, data, timestamp };
            testResults.push(result);
            
            const resultsDiv = document.getElementById('test-results');
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'warning';
            
            resultsDiv.innerHTML += `
                <div class="card ${statusClass}">
                    <h3>${test}</h3>
                    <p><strong>[${timestamp}]</strong> ${message}</p>
                    ${data ? `<details><summary>Данные ответа</summary><pre>${JSON.stringify(data, null, 2)}</pre></details>` : ''}
                </div>
            `;
            
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function testBasicAPI() {
            try {
                logResult('Базовый тест API', 'info', 'Отправка запроса...');
                
                const response = await fetch(`${API_BASE_URL}/parsing/historical-rates?limit=5&days_back=7`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    logResult('Базовый тест API', 'success', 
                        `✅ Успешно получено ${data.data.rates.length} записей из ${data.data.total_count} общих`, 
                        data);
                } else {
                    logResult('Базовый тест API', 'error', 
                        `❌ Ошибка: ${response.status} ${response.statusText}`, 
                        data);
                }
            } catch (error) {
                logResult('Базовый тест API', 'error', `❌ Исключение: ${error.message}`);
            }
        }

        async function testWithFilters() {
            try {
                logResult('Тест с фильтрами', 'info', 'Тестирование фильтрации по валютной паре...');
                
                const response = await fetch(`${API_BASE_URL}/parsing/historical-rates?currency_pair=RUB/THB&limit=5&days_back=7`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const rubThbRates = data.data.rates.filter(r => r.currency_pair === 'RUB/THB');
                    logResult('Тест с фильтрами', 'success', 
                        `✅ Фильтрация работает: ${rubThbRates.length} записей RUB/THB из ${data.data.rates.length} общих`, 
                        data);
                } else {
                    logResult('Тест с фильтрами', 'error', 
                        `❌ Ошибка фильтрации: ${response.status}`, 
                        data);
                }
            } catch (error) {
                logResult('Тест с фильтрами', 'error', `❌ Исключение: ${error.message}`);
            }
        }

        async function testCurrencyPairs() {
            try {
                logResult('Тест валютных пар', 'info', 'Проверка доступных валютных пар...');
                
                const response = await fetch(`${API_BASE_URL}/parsing/historical-rates?limit=1&days_back=30`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const pairs = data.data.available_filters?.currency_pairs || [];
                    logResult('Тест валютных пар', 'success', 
                        `✅ Найдено ${pairs.length} валютных пар: ${pairs.map(p => p.currency_pair).join(', ')}`, 
                        { currency_pairs: pairs });
                } else {
                    logResult('Тест валютных пар', 'error', 
                        `❌ Ошибка получения валютных пар: ${response.status}`, 
                        data);
                }
            } catch (error) {
                logResult('Тест валютных пар', 'error', `❌ Исключение: ${error.message}`);
            }
        }

        async function testExchangers() {
            try {
                logResult('Тест обменников', 'info', 'Проверка доступных обменников...');
                
                const response = await fetch(`${API_BASE_URL}/parsing/historical-rates?limit=1&days_back=30`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const exchangers = data.data.available_filters?.exchangers || [];
                    logResult('Тест обменников', 'success', 
                        `✅ Найдено ${exchangers.length} обменников: ${exchangers.map(e => e.exchanger_name).join(', ')}`, 
                        { exchangers: exchangers });
                } else {
                    logResult('Тест обменников', 'error', 
                        `❌ Ошибка получения обменников: ${response.status}`, 
                        data);
                }
            } catch (error) {
                logResult('Тест обменников', 'error', `❌ Исключение: ${error.message}`);
            }
        }

        async function runAllTests() {
            clearResults();
            logResult('Комплексное тестирование', 'info', '🚀 Запуск всех тестов...');
            
            await testBasicAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testWithFilters();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testCurrencyPairs();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testExchangers();
            
            logResult('Комплексное тестирование', 'success', '🎉 Все тесты завершены!');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            testResults = [];
        }

        // Обновление детальной информации
        setInterval(async () => {
            const detailedInfo = document.getElementById('detailed-info');
            const now = new Date().toLocaleString('ru-RU');
            
            detailedInfo.innerHTML = `
                <h3>📊 Текущее состояние</h3>
                <p><strong>Время:</strong> ${now}</p>
                <p><strong>API URL:</strong> ${API_BASE_URL}</p>
                <p><strong>Выполнено тестов:</strong> ${testResults.length}</p>
                <p><strong>Успешных:</strong> ${testResults.filter(r => r.status === 'success').length}</p>
                <p><strong>Ошибок:</strong> ${testResults.filter(r => r.status === 'error').length}</p>
            `;
        }, 5000);
    </script>
</body>
</html>
