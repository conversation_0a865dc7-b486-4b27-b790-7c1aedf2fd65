import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Users, MessageCircle, Star, Download, Calendar, Activity, Eye, DollarSign } from 'lucide-react';
import { DashboardStats, ActivityLog } from '../../types/admin';
import { useSystemStatus } from '../../hooks/useExchangeData';
import { adminAPI } from '../../data/adminData'; // Keep for mock data

const Analytics: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'day' | 'week' | 'month' | 'year'>('month');

  // Get real system status
  const { data: systemStatus, isLoading: statusLoading } = useSystemStatus();

  useEffect(() => {
    loadDashboardData();
  }, [selectedPeriod]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Load real dashboard data with enhanced error handling
      let dashboardData;
      let logsData;
      
      try {
        dashboardData = await adminAPI.getDashboardStats();
        logsData = await adminAPI.getActivityLogs(1, 10);
      } catch (apiError) {
        console.warn('Dashboard API failed, using fallback calculation:', apiError);
        
        // Calculate stats manually as fallback
        const { getActiveExchangersCount } = await import('../../data/adminData');
        const activeCount = getActiveExchangersCount();
        
        dashboardData = {
          totalExchangers: activeCount,
          activeExchangers: activeCount,
          inactiveExchangers: 0,
          pendingExchangers: 0,
          totalUsers: 0,
          activeUsers: 0,
          totalReviews: 0,
          pendingReviews: 0,
          approvedReviews: 0,
          rejectedReviews: 0,
          totalRates: activeCount * 3, // Estimate 3 rates per exchanger
          todayVisits: Math.floor(activeCount * 10),
          monthlyGrowth: Math.floor(activeCount / 10),
          topExchangers: [],
          lastUpdate: new Date().toISOString(),
          dataSource: 'calculated_fallback'
        };
        
        logsData = { logs: [] };
      }
      
      setStats({
        totalUsers: dashboardData.totalUsers || 0,
        activeUsers: dashboardData.activeUsers || 0,
        totalExchangers: dashboardData.totalExchangers || 0,
        activeExchangers: dashboardData.activeExchangers || 0,
        inactiveExchangers: dashboardData.inactiveExchangers || 0,
        pendingExchangers: dashboardData.pendingExchangers || 0,
        totalReviews: dashboardData.totalReviews || 0,
        pendingReviews: dashboardData.pendingReviews || 0,
        approvedReviews: dashboardData.approvedReviews || 0,
        rejectedReviews: dashboardData.rejectedReviews || 0,
        todayVisits: dashboardData.todayVisits || 0,
        monthlyGrowth: dashboardData.monthlyGrowth || 0,
        topExchangers: dashboardData.topExchangers || [],
        totalRates: dashboardData.totalRates || 0,
        recentActivity: logsData.logs || [],
        lastCalculated: new Date().toISOString(),
        dataSource: dashboardData.dataSource || 'api'
      });
      
      setActivityLogs(logsData.logs || []);
      
      console.log('Analytics data loaded:', {
        source: dashboardData.dataSource,
        exchangersCount: dashboardData.totalExchangers,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      
      // Emergency fallback data
      setStats({
        totalUsers: 0,
        activeUsers: 0,
        totalExchangers: 0,
        activeExchangers: 0,
        inactiveExchangers: 0,
        pendingExchangers: 0,
        totalReviews: 0,
        pendingReviews: 0,
        approvedReviews: 0,
        rejectedReviews: 0,
        todayVisits: 0,
        monthlyGrowth: 0,
        totalRates: 0,
        topExchangers: [],
        recentActivity: [],
        dataSource: 'emergency_fallback',
        error: 'Failed to load analytics data'
      });
    } finally {
      setLoading(false);
    }
  };

  // Show system status info if available
  const renderSystemStatus = () => {
    if (statusLoading) {
      return (
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-6">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-blue-800 font-medium">Проверка состояния системы...</span>
          </div>
        </div>
      );
    }

    if (systemStatus) {
      return (
        <div className="bg-green-50 p-4 rounded-lg border border-green-200 mb-6">
          <h4 className="text-green-800 font-semibold mb-2">Состояние системы</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-green-700">Google Sheets: </span>
              <span className={systemStatus.google_sheets?.success ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                {systemStatus.google_sheets?.success ? 'Подключено' : 'Ошибка'}
              </span>
            </div>
            <div>
              <span className="text-green-700">Кэш: </span>
              <span className={systemStatus.cache?.exists ? 'text-green-600 font-medium' : 'text-yellow-600 font-medium'}>
                {systemStatus.cache?.exists ? 'Активен' : 'Пустой'}
              </span>
            </div>
            <div>
              <span className="text-green-700">Обменников: </span>
              <span className="text-green-600 font-medium">{systemStatus.total_exchangers || 0}</span>
            </div>
          </div>
          {systemStatus.last_update && (
            <div className="mt-2 text-xs text-green-600">
              Последнее обновление: {new Date(systemStatus.last_update).toLocaleString('ru-RU')}
            </div>
          )}
        </div>
      );
    }

    return null;
  };

  const exportReport = () => {
    if (!stats) return;
    
    const reportData = {
      period: selectedPeriod,
      generatedAt: new Date().toISOString(),
      generatedBy: 'admin', // В реальном приложении получать из контекста
      stats: {
        totalUsers: stats.totalUsers,
        activeUsers: stats.activeUsers,
        totalExchangers: stats.totalExchangers,
        totalReviews: stats.totalReviews,
        pendingReviews: stats.pendingReviews,
        todayVisits: stats.todayVisits,
        monthlyGrowth: stats.monthlyGrowth
      },
      topExchangers: stats.topExchangers,
      recentActivity: stats.recentActivity,
      systemStatus: systemStatus
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analytics-report-${selectedPeriod}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getActivityIcon = (action: string) => {
    if (action.includes('review')) return <MessageCircle className="w-4 h-4" />;
    if (action.includes('user')) return <Users className="w-4 h-4" />;
    if (action.includes('settings')) return <BarChart3 className="w-4 h-4" />;
    return <Activity className="w-4 h-4" />;
  };

  const getActivityColor = (action: string) => {
    if (action.includes('approved')) return 'text-green-600';
    if (action.includes('rejected') || action.includes('banned')) return 'text-red-600';
    if (action.includes('created')) return 'text-blue-600';
    return 'text-gray-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="text-gray-600 ml-3">Загрузка аналитики...</p>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-8">
        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">Не удалось загрузить данные аналитики</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {renderSystemStatus()}
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BarChart3 className="w-6 h-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-800">Аналитика и отчеты</h2>
        </div>
        <div className="flex items-center space-x-3">
          <div className="text-sm text-gray-600">
            Период: <strong>{selectedPeriod === 'day' ? 'День' : selectedPeriod === 'week' ? 'Неделя' : selectedPeriod === 'month' ? 'Месяц' : 'Год'}</strong>
          </div>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 bg-white"
          >
            <option value="day">За день</option>
            <option value="week">За неделю</option>
            <option value="month">За месяц</option>
            <option value="year">За год</option>
          </select>
          <button
            onClick={exportReport}
            disabled={!stats}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Download className="w-4 h-4" />
            <span>Экспорт</span>
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow border border-gray-200 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Всего пользователей</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalUsers.toLocaleString()}</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <TrendingUp className="w-3 h-3 mr-1" />
                +{stats.monthlyGrowth}% за месяц
              </p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            Активных: {stats.activeUsers} ({((stats.activeUsers / Math.max(stats.totalUsers, 1)) * 100).toFixed(1)}%)
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border border-gray-200 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Активные пользователи</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeUsers.toLocaleString()}</p>
              <p className="text-sm text-gray-500 mt-1">
                {stats.totalUsers > 0 
                  ? `${((stats.activeUsers / stats.totalUsers) * 100).toFixed(1)}% от общего числа`
                  : 'Нет данных о пользователях'
                }
              </p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <Activity className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border border-gray-200 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Всего отзывов</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalReviews.toLocaleString()}</p>
              <p className="text-sm text-yellow-600 mt-1">
                {stats.pendingReviews > 0 
                  ? `${stats.pendingReviews} на модерации`
                  : 'Нет отзывов на модерации'
                }
              </p>
            </div>
            <div className="bg-yellow-100 p-3 rounded-full">
              <MessageCircle className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            Одобрено: {stats.approvedReviews} • Отклонено: {stats.rejectedReviews}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border border-gray-200 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Посещений сегодня</p>
              <p className="text-2xl font-bold text-gray-900">{stats.todayVisits.toLocaleString()}</p>
              <p className="text-sm text-blue-600 flex items-center mt-1">
                <Eye className="w-3 h-3 mr-1" />
                Уникальные посетители
              </p>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <Eye className="w-6 h-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            {stats.dataSource && `Источник: ${stats.dataSource}`}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Exchangers */}
        <div className="bg-white rounded-lg shadow border border-gray-200 hover:shadow-md transition-shadow">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center">
              <Star className="w-5 h-5 text-yellow-500 mr-2" />
              Топ обменников
            </h3>
            <p className="text-sm text-gray-500 mt-1">По рейтингу и количеству отзывов</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {stats.topExchangers.map((exchanger, index) => (
                <div key={exchanger.id} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                      index === 0 ? 'bg-yellow-500' : 
                      index === 1 ? 'bg-gray-400' : 
                      index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">{exchanger.name}</p>
                      <p className="text-sm text-gray-500">{exchanger.reviewCount} отзывов</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="font-semibold text-gray-800">{exchanger.rating.toFixed(1)}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow border border-gray-200 hover:shadow-md transition-shadow">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center">
              <Activity className="w-5 h-5 text-blue-500 mr-2" />
              Последняя активность
            </h3>
            <p className="text-sm text-gray-500 mt-1">Действия администраторов</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {activityLogs.map((log) => (
                <div key={log.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className={`p-2 rounded-full ${getActivityColor(log.action)} bg-gray-100`}>
                    {getActivityIcon(log.action)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-800">{log.details}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs text-gray-500">{log.username}</span>
                      <span className="text-xs text-gray-400">•</span>
                      <span className="text-xs text-gray-500">
                        {new Date(log.timestamp).toLocaleString('ru-RU')}
                      </span>
                      <span className="text-xs text-gray-400">•</span>
                      <span className="text-xs text-gray-500">
                        IP: {log.ipAddress}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200">
              <button className="text-sm text-blue-600 hover:text-blue-800 transition-colors">
                Показать все логи →
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Placeholder */}
      <div className="bg-white rounded-lg shadow border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center" id="charts-heading">
          <BarChart3 className="w-5 h-5 text-blue-500 mr-2" />
          Графики и диаграммы
        </h3>
        <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg p-8 text-center border border-gray-200" role="region" aria-labelledby="charts-heading">
          <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" aria-hidden="true" />
          <p className="text-gray-600 mb-2">Интерактивные графики</p>
          <p className="text-sm text-gray-500">
            Здесь будут отображаться детальные графики посещаемости, конверсии и других метрик
          </p>
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="bg-white p-3 rounded border">
              <div className="font-medium text-gray-800">Посещаемость</div>
              <div className="text-gray-500">График по дням</div>
            </div>
            <div className="bg-white p-3 rounded border">
              <div className="font-medium text-gray-800">Конверсия</div>
              <div className="text-gray-500">Воронка пользователей</div>
            </div>
            <div className="bg-white p-3 rounded border">
              <div className="font-medium text-gray-800">Активность</div>
              <div className="text-gray-500">Действия админов</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;